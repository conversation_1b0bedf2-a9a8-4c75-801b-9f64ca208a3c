import requests
import os
from urllib.parse import urlparse

# List of image URLs (duplicates removed)
image_urls = list(set([
    "https://d3b4nwfy34ee2t.cloudfront.net/assets/images/optima-lock/pdp-1.png",
    "https://d3b4nwfy34ee2t.cloudfront.net/assets/images/optima-lock/pdp-2.png",
    "https://d3b4nwfy34ee2t.cloudfront.net/assets/images/optima-lock/pdp-4-new.png",
    "https://d3b4nwfy34ee2t.cloudfront.net/assets/images/optima-lock/pdp_4_n.png",
    "https://d3b4nwfy34ee2t.cloudfront.net/assets/images/optima-lock/pdp_5-n.png",
    "https://d3b4nwfy34ee2t.cloudfront.net/assets/images/optima-lock/pdp-6.png",
    "https://d29rw3zaldax51.cloudfront.net/assets/images/lock-select/feture-arrow.png",
    "https://d3b4nwfy34ee2t.cloudfront.net/assets/images/optima-lock/college-new.png",
    "https://d3b4nwfy34ee2t.cloudfront.net/assets/images/optima-lock/college-m-new.png",
    "https://d3b4nwfy34ee2t.cloudfront.net/assets/images/optima-lock/whatin-box.jpg",
    "https://d3b4nwfy34ee2t.cloudfront.net/assets/images/optima-lock/whatin-box-m.jpg"
]))

# Create directory to save images
output_dir = "downloaded_images"
os.makedirs(output_dir, exist_ok=True)

# Download images with renamed format: optima_1, optima_2, ...
for idx, url in enumerate(image_urls, start=1):
    try:
        response = requests.get(url)
        response.raise_for_status()

        # Extract file extension
        path = urlparse(url).path
        ext = os.path.splitext(path)[-1]  # e.g. .png, .jpg

        # Create new filename
        filename = f"optima_{idx}{ext}"
        filepath = os.path.join(output_dir, filename)

        # Save file
        with open(filepath, 'wb') as f:
            f.write(response.content)
        print(f"Downloaded: {filepath}")
    except Exception as e:
        print(f"Failed to download {url}: {e}")
