# Generated by Django 3.2.18 on 2024-11-25 11:09

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('promotions', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='promotionusage',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='promotion_usages', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='promotion',
            index=models.Index(fields=['code', 'is_active', 'start_date', 'end_date'], name='promotions__code_2c32bf_idx'),
        ),
        migrations.AddIndex(
            model_name='promotionusage',
            index=models.Index(fields=['user', 'promotion', 'order'], name='promotions__user_id_62268c_idx'),
        ),
        migrations.AddIndex(
            model_name='promotionusage',
            index=models.Index(fields=['created_at'], name='promotions__created_8f3bf6_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='promotionusage',
            unique_together={('promotion', 'order')},
        ),
    ]
