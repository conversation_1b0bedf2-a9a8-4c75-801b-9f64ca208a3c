import json
import os
import re
import asyncio
from playwright.async_api import async_playwright

# Configuration
JSON_FILE_PATH = 'output/json/haier_kitchen_products.json'
OUTPUT_DIR = 'haier_product_screenshots'

def create_folder(folder_path):
    """Create folder if it doesn't exist"""
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
        print(f"Created folder: {folder_path}")

def sanitize_filename(filename):
    """Remove invalid characters from filename"""
    return re.sub(r'[\\/*?:"<>|]', "", filename)

async def main_async():
    # Create base output directory
    create_folder(OUTPUT_DIR)
    
    # Load JSON file
    with open(JSON_FILE_PATH, 'r') as f:
        products = json.load(f)
    
    print(f"Found {len(products)} products in the JSON file")
    
    # Initialize Playwright
    async with async_playwright() as p:
        # Launch browser
        browser = await p.chromium.launch(headless=False)  # Set headless=True for no GUI
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        )
        page = await context.new_page()
        
        try:
            # Process each product
            for i, product in enumerate(products):
                product_name = product.get('name', 'Unknown')
                product_model = product.get('model', 'Unknown')
                
                # Get product URL from images array
                product_urls = product.get('images', [])
                if not product_urls:
                    print(f"No URL found for product: {product_name} ({product_model})")
                    continue
                
                # Process each URL for the product
                for url_index, product_url in enumerate(product_urls):
                    # Skip empty URLs
                    if not product_url:
                        continue
                    
                    try:
                        print(f"Visiting: {product_url}")
                        # Navigate to the product page
                        await page.goto(product_url, wait_until='networkidle')
                        
                        # Create a sanitized filename
                        filename = sanitize_filename(f"{product_model}_{product_name}_page{url_index+1}.png")
                        screenshot_path = os.path.join(OUTPUT_DIR, filename)
                        
                        # Take a full-page screenshot
                        await page.screenshot(path=screenshot_path, full_page=True)
                        print(f"Captured screenshot: {screenshot_path}")
                        
                        # Add a small delay to avoid overwhelming the server
                        await page.wait_for_timeout(2000)
                    
                    except Exception as e:
                        print(f"Error processing {product_url}: {e}")
                
                print(f"Completed {i+1}/{len(products)}: {product_name}")
        
        finally:
            # Always close the browser to free resources
            await browser.close()
            print("Browser closed")

def main():
    # Run the async main function
    asyncio.run(main_async())

if __name__ == "__main__":
    main()
