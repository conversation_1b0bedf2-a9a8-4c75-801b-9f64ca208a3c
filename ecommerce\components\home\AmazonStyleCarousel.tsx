"use client";

import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { cn } from "@/lib/utils";

interface Slide {
  image: string;
  title: string;
  subtitle: string;
  code?: string;
  cta?: string;
  link?: string;
  specs?: string;
  brand?: {
    id?: number;
    name: string;
    image?: string;
    image_url?: string;
  } | string;
}

interface AmazonStyleCarouselProps {
  slides: Slide[];
  autoplayInterval?: number;
  className?: string;
}

export default function AmazonStyleCarousel({
  slides,
  autoplayInterval = 5000,
  className,
}: AmazonStyleCarouselProps) {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  // Reset autoplay timer when slide changes
  useEffect(() => {
    if (isAutoPlaying) {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current);
      }

      autoPlayRef.current = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % slides.length);
      }, autoplayInterval);
    }

    return () => {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current);
      }
    };
  }, [currentSlide, isAutoPlaying, autoplayInterval, slides.length]);

  // Pause autoplay on hover
  const pauseAutoPlay = () => setIsAutoPlaying(false);
  const resumeAutoPlay = () => setIsAutoPlaying(true);

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const goToPrevSlide = () => {
    setCurrentSlide((prev) => (prev === 0 ? slides.length - 1 : prev - 1));
  };

  const goToNextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  // Handle touch events for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      goToNextSlide();
    } else if (isRightSwipe) {
      goToPrevSlide();
    }

    setTouchStart(null);
    setTouchEnd(null);
  };

  return (
    <div
      className={cn(
        "relative overflow-hidden rounded-xl shadow-lg mt-2 bg-gradient-to-r from-theme-header to-theme-header/90",
        className
      )}
      onMouseEnter={pauseAutoPlay}
      onMouseLeave={resumeAutoPlay}
      ref={containerRef}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Background pattern overlay */}
      <div className="absolute inset-0 bg-opacity-10 z-0 overflow-hidden">
        <div className="absolute inset-0 opacity-10 bg-[radial-gradient(#ffffff33_1px,transparent_1px)] bg-[size:20px_20px]"></div>
      </div>

      <div
        className="flex transition-transform duration-700 ease-out h-[300px] xs:h-[350px] sm:h-[400px] md:h-[450px] lg:h-[500px] xl:h-[550px]"
        style={{ transform: `translateX(-${currentSlide * 100}%)` }}
      >
        {slides.map((slide, index) => (
          <div key={index} className="min-w-full relative group cursor-pointer" onClick={() => {
            try {
              window.location.href = slide.link || "/shop";
            } catch (error) {
              console.error("Navigation error:", error);
              window.location.href = "/shop";
            }
          }}>
            {/* Background with gradient */}
            <div className="absolute inset-0 bg-gradient-to-r from-[#8a6f4d]/20 to-[#d9c3a9]/10"></div>

            {/* Product image container - positioned to the right side */}
            <div className="absolute top-0 right-0 bottom-0 w-[60%] md:w-[50%] flex items-center justify-center p-4">
              <div className="relative w-full h-full max-w-[90%] max-h-[90%]">
                <Image
                  src={slide.image || '/home/<USER>'}
                  alt={slide.title || 'Product Image'}
                  fill
                  sizes="(max-width: 640px) 100vw, (max-width: 768px) 100vw, (max-width: 1024px) 100vw, 100vw"
                  className="object-contain object-center"
                  priority={index === 0}
                  onError={(e) => {
                    // Fallback to a placeholder if image fails to load
                    const imgElement = e.currentTarget as HTMLImageElement;
                    if (!imgElement.src.includes('placeholder') && !imgElement.src.includes('image-1.png')) {
                      imgElement.src = '/home/<USER>';
                    }
                  }}
                />

                {/* Brand Logo - positioned at bottom right of product image */}
                {slide.brand && typeof slide.brand !== 'string' && (slide.brand?.image_url || slide.brand?.image) && (
                  <div className="absolute bottom-0 right-0 mb-2 mr-2 sm:mb-4 sm:mr-4 z-20">
                    <div className="flex flex-col items-center">
                      <div className="w-12 h-12 xs:w-14 xs:h-14 sm:w-16 sm:h-16 md:w-20 md:h-20 overflow-hidden rounded-lg border-2 border-white shadow-xl bg-white flex items-center justify-center p-1">
                        <img
                          src={slide.brand?.image_url || `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}${slide.brand?.image}`}
                          alt={`${slide.brand?.name} logo`}
                          className="max-w-full max-h-full object-contain"
                          onError={(e) => {
                            // Hide the image on error
                            const imgElement = e.currentTarget as HTMLImageElement;
                            imgElement.style.display = 'none';
                          }}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Subtle gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-r from-black/50 via-black/30 to-transparent"></div>

            {/* Animated pattern overlay */}
            <div className="absolute inset-0 bg-[url('/patterns/dot-pattern.svg')] bg-repeat opacity-10"></div>

            {/* Content overlay - positioned to the left */}
            <div className="absolute inset-0 flex items-center">
              <div className="text-white p-4 xs:p-6 sm:p-8 md:p-10 lg:p-16 w-[50%] md:w-[45%] lg:w-[40%]">
                {/* Brand name badge - positioned above product title */}
                {slide.brand && (
                  <div className="mb-3 xs:mb-4 md:mb-5">
                    <span className="inline-block bg-theme-accent-primary/90 text-white text-xs xs:text-sm sm:text-base md:text-lg px-3 xs:px-4 py-1 xs:py-2 rounded-md shadow-lg font-medium">
                      {typeof slide.brand === 'string' ? slide.brand : slide.brand?.name || ''}
                    </span>
                  </div>
                )}
                <h2 className="text-xl xs:text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-1 xs:mb-2 md:mb-4
                  [text-shadow:_0_1px_3px_rgb(0_0_0_/_50%)]">
                  {slide.title}
                </h2>
                <p className="text-xs xs:text-sm sm:text-base md:text-lg mb-2 xs:mb-3 md:mb-6 text-white/90
                  [text-shadow:_0_1px_2px_rgb(0_0_0_/_30%)] line-clamp-2">
                  {slide.subtitle}
                </p>
                {slide.code && (
                  <p className="inline-block bg-white/20 backdrop-blur-sm px-2 xs:px-3 py-1 rounded-full text-xs xs:text-sm md:text-base font-medium mb-2 xs:mb-4
                    [text-shadow:_0_1px_1px_rgb(0_0_0_/_20%)]">
                    {slide.code}
                  </p>
                )}
                <div className="mt-2 xs:mt-4">
                  <Link href={slide.link || "/shop"} onClick={(e) => {
                    e.stopPropagation();
                    // Additional error handling can be added here if needed
                  }}>
                    <Button className="bg-[#2ECC71] hover:bg-[#27AE60] text-white font-medium px-4 xs:px-6 py-1 xs:py-2 rounded-full
                      transition-all duration-300 hover:shadow-lg hover:scale-105 text-xs xs:text-sm md:text-base">
                      {slide.cta || "Shop Now"}
                    </Button>
                  </Link>
                </div>
              </div>
            </div>

            {/* Product specifications - positioned at bottom */}
            <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent">
              <div className="text-white text-xs xs:text-sm">
                {slide.specs && (
                  <p className="mb-1 [text-shadow:_0_1px_1px_rgb(0_0_0_/_30%)]">{slide.specs}</p>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Navigation arrows - hidden on smallest screens */}
      <button
        onClick={(e) => {
          e.stopPropagation();
          goToPrevSlide();
        }}
        className="absolute left-2 xs:left-4 top-1/2 -translate-y-1/2 w-8 h-8 xs:w-10 xs:h-10 items-center justify-center
          bg-theme-accent-primary hover:bg-theme-accent-hover text-white rounded-full shadow-md z-10 transition-all duration-300
          hover:shadow-lg hover:scale-110 hidden xs:inline-flex"
        aria-label="Previous slide"
      >
        <ChevronLeft className="h-4 w-4 xs:h-6 xs:w-6 text-white transition-transform duration-300 hover:-translate-x-0.5" />
      </button>

      <button
        onClick={(e) => {
          e.stopPropagation();
          goToNextSlide();
        }}
        className="absolute right-2 xs:right-4 top-1/2 -translate-y-1/2 w-8 h-8 xs:w-10 xs:h-10 items-center justify-center
          bg-theme-accent-primary hover:bg-theme-accent-hover text-white rounded-full shadow-md z-10 transition-all duration-300
          hover:shadow-lg hover:scale-110 hidden xs:inline-flex"
        aria-label="Next slide"
      >
        <ChevronRight className="h-4 w-4 xs:h-6 xs:w-6 text-white transition-transform duration-300 hover:translate-x-0.5" />
      </button>

      {/* Indicators */}
      <div className="absolute bottom-2 xs:bottom-4 left-1/2 -translate-x-1/2 flex gap-1 xs:gap-2 z-10">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-2 h-2 xs:w-3 xs:h-3 rounded-full transition-all duration-300 ${
              index === currentSlide
                ? "bg-theme-accent-secondary scale-110"
                : "bg-white/50 hover:bg-white/70 hover:scale-105"
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
}
