#!/usr/bin/env python
"""
Test script to verify the GST rate email fix
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from orders.models import Order, Payment
from orders.utils import send_order_confirmation_email, send_payment_success_email, send_payment_failure_email

def test_gst_rate_in_breakdown():
    """Test that GST rate is included in product breakdown"""
    print("🧪 Testing GST Rate in Product Breakdown...")
    
    try:
        # Get the test order
        order_id = '71af1754-4846-4b52-bad1-f1cea79847ce'
        order = Order.objects.get(id=order_id)
        item = order.items.first()
        
        if not item or not item.product:
            print("❌ No product found in order items")
            return False
            
        print(f"✅ Testing with product: {item.product_name}")
        
        # Test the GST breakdown method
        gst_breakdown = item.product.calculate_gst_breakdown_from_mrp(quantity=item.quantity)
        
        # Check if gst_rate is included
        if 'gst_rate' not in gst_breakdown:
            print("❌ gst_rate key is missing from GST breakdown")
            return False
        
        print(f"✅ gst_rate is included: {gst_breakdown['gst_rate']}%")
        
        # Verify all required keys are present
        required_keys = ['base_price', 'total_gst', 'mrp', 'gst_rate', 'cgst_amount', 'sgst_amount']
        missing_keys = [key for key in required_keys if key not in gst_breakdown]
        
        if missing_keys:
            print(f"❌ Missing required keys: {missing_keys}")
            return False
        
        print(f"✅ All required keys present in GST breakdown")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_email_functions():
    """Test that email functions work without GST rate errors"""
    print("\n📧 Testing Email Functions...")
    
    try:
        # Get the test order
        order_id = '71af1754-4846-4b52-bad1-f1cea79847ce'
        order = Order.objects.get(id=order_id)
        
        # Get or create a payment record
        payment = Payment.objects.filter(order=order).first()
        if not payment:
            payment = Payment.objects.create(
                order=order,
                amount=order.total,
                status="COMPLETED",
                payment_method="PHONEPE",
                transaction_id="test_gst_rate_fix"
            )
        
        print(f"✅ Order: {order.id}")
        print(f"✅ Payment: {payment.status}")
        
        # Test order confirmation email
        print("\n   Testing order confirmation email...")
        try:
            result1 = send_order_confirmation_email(order, payment)
            if result1:
                print("   ✅ Order confirmation email sent successfully")
            else:
                print("   ❌ Order confirmation email failed")
                return False
        except Exception as e:
            print(f"   ❌ Order confirmation email error: {e}")
            return False
        
        # Test payment success email
        print("\n   Testing payment success email...")
        try:
            result2 = send_payment_success_email(order, payment)
            if result2:
                print("   ✅ Payment success email sent successfully")
            else:
                print("   ❌ Payment success email failed")
                return False
        except Exception as e:
            print(f"   ❌ Payment success email error: {e}")
            return False
        
        # Test payment failure email (with failed payment)
        print("\n   Testing payment failure email...")
        try:
            # Create a failed payment for testing
            failed_payment = Payment.objects.create(
                order=order,
                amount=order.total,
                status="FAILED",
                payment_method="PHONEPE",
                transaction_id="test_failed_payment"
            )
            
            result3 = send_payment_failure_email(order, failed_payment)
            if result3:
                print("   ✅ Payment failure email sent successfully")
            else:
                print("   ❌ Payment failure email failed")
                return False
        except Exception as e:
            print(f"   ❌ Payment failure email error: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Email test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting GST Rate Email Fix Verification...\n")
    
    # Test 1: GST Rate in Breakdown
    test1_success = test_gst_rate_in_breakdown()
    
    # Test 2: Email Functions
    test2_success = test_email_functions()
    
    # Summary
    print(f"\n📊 Test Results Summary:")
    print(f"   GST Rate in Breakdown: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"   Email Functions: {'✅ PASS' if test2_success else '❌ FAIL'}")
    
    overall_success = test1_success and test2_success
    
    if overall_success:
        print(f"\n🎉 All tests passed! GST rate email error is FIXED!")
        print(f"\n🔧 What was fixed:")
        print(f"   ✅ Added 'gst_rate' key to calculate_gst_breakdown_from_mrp() method")
        print(f"   ✅ Email templates can now access gst_detail.gst_rate")
        print(f"   ✅ Order confirmation emails work correctly")
        print(f"   ✅ Payment success emails work correctly")
        print(f"   ✅ Payment failure emails work correctly")
        
        print(f"\n📝 The 'gst_rate' KeyError in payment gateway emails is resolved!")
    else:
        print(f"\n💥 Some tests failed. Please check the errors above.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
