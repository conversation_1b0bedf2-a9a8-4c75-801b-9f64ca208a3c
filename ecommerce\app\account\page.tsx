"use client";
import { useEffect, useState } from "react";
// import { useNavigate } from "react-router-dom";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>List,
  TabsTrigger,
} from "../../components/ui/tabs";
import { Button } from "../../components/ui/button";
import { ProfileOverview } from "../../components/account/ProfileOverview";
import { OrderHistory } from "../../components/account/OrderHistory";
import { SavedAddresses } from "../../components/account/SavedAddresses";
import { PaymentMethods } from "../../components/account/PaymentMethods";
import { Wishlist } from "../../components/account/Wishlist";
import { LogOut } from "lucide-react";
import { useToast } from "../../hooks/use-toast";
import { useRouter } from "next/navigation";
import MainHOF from "../../layout/MainHOF";
import useApi from "../../hooks/useApi";
import {
  MAIN_URL,
  USER_DETAIL,
  USER_REFFRESH_BLACKLIST,
} from "../../constant/urls";
import { signOut, useSession } from "next-auth/react";
import AccountLoading from "@/components/ui/loading/AccountLoading";

const Account = () => {
  //   const navigate = useNavigate();
  const { toast } = useToast();
  const router = useRouter();
  const { read, error, loading, data }: any = useApi(MAIN_URL);
  const {
    create,
    error: tokenError,
    loading: tokenLoading,
  }: any = useApi(MAIN_URL);
  const { data: session, status }: any = useSession();
  const handleLogout = async () => {
    signOut({
      callbackUrl: "/auth/login",
    });

    create(USER_REFFRESH_BLACKLIST, { refresh: session?.user?.refresh });
    // In a real app, this would handle the logout logic
    toast({
      title: "Logged out successfully",
      description: "You have been logged out of your account.",
    });
    router.replace("/");
  };

  useEffect(() => {
    if (status === "authenticated") {
      read(USER_DETAIL);
    }
  }, [status]);

  if (loading) {
    return (
      <MainHOF>
        <div className="container mx-auto px-4 py-8">
          <AccountLoading />
        </div>
      </MainHOF>
    );
  }

  return (
    <MainHOF>
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
        <div className="container mx-auto px-4 py-12 max-w-6xl">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-10 gap-4">
            <h1 className="text-4xl font-bold text-gray-800 tracking-tight">My Account</h1>
            <Button
              variant="destructive"
              onClick={handleLogout}
              className="flex items-center gap-2 shadow-sm hover:shadow-md transition-all duration-300 px-5 py-2 rounded-lg"
            >
              <LogOut className="h-4 w-4" />
              Logout
            </Button>
          </div>

          <ProfileOverview {...data?.user} />

          <div className="mt-12">
            <Tabs defaultValue="orders" className="w-full">
              <TabsList className="grid w-full grid-cols-3 lg:w-auto mb-6 rounded-xl bg-gray-100/80 p-1.5">
                <TabsTrigger value="orders" className="rounded-lg text-sm font-medium">Orders</TabsTrigger>
                <TabsTrigger value="addresses" className="rounded-lg text-sm font-medium">Addresses</TabsTrigger>
                <TabsTrigger value="wishlist" className="rounded-lg text-sm font-medium">Wishlist</TabsTrigger>
              </TabsList>
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <TabsContent value="orders" className="mt-0 pt-2">
                  <OrderHistory orders={data?.orders} />
                </TabsContent>
                <TabsContent value="addresses" className="mt-0 pt-2">
                  <SavedAddresses addresses={data?.shipping_address} />
                </TabsContent>
                <TabsContent value="wishlist" className="mt-0 pt-2">
                  <Wishlist wishlist={data?.wishlist} />
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </div>
      </div>
    </MainHOF>
  );
};

export default Account;
