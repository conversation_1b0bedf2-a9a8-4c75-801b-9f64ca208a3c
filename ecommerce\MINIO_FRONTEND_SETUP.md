# MinIO Frontend Integration

This document explains how to update the Next.js frontend to work with Min<PERSON> for media storage.

## Updating Next.js Configuration

To allow Next.js to load images from MinIO, you need to update the `next.config.ts` file to include the MinIO domain in the `remotePatterns` array.

### Step 1: Update next.config.ts

Open the `next.config.ts` file and add the MinIO domain to the `remotePatterns` array:

```typescript
// next.config.ts
import type { NextConfig } from "next";
import { MAIN_URL } from "./constant/urls";

// Configuration for image domains
// ... existing code ...

const nextConfig: NextConfig = {
  // ... existing config ...
  
  images: {
    remotePatterns: [
      // ... existing patterns ...
      
      // Add MinIO pattern
      {
        protocol: "http",
        hostname: "your-minio-domain-or-ip", // Replace with your MinIO domain or IP
        port: "9000",                        // MinIO API port
        pathname: "/media/**",
      },
      
      // For local development with MinIO
      {
        protocol: "http",
        hostname: "localhost",
        port: "9000",
        pathname: "/media/**",
      },
      
      // ... other patterns ...
    ],
  },
};

export default nextConfig;
```

Replace `your-minio-domain-or-ip` with the actual domain or IP address of your MinIO server.

### Step 2: Update Environment Variables

Make sure your frontend environment variables are set correctly to point to the MinIO server:

```
NEXT_PUBLIC_API_URL=http://your-api-domain
NEXT_PUBLIC_MINIO_URL=http://your-minio-domain-or-ip:9000
```

### Step 3: Update Image Components

If you have custom image components, update them to use the MinIO URL when available:

```typescript
// Example component
import Image from "next/image";

interface ProductImageProps {
  src: string;
  alt: string;
  // ... other props
}

export function ProductImage({ src, alt, ...props }: ProductImageProps) {
  // Check if the image URL is already a full URL
  if (src.startsWith("http")) {
    return <Image src={src} alt={alt} {...props} />;
  }
  
  // If it's a relative path, check if it's a media path
  if (src.startsWith("/media/") || src.startsWith("media/")) {
    const cleanPath = src.startsWith("/") ? src.substring(1) : src;
    const minioUrl = process.env.NEXT_PUBLIC_MINIO_URL || "http://localhost:9000";
    return <Image src={`${minioUrl}/${cleanPath}`} alt={alt} {...props} />;
  }
  
  // Otherwise, use the API URL
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || "";
  return <Image src={`${apiUrl}${src}`} alt={alt} {...props} />;
}
```

## Testing the Integration

1. Start your MinIO server (locally or in production)
2. Upload some test images to the MinIO media bucket
3. Start your Next.js application
4. Navigate to a page that displays images
5. Check that the images are loading correctly from MinIO

## Troubleshooting

- **Images Not Loading**: Check the browser console for errors. Make sure the MinIO domain is correctly added to the `remotePatterns` array.
- **CORS Errors**: Configure CORS settings on your MinIO server to allow requests from your frontend domain.
- **404 Errors**: Verify that the image paths are correct and that the images exist in the MinIO bucket.

## Additional Resources

- [Next.js Image Configuration](https://nextjs.org/docs/api-reference/next/image#configuration-options)
- [MinIO CORS Configuration](https://min.io/docs/minio/container/administration/identity-access-management/minio-identity-management-system.html#minio-policy-cors)
