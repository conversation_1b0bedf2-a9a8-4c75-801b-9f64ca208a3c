from django.contrib.auth.models import AbstractUser
from django.db import models
from users.manager import CustomUserManager



class Customer(AbstractUser):
    phone_number = models.CharField(max_length=15, blank=True)
    date_of_birth = models.DateField(null=True, blank=True)
    is_verified = models.BooleanField(default=False)
    email = models.EmailField(unique=True)
    profile_image_url = models.URLField(null=True, blank=True)
    image = models.ImageField(upload_to="profile_images", null=True, blank=True)
    username = None
    first_name = None
    last_name = None
    name = models.CharField(max_length=512)
    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = []
    objects = CustomUserManager()

    def __str__(self):
        return self.email

    @property
    def display_image_url(self):
        if self.image:
            return self.image.url
        return self.profile_image_url

    class Meta:
        ordering = ["-date_joined"]
        indexes = [
            models.Index(fields=['email', 'is_active']),
            models.Index(fields=['name']),
            models.Index(fields=['phone_number']),
            models.Index(fields=['date_joined']),
        ]


class Address(models.Model):
    ADDRESS_TYPES = (
        ("BILLING", "Billing"),
        ("SHIPPING", "Shipping"),
    )

    user = models.ForeignKey(
        Customer, on_delete=models.CASCADE, related_name="addresses"
    )
    address_type = models.CharField(max_length=10, choices=ADDRESS_TYPES)
    street_address = models.CharField(max_length=255)
    apartment = models.CharField(max_length=50, blank=True)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    country = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20)
    order_user_phone = models.CharField(max_length=15, blank=True)
    order_user_email = models.EmailField(blank=True)
    is_default = models.BooleanField(default=False)

    class Meta:
        ordering = ["-is_default"]

    def save(self, *args, **kwargs):
        if self.is_default:
            # Set all other addresses of the same type to non-default
            Address.objects.filter(
                user=self.user, address_type=self.address_type, is_default=True
            ).update(is_default=False)
        super().save(*args, **kwargs)


class PaymentMethod(models.Model):
    user = models.ForeignKey(
        Customer, on_delete=models.CASCADE, related_name="payment_methods"
    )
    card_type = models.CharField(max_length=50)
    last_four = models.CharField(max_length=4)
    stripe_payment_method_id = models.CharField(max_length=100)
    is_default = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["-is_default", "-created_at"]

    def save(self, *args, **kwargs):
        if self.is_default:
            PaymentMethod.objects.filter(user=self.user, is_default=True).update(
                is_default=False
            )
        super().save(*args, **kwargs)


class Wishlist(models.Model):
    user = models.ForeignKey(
        Customer, on_delete=models.CASCADE, related_name="wishlist"
    )
    product = models.ForeignKey("products.Product", on_delete=models.CASCADE)
    added_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ("user", "product")
        ordering = ["-added_at"]


class ContactMessage(models.Model):
    name = models.CharField(max_length=100)
    email = models.EmailField()
    subject = models.CharField(max_length=200)
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} - {self.subject}"
