import csv
import json
import os

# Create output directory if it doesn't exist
os.makedirs('output/json', exist_ok=True)

# API data from our previous fetch (page 1)
page1_data = {
    "data": [
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/76-litres-smart-oven-hco-t10u1.shtml",
            "price": 59990.0,
            "pname": "76 Litres, Built-In Smart Oven",
            "psale": "1",
            "Label": "",
            "metaDataId": 166873,
            "modelno": "HCO-T10U1",
            "appFile": "W020210810739320716237.jpg",
            "labelName": "Smart Home Products",
            "ggdesc": "10 Heating Modes|Precise Temperature Control|3D Heating",
            "channelId": 41478,
            "tags": ["Smart Home Products"]
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/t-shaped-smart-hood-90-cm-hih-t895.shtml",
            "price": 54990.0,
            "pname": "90 cm, T-Shaped-Smart Hood",
            "psale": "1",
            "Label": "",
            "metaDataId": 166074,
            "modelno": "HIH-T895",
            "appFile": "W020210907535116586774.jpg",
            "labelName": "Smart Home Products",
            "ggdesc": "Direct Drive Inverter Motor|Air Curtain 8°Patented Technology|Easy Connectivity",
            "channelId": 41478,
            "tags": ["Smart Home Products"]
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/65-litres-smart-sterilizer-25-litres-dish-warmer-his-x76swa1.shtml",
            "price": 57000.0,
            "pname": "65 Litres Smart Sterilizer with 25 Litres Warm Drawer",
            "psale": "1",
            "Label": "",
            "metaDataId": 166879,
            "modelno": "HIS-X76SWA1",
            "appFile": "W020210810767136451832.jpg",
            "labelName": "Smart Home Products",
            "ggdesc": "Sterilizer Combined With Dish-Warmer|Made for Stainless Steel Utensils|Intelligent Tracking",
            "channelId": 41478,
            "tags": ["Smart Home Products"]
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/hybrid-hob-5-burner-90-cm-hic-905fcf-o.shtml",
            "price": 34990.0,
            "pname": "5 Burner, 90cm Hybrid - Hob",
            "psale": "1",
            "Label": "",
            "metaDataId": 166155,
            "modelno": "HIC-905FCF-O",
            "appFile": "W020210809703075500749.jpg",
            "ggdesc": "8mm Tempered Glass|All Brass Burner|All Burners With FFD",
            "channelId": 41478
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/built-in-smart-hob-3-burner-90-cm-hic-b3cgafh.shtml",
            "price": 52200.0,
            "pname": "3 Burner, 90cm Built-In Smart Hob",
            "psale": "1",
            "Label": "",
            "metaDataId": 166871,
            "modelno": "HIC-B3CGAFH",
            "appFile": "W020210810728361342071.jpg",
            "labelName": "Smart Home Products",
            "ggdesc": "8mm Tempered Glass|All Brass Burner|Easy Connectivity",
            "channelId": 41478,
            "tags": ["Smart Home Products"]
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/t-shaped-smart-hood-90-cm-hih-t2903.shtml",
            "price": 45990.0,
            "pname": "90 cm, T-Shaped- Smart Hood",
            "psale": "1",
            "Label": "",
            "metaDataId": 166078,
            "modelno": "HIH-T2903",
            "appFile": "W020210907537116272092.jpg",
            "labelName": "Smart Home Products",
            "ggdesc": "APP Control|Powerful Suction|W Shaped Double Filter",
            "channelId": 41478,
            "tags": ["Smart Home Products"]
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/hybrid-hob-4-burner-90-cm-hic-904fcf-o.shtml",
            "price": 33990.0,
            "pname": "4 Burner, 90cm Hybrid - Hob",
            "psale": "1",
            "Label": "",
            "metaDataId": 166857,
            "modelno": "HIC-904FCF-O",
            "appFile": "W020210810677861510146.jpg",
            "ggdesc": "8mm Tempered Glass|All Brass Burner|All Burners With FFD",
            "channelId": 41478
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/built-in-oven-59-litres-hio-m59cf.shtml",
            "price": 41490.0,
            "pname": "59 Litres, Built-in Oven",
            "psale": "1",
            "Label": "",
            "metaDataId": 166875,
            "modelno": "HIO-M59CF",
            "appFile": "W020210810754728599548.jpg",
            "ggdesc": "Humanized Design|5 Heating Modes|Uniform Heating",
            "channelId": 41478
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/split-vertical-hood-90-cm-hih-v90hm-p.shtml",
            "price": 31990.0,
            "pname": "90 cm, Split Vertical-Hood",
            "psale": "1",
            "Label": "",
            "metaDataId": 163246,
            "modelno": "HIH-V90HM-P (P1/P2)",
            "appFile": "W020210806499877805384.jpg",
            "ggdesc": "Detachable Chimney & Panel|Heat Auto Clean|Powerful Suction",
            "channelId": 41478
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/hybrid-hob-4-burner-75-cm-hic-754fcf-o.shtml",
            "price": 30790.0,
            "pname": "4 Burner, 75cm Hybrid - Hob",
            "psale": "1",
            "Label": "",
            "metaDataId": 166859,
            "modelno": "HIC-754FCF-O",
            "appFile": "W020210810678972402863.jpg",
            "ggdesc": "8mm Tempered Glass|All Brass Burner|4.5KW (triple ring wok burner with FFD)",
            "channelId": 41478
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/hybrid-hob-4-burner-65-cm-hic-654fcf-o.shtml",
            "price": 25790.0,
            "pname": "4 Burner, 65cm Hybrid - Hob",
            "psale": "1",
            "Label": "",
            "metaDataId": 166861,
            "modelno": "HIC-654FCF-O",
            "appFile": "W020210810691460679433.jpg",
            "ggdesc": "8mm Tempered Glass|All Brass Burner|4.5 KW (triple ring wok burner with FFD)",
            "channelId": 41478
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/t-shaped-hood-90-cm-hih-t90hm-v.shtml",
            "price": 24990.0,
            "pname": "90 cm, T-Shaped - Hood",
            "psale": "1",
            "Label": "",
            "metaDataId": 166081,
            "modelno": "HIH-T90HM-V",
            "appFile": "W020230214490138814320.jpg",
            "ggdesc": "Heat Auto Clean|Gesture Control|Touch Control",
            "channelId": 41478
        }
    ]
}

# API data from our previous fetch (page 2)
page2_data = {
    "data": [
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/vertical-hood-90-cm-hih-v90hm-c.shtml",
            "price": 25990.0,
            "pname": "90cm, Vertical-Hood",
            "psale": "1",
            "Label": "",
            "metaDataId": 164579,
            "modelno": "HIH-V90HM-C",
            "appFile": "W020210806516671225765.jpg",
            "ggdesc": "Heat Auto Clean|Powerful Suction|Gesture Control",
            "channelId": 41478
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/built-in-microwave-28-litres-him-m28sa1.shtml",
            "price": 35490.0,
            "pname": "28 Litres, Built-In Microwave",
            "psale": "1",
            "Label": "",
            "metaDataId": 166877,
            "modelno": "HIM-M28SA1",
            "appFile": "W020210810760946528514.jpg",
            "ggdesc": "Built-in Capable Microwave|Microcomputer Control|28L Capacity",
            "channelId": 41478
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/tablet-hob-3-burner-76-cm-hic-763cf-s.shtml",
            "price": 17990.0,
            "pname": "3 Burner, 76cm Tablet - Hob",
            "psale": "1",
            "Label": "",
            "metaDataId": 166867,
            "modelno": "HIC-763CF-S",
            "appFile": "W020230214492153138237.jpg",
            "ggdesc": "6mm Tempered Glass|All Brass Burner|Auto  Ignition",
            "channelId": 41478
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/hybrid-hob-3-burner-60-cm-hic-603fcf-o.shtml",
            "price": 22790.0,
            "pname": "3 Burner, 60cm Hybrid - Hob",
            "psale": "1",
            "Label": "",
            "metaDataId": 166865,
            "modelno": "HIC-603FCF-O",
            "appFile": "W020210810714937531515.jpg",
            "ggdesc": "8mm Tempered Glass|All Brass Burner|4.5 KW (triple ring wok burner with FFD)",
            "channelId": 41478
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/hybrid-hob-3-burner-65-cm-hic-653fcf-o.shtml",
            "price": 23790.0,
            "pname": "3 Burner, 65cm Hybrid - Hob",
            "psale": "1",
            "Label": "",
            "metaDataId": 166863,
            "modelno": "HIC-653FCF-O",
            "appFile": "W020210810702360014869.jpg",
            "ggdesc": "8mm Tempered Glass|All Brass Burner|4.5 KW (triple ring wok burner with FFD)",
            "channelId": 41478
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/t-shaped-hood-60-cm-hih-t60hm-v.shtml",
            "price": 21990.0,
            "pname": "60 cm, T-Shaped - Hood",
            "psale": "1",
            "Label": "",
            "metaDataId": 166149,
            "modelno": "HIH-T60HM-V",
            "appFile": "W020230214492753810244.jpg",
            "ggdesc": "Heat Auto Clean|Gesture Control|Touch Control",
            "channelId": 41478
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/tablet-hob-4-burner-65-cm-hic-654cf-s.shtml",
            "price": 18390.0,
            "pname": "4 Burner, 65cm Tablet - Hob",
            "psale": "1",
            "Label": "",
            "metaDataId": 166869,
            "modelno": "HIC-654CF-S",
            "appFile": "W020230214491100446912.jpg",
            "ggdesc": "6mm Tempered Glass|All Brass Burner|Auto Ignition",
            "channelId": 41478
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/vertical-hood-75-cm-hih-v75hm-c.shtml",
            "price": 23990.0,
            "pname": "75 cm, Vertical-Hood",
            "psale": "1",
            "Label": "",
            "metaDataId": 165970,
            "modelno": "HIH-V75HM-C",
            "appFile": "W020210806536055285192.jpg",
            "ggdesc": "Heat Auto Clean|Powerful Suction|Gesture Control",
            "channelId": 41478
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/vertical-hood-60-cm-hih-v60hm-c.shtml",
            "price": 21990.0,
            "pname": "60 cm, Vertical-Hood",
            "psale": "1",
            "Label": "",
            "metaDataId": 165978,
            "modelno": "HIH-V60HM-C",
            "appFile": "W020210809474007908411.jpg",
            "ggdesc": "Heat Auto Clean|Gesture Control|Powerful Suction",
            "channelId": 41478
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/glass-shaped-hood-90-cm-hih-g90hm-g.shtml",
            "price": 20790.0,
            "pname": "90 cm, Glass-Shaped - Hood",
            "psale": "1",
            "Label": "",
            "metaDataId": 166151,
            "modelno": "HIH-G90HM-G",
            "appFile": "W020210809682512771958.jpg",
            "ggdesc": "Heat Auto Clean|Gesture Control|Filterless Technology",
            "channelId": 41478
        },
        {
            "docPubUrl": "https://www.haier.com/in/kitchen-appliance/glass-shaped-hood-60-cm-hih-g60hm-g.shtml",
            "price": 16590.0,
            "pname": "60 cm, Glass-Shaped - Hood",
            "psale": "1",
            "Label": "",
            "metaDataId": 166153,
            "modelno": "HIH-G60HM-G",
            "appFile": "W020210809697949417163.jpg",
            "ggdesc": "Heat Auto Clean|Gesture Control|Filterless Concept",
            "channelId": 41478
        }
    ]
}

# Combine data from both pages
all_api_products = page1_data["data"] + page2_data["data"]

# Read CSV file
def read_csv_file(file_path):
    products = []
    with open(file_path, 'r', encoding='utf-8') as csvfile:
        reader = csv.reader(csvfile)
        # Skip header rows
        next(reader)  # Skip the first row (Haier)
        next(reader)  # Skip the second row (column headers)
        
        for row in reader:
            if len(row) >= 6 and row[2].strip():  # Check if model exists
                product = {
                    "classification": row[0].strip(),
                    "sap_code": row[1].strip(),
                    "model": row[2].strip(),
                    "mrp": row[3].strip().replace(",", ""),
                    "dp": row[4].strip().replace(",", "").replace('"', ''),
                    "mop": row[5].strip().replace(",", "").replace('"', '')
                }
                products.append(product)
    return products

# Main function
def main():
    # Read CSV file
    csv_products = read_csv_file('Haier KA.csv')
    
    # Match CSV products with API products
    matched_products = []
    
    for csv_product in csv_products:
        csv_model = csv_product['model']
        
        # Handle special case for models with variants
        base_model = csv_model.split('-')[0] if '-' in csv_model else csv_model
        if '(' in csv_model:
            base_model = csv_model.split('(')[0].strip()
        
        matched_api_product = None
        
        # Try to find exact match first
        for api_product in all_api_products:
            if api_product['modelno'] == csv_model:
                matched_api_product = api_product
                break
        
        # If no exact match, try partial match
        if not matched_api_product:
            for api_product in all_api_products:
                if base_model in api_product['modelno'] or csv_model in api_product['modelno']:
                    matched_api_product = api_product
                    break
                
                # Special case for P1/P2 variants
                if "HIH-V90HM-P" in csv_model and "HIH-V90HM-P (P1/P2)" in api_product['modelno']:
                    matched_api_product = api_product
                    break
        
        if matched_api_product:
            # Extract category from product name
            category = "Kitchen Appliance"
            product_name = matched_api_product['pname']
            
            if "Hood" in product_name:
                category = "Kitchen Appliance - Hood"
            elif "Hob" in product_name:
                category = "Kitchen Appliance - Hob"
            elif "Oven" in product_name:
                category = "Kitchen Appliance - Oven"
            elif "Microwave" in product_name:
                category = "Kitchen Appliance - Microwave"
            elif "Sterilizer" in product_name:
                category = "Kitchen Appliance - Sterilizer"
            
            # Format product data according to specified structure
            product_data = {
                "name": matched_api_product['pname'],
                "description": matched_api_product.get('ggdesc', ''),  # Use ggdesc directly as description
                "price": float(csv_product['mrp']) if csv_product['mrp'] else matched_api_product['price'],
                "brand": "Haier",
                "category": category,
                "images": [matched_api_product['docPubUrl']],  # Use product URL as image placeholder
                "model": csv_product['model']
            }
            
            matched_products.append(product_data)
            print(f"Matched {csv_model} with {matched_api_product['modelno']}")
        else:
            # If no match found, create a basic entry with available information
            print(f"No match found for {csv_model}, creating basic entry")
            
            # Determine category from model number
            category = "Kitchen Appliance"
            if "HIH" in csv_model:
                category = "Kitchen Appliance - Hood"
            elif "HIC" in csv_model:
                category = "Kitchen Appliance - Hob"
            elif "HIO" in csv_model or "HCO" in csv_model:
                category = "Kitchen Appliance - Oven"
            elif "HIM" in csv_model:
                category = "Kitchen Appliance - Microwave"
            elif "HIS" in csv_model:
                category = "Kitchen Appliance - Sterilizer"
            
            # Create basic product data
            product_data = {
                "name": csv_model,
                "description": f"Haier {csv_model}",
                "price": float(csv_product['mrp']) if csv_product['mrp'] else 0,
                "brand": "Haier",
                "category": category,
                "images": [],
                "model": csv_model
            }
            
            matched_products.append(product_data)
    
    # Save the data to a JSON file
    with open('output/json/haier_kitchen_products.json', 'w', encoding='utf-8') as f:
        json.dump(matched_products, f, indent=2, ensure_ascii=False)
    
    print(f"Extracted data for {len(matched_products)} products")
    print(f"Data saved to output/json/haier_kitchen_products.json")

if __name__ == "__main__":
    main()
