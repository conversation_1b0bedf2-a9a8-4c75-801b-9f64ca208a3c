import logging
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from datetime import datetime

logger = logging.getLogger(__name__)

def send_contact_emails(contact_data):
    """
    Send emails to both the sender and receiver after a contact form submission.

    Args:
        contact_data (dict): Dictionary containing contact form data
            - name: Sender's name
            - email: Sender's email
            - subject: Email subject
            - message: Email message

    Returns:
        bool: True if emails were sent successfully, False otherwise
    """
    try:
        # Get current date for the notification email
        current_date = datetime.now().strftime("%B %d, %Y, %I:%M %p")

        # Send confirmation email to the sender
        send_confirmation_email(contact_data)

        # Send notification email to the site admin
        contact_data['date'] = current_date
        send_notification_email(contact_data)

        return True
    except Exception as e:
        logger.error(f"Error sending contact emails: {str(e)}")
        return False

def send_confirmation_email(contact_data):
    """
    Send a confirmation email to the person who submitted the contact form.

    Args:
        contact_data (dict): Dictionary containing contact form data
    """
    subject = "Thank you for contacting Triumph Enterprises"
    from_email = settings.DEFAULT_FROM_EMAIL
    to_email = contact_data['email']

    # Render HTML content
    html_content = render_to_string('emails/contact_confirmation.html', {
        'name': contact_data['name'],
        'subject': contact_data['subject'],
        'message': contact_data['message']
    })

    # Create email message
    email = EmailMultiAlternatives(subject, "", from_email, [to_email])
    email.attach_alternative(html_content, "text/html")

    # Send email
    email.send()

def send_notification_email(contact_data):
    """
    Send a notification email to the site admin about the new contact form submission.

    Args:
        contact_data (dict): Dictionary containing contact form data
    """
    subject = f"New Contact Form Submission: {contact_data['subject']}"
    from_email = settings.DEFAULT_FROM_EMAIL

    # Send to both the default admin email and the Triumph Enterprises email
    to_emails = [settings.DEFAULT_FROM_EMAIL, "<EMAIL>"]

    # Render HTML content
    html_content = render_to_string('emails/contact_notification.html', {
        'name': contact_data['name'],
        'email': contact_data['email'],
        'subject': contact_data['subject'],
        'message': contact_data['message'],
        'date': contact_data['date']
    })

    # Create email message
    email = EmailMultiAlternatives(subject, "", from_email, to_emails)
    email.attach_alternative(html_content, "text/html")

    # Send email
    email.send()
