"use client";
import { useEffect, useMemo, useState } from "react";
import { CartItemList } from "../../components/cart/CartItemList";
import { OrderSummary } from "../../components/cart/OrderSummary";
// import { PromoCodeInput } from "../../components/cart/PromoCodeInput";
import { Button } from "../../components/ui/button";
import { ShoppingBag } from "lucide-react";
import Link from "next/link";
import MainHOF from "../../layout/MainHOF";
import useApi from "../../hooks/useApi";
import { MAIN_URL, UPDATE_CART, USER_CART } from "../../constant/urls";
import { useSession } from "next-auth/react";
import { CartLoading } from "@/components/ui/loading/CartLoading";
import useStorage from "@/hooks/useStorage";
import ClientOnly from "@/components/ClientOnly";

const Cart = () => {
  const { read, error }: any = useApi(MAIN_URL);
  const {
    data: cart,
    update,
    loading: cartLoading,
    error: cartError,
  } = useApi(MAIN_URL);
  const [promotion, setPromotion] = useState<any>({});
  const { status }: any = useSession();
  const [data, setData] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const storage = useStorage('local');

  // Client-side only effect to get promotion from localStorage using our custom hook
  useEffect(() => {
    const getPromotionFromStorage = () => {
      try {
        const promo = storage.getItem("promotion") ?? "{}";
        const parsedPromotion = JSON.parse(promo);
        if (parsedPromotion && Object.keys(parsedPromotion).length > 0) {
          setPromotion(parsedPromotion);
        }
      } catch (error) {
        console.error("Error reading promotion from localStorage:", error);
      }
    };

    getPromotionFromStorage();
  }, [storage]);

  const getUserCart = async () => {
    try {
      setLoading(true);
      const response = await read(USER_CART);
      if (Boolean(response.total_items > 0)) {
        setData(response);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (status === "authenticated") {
      getUserCart();
    }
  }, [status]);

  // Use dynamic GST breakdown from cart data if available
  const gstBreakdown = data?.gst_breakdown;

  // Calculate subtotal from GST breakdown (base price) if available, otherwise fallback to MRP calculation
  const subtotalValue = useMemo(() => {
    if (!Array.isArray(data?.items) || data?.items.length === 0) {
      return 0;
    }

    // If we have GST breakdown, use the subtotal (base price) from backend
    if (gstBreakdown?.subtotal) {
      return parseFloat(gstBreakdown.subtotal.toString());
    }

    // Fallback: calculate from MRP (this should be avoided as it's GST-inclusive)
    return data?.items.reduce((total: number, item: any) => {
      const itemPrice = parseFloat(item.product.price);
      return total + itemPrice * item.quantity;
    }, 0);
  }, [data?.items, gstBreakdown]);

  const gstAmount = gstBreakdown?.total_gst_amount || (subtotalValue * 0.18);
  const cgstAmount = gstBreakdown?.total_cgst_amount || (gstAmount / 2);
  const sgstAmount = gstBreakdown?.total_sgst_amount || (gstAmount / 2);
  const igstAmount = gstBreakdown?.total_igst_amount || 0;

  // Format subtotal for display but keep the numeric value for calculations
  const subtotal = subtotalValue.toFixed(2);

  // Calculate shipping cost based on numeric subtotal value
  const shippingCost = subtotalValue > 500 ? 0 : 29.99;

  // Calculate total properly with numeric values including GST
  // Use total_mrp from GST breakdown if available (this is the correct GST-inclusive total)
  let totalValue = gstBreakdown?.total_mrp
    ? parseFloat(gstBreakdown.total_mrp.toString()) + shippingCost
    : subtotalValue + gstAmount + shippingCost;

  if (Boolean(promotion?.discount)) {
    totalValue = totalValue - Number(promotion?.discount);
  }

  // Ensure total is never negative
  totalValue = Math.max(0, totalValue);

  const handleAddOrRemoveToCart = async (itemId: number, action: string) => {
    try {
      const res = await update(UPDATE_CART, {
        item_id: itemId,
        action,
      });

      if (Boolean(res)) {
        setData((prev: any) => ({
          ...prev,
          items:
            action === "delete"
              ? prev.items.filter((item: any) => item.id !== itemId)
              : prev.items.map((item: any) =>
                  item.id === itemId
                    ? {
                        ...item,
                        quantity:
                          action === "add"
                            ? item.quantity + 1
                            : item.quantity - 1,
                      }
                    : item
                ),
        }));
      }
    } catch (error) {
      console.error("Failed to update cart:", error);
    }
  };

  if (loading) {
    return (
      <MainHOF>
        <div className="container mx-auto px-4 py-8">
          <CartLoading />
        </div>
      </MainHOF>
    );
  }

  return (
    <MainHOF>
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">Your Shopping Cart</h1>
          <div className="text-sm text-gray-500">
            {data?.items?.length > 0 && `${data.items.length} item${data.items.length > 1 ? 's' : ''} in your cart`}
          </div>
        </div>

        {Boolean(data?.items?.length) ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-8">
              <div className="bg-white rounded-lg shadow-sm p-6 transition-all duration-300">
                <CartItemList
                  handleAddOrRemoveToCart={handleAddOrRemoveToCart}
                  items={data?.items ?? []}
                />
              </div>

              {/* <div className="bg-white rounded-lg shadow-sm p-6 transition-all duration-300">
                <PromoCodeInput
                  setPromotion={setPromotion}
                  promotion={promotion}
                />
              </div> */}

              <div className="flex justify-between items-center">
                <Link href="/shop">
                  <Button variant="outline" className="flex items-center gap-2 px-6 py-5 transition-all duration-300 hover:bg-gray-100">
                    <ShoppingBag className="h-4 w-4" />
                    Continue Shopping
                  </Button>
                </Link>

                <div className="text-sm text-gray-500">
                  Prices are inclusive of all taxes
                </div>
              </div>
            </div>

            <div>
              <OrderSummary
                subtotal={subtotalValue}
                shippingCost={shippingCost ?? 0}
                discount={promotion?.discount ?? 0}
                total={totalValue}
                gstAmount={gstAmount}
                cgstAmount={cgstAmount}
                sgstAmount={sgstAmount}
                igstAmount={igstAmount}
                showGstBreakdown={true}
                gstBreakdown={gstBreakdown}
              />
            </div>
          </div>
        ) : (
          <div className="text-center py-16 bg-white rounded-lg shadow-sm p-12 max-w-2xl mx-auto">
            <div className="flex justify-center mb-6">
              <ShoppingBag className="h-16 w-16 text-gray-300" />
            </div>
            <h2 className="text-2xl font-semibold mb-4">Your cart is empty</h2>
            <p className="text-gray-500 mb-8">Looks like you haven't added anything to your cart yet.</p>
            <Link href="/shop">
              <Button className="px-8 py-6 text-base">
                <ShoppingBag className="mr-2 h-5 w-5" />
                Start Shopping
              </Button>
            </Link>
          </div>
        )}
      </div>
    </MainHOF>
  );
};

export default Cart;
