#!/usr/bin/env python3
"""
Script to remove duplicate URLs from urls_haier.txt file.
Preserves the original format with quotes and handles trailing commas.
"""

import re
from collections import OrderedDict


def clean_url_line(line):
    """
    Clean a URL line by removing quotes and trailing commas.
    Returns the clean URL.
    """
    # Remove leading/trailing whitespace
    line = line.strip()
    
    # Remove quotes and trailing comma
    line = line.strip('"').rstrip(',').strip('"')
    
    return line


def format_url_line(url, is_last=False):
    """
    Format a URL back to the original format with quotes.
    Optionally add comma if not the last line.
    """
    if is_last:
        return f'"{url}"'
    else:
        return f'"{url}",'


def remove_duplicates(input_file, output_file=None):
    """
    Remove duplicate URLs from the input file.
    If output_file is None, overwrites the input file.
    """
    if output_file is None:
        output_file = input_file
    
    # Read all lines
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"Error: File '{input_file}' not found.")
        return False
    except Exception as e:
        print(f"Error reading file: {e}")
        return False
    
    # Extract unique URLs while preserving order
    unique_urls = OrderedDict()
    
    for line_num, line in enumerate(lines, 1):
        if line.strip():  # Skip empty lines
            try:
                clean_url = clean_url_line(line)
                if clean_url:  # Only add non-empty URLs
                    unique_urls[clean_url] = line_num
            except Exception as e:
                print(f"Warning: Could not process line {line_num}: {line.strip()}")
                continue
    
    # Write unique URLs back to file
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            url_list = list(unique_urls.keys())
            for i, url in enumerate(url_list):
                is_last = (i == len(url_list) - 1)
                formatted_line = format_url_line(url, is_last)
                f.write(formatted_line + '\n')
        
        print(f"Successfully processed {input_file}")
        print(f"Original lines: {len(lines)}")
        print(f"Unique URLs: {len(unique_urls)}")
        print(f"Duplicates removed: {len(lines) - len(unique_urls)}")
        
        return True
        
    except Exception as e:
        print(f"Error writing to file: {e}")
        return False


def main():
    """Main function to run the duplicate removal."""
    input_file = "urls_haier.txt"
    
    print(f"Removing duplicates from {input_file}...")
    
    # Create backup first
    backup_file = f"{input_file}.backup"
    try:
        with open(input_file, 'r', encoding='utf-8') as src:
            with open(backup_file, 'w', encoding='utf-8') as dst:
                dst.write(src.read())
        print(f"Backup created: {backup_file}")
    except Exception as e:
        print(f"Warning: Could not create backup: {e}")
    
    # Remove duplicates
    success = remove_duplicates(input_file)
    
    if success:
        print(f"\nDuplicates removed successfully!")
        print(f"Original file backed up as: {backup_file}")
    else:
        print("Failed to remove duplicates.")


if __name__ == "__main__":
    main()
