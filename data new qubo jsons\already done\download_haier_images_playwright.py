import json
import os
import time
import re
import asyncio
from playwright.async_api import async_playwright
from PIL import Image
from io import BytesIO
import base64

# Configuration
JSON_FILE_PATH = 'output/json/haier_kitchen_products.json'
OUTPUT_DIR = 'haier_product_images_playwright'
WAIT_TIME = 10000  # milliseconds to wait for page elements to load

def create_folder(folder_path):
    """Create folder if it doesn't exist"""
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
        print(f"Created folder: {folder_path}")

def sanitize_filename(filename):
    """Remove invalid characters from filename"""
    return re.sub(r'[\\/*?:"<>|]', "", filename)

async def capture_element_screenshot(page, element, save_path):
    """Capture a screenshot of a specific element and save it."""
    try:
        # Scroll element into view
        await element.scroll_into_view_if_needed()
        await page.wait_for_timeout(500)  # Allow time for any animations to complete
        
        # Capture screenshot of the element
        screenshot_bytes = await element.screenshot()
        
        # Save the image
        with open(save_path, 'wb') as f:
            f.write(screenshot_bytes)
        
        print(f"Saved screenshot: {save_path}")
        return True
    except Exception as e:
        print(f"Error capturing screenshot: {e}")
        return False

async def process_product_page(page, product_url, product_model, product_name, product_dir):
    """Visit product page and capture images using Playwright."""
    try:
        print(f"Visiting: {product_url}")
        await page.goto(product_url, wait_until='networkidle')
        
        # Look for image elements - adjust selectors based on the actual website structure
        image_elements = []
        
        # Try different possible image containers
        # Product gallery images
        gallery_images = await page.query_selector_all('.product-gallery img, .product-images img, .gallery img')
        image_elements.extend(gallery_images)
        
        # Main product image
        main_image = await page.query_selector_all('.product-image img, .main-image img, .product-img img')
        image_elements.extend(main_image)
        
        # Banner images that might contain product
        banner_images = await page.query_selector_all('.product-banner img')
        image_elements.extend(banner_images)
            
        # If still no images found, try a more generic approach
        if not image_elements:
            # Look for any img with src containing the product model or with alt text containing product name
            all_images = await page.query_selector_all('img')
            for img in all_images:
                src = await img.get_attribute('src') or ''
                alt = await img.get_attribute('alt') or ''
                if product_model.lower() in src.lower() or product_model.lower() in alt.lower() or \
                   product_name.lower() in alt.lower():
                    image_elements.append(img)
        
        # If still no images, get all images from the page that are reasonably sized
        if not image_elements:
            all_images = await page.query_selector_all('img')
            for img in all_images:
                width = await img.get_attribute('width')
                height = await img.get_attribute('height')
                if width and height and int(width) > 100 and int(height) > 100:
                    image_elements.append(img)
        
        # Capture screenshots of each image element
        successful_captures = 0
        for i, img in enumerate(image_elements):
            filename = f"{product_model}_{i+1}.png"
            save_path = os.path.join(product_dir, filename)
            
            if await capture_element_screenshot(page, img, save_path):
                successful_captures += 1
        
        print(f"Captured {successful_captures} images for {product_name} ({product_model})")
        return successful_captures
    
    except Exception as e:
        print(f"Error processing {product_url}: {e}")
        return 0

async def main_async():
    # Create base output directory
    create_folder(OUTPUT_DIR)
    
    # Load JSON file
    with open(JSON_FILE_PATH, 'r') as f:
        products = json.load(f)
    
    print(f"Found {len(products)} products in the JSON file")
    
    # Initialize Playwright
    async with async_playwright() as p:
        # Launch browser
        browser = await p.chromium.launch(headless=False)  # Set headless=True for no GUI
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        )
        page = await context.new_page()
        
        try:
            # Process each product
            for i, product in enumerate(products):
                product_name = product.get('name', 'Unknown')
                product_model = product.get('model', 'Unknown')
                
                # Create sanitized folder name
                folder_name = sanitize_filename(f"{product_model}_{product_name}")
                product_dir = os.path.join(OUTPUT_DIR, folder_name)
                create_folder(product_dir)
                
                # Get product URL from images array
                product_urls = product.get('images', [])
                if not product_urls:
                    print(f"No URL found for product: {product_name} ({product_model})")
                    continue
                
                # Process each URL for the product
                for product_url in product_urls:
                    # Skip empty URLs
                    if not product_url:
                        continue
                        
                    # Process the product page and capture images
                    await process_product_page(page, product_url, product_model, product_name, product_dir)
                    
                    # Add a small delay to avoid overwhelming the server
                    await page.wait_for_timeout(2000)
                
                print(f"Completed {i+1}/{len(products)}: {product_name}")
        
        finally:
            # Always close the browser to free resources
            await browser.close()
            print("Browser closed")

def main():
    # Run the async main function
    asyncio.run(main_async())

if __name__ == "__main__":
    main()
