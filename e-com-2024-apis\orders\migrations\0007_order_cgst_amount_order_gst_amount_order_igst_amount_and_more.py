# Generated by Django 5.0.2 on 2025-05-27 11:45

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0006_order_orders_orde_user_id_02a211_idx_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='cgst_amount',
            field=models.DecimalField(decimal_places=2, default=0, help_text='CGST amount', max_digits=10),
        ),
        migrations.AddField(
            model_name='order',
            name='gst_amount',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Total GST amount', max_digits=10),
        ),
        migrations.AddField(
            model_name='order',
            name='igst_amount',
            field=models.DecimalField(decimal_places=2, default=0, help_text='IGST amount (for inter-state)', max_digits=10),
        ),
        migrations.AddField(
            model_name='order',
            name='sgst_amount',
            field=models.DecimalField(decimal_places=2, default=0, help_text='SGST amount', max_digits=10),
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(help_text='Format: INV-YYYY-MM-XXXXXX', max_length=50, unique=True)),
                ('generated_at', models.DateTimeField(auto_now_add=True)),
                ('pdf_file', models.FileField(blank=True, null=True, upload_to='invoices/')),
                ('company_name', models.CharField(default='Triumph Enterprises', max_length=200)),
                ('company_address', models.TextField(default='D.No. 5-5-190/65A, Ehata Nooruddin Shah Qadri, Patel Nagar, Darussalam, Hyderabad, Telangana 500001, India')),
                ('company_email', models.EmailField(default='<EMAIL>', max_length=254)),
                ('company_phone', models.CharField(default='+91 9848486452', max_length=20)),
                ('gst_number', models.CharField(blank=True, help_text='Company GST registration number', max_length=20)),
                ('order', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='invoice', to='orders.order')),
            ],
            options={
                'ordering': ['-generated_at'],
                'indexes': [models.Index(fields=['order'], name='orders_invo_order_i_e9463d_idx'), models.Index(fields=['invoice_number'], name='orders_invo_invoice_4a2c44_idx'), models.Index(fields=['generated_at'], name='orders_invo_generat_6f7055_idx')],
            },
        ),
    ]
