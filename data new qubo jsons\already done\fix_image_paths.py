#!/usr/bin/env python3
"""
Fix image paths in the Haier products JSON file
Converts paths to proper format and validates they exist
"""

import json
import os
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PathFixer:
    def __init__(self, json_file: str = "haier_products_updated_with_images.json"):
        self.json_file = Path(json_file)
        self.products = []
        
    def load_products(self) -> None:
        """Load products from JSON file"""
        try:
            with open(self.json_file, 'r', encoding='utf-8') as f:
                self.products = json.load(f)
            logger.info(f"Loaded {len(self.products)} products from {self.json_file}")
        except Exception as e:
            logger.error(f"Error loading products: {e}")
            self.products = []
    
    def fix_paths(self, path_style: str = "forward") -> None:
        """Fix image paths in products
        
        Args:
            path_style: 'forward' for /, 'backward' for \, 'relative' for ./
        """
        fixed_count = 0
        missing_files = []
        
        for product in self.products:
            if 'images' in product:
                fixed_images = []
                
                for image_path in product['images']:
                    # Convert to Path object for proper handling
                    original_path = Path(image_path)
                    
                    # Check if file exists
                    if original_path.exists():
                        # Fix path based on style preference
                        if path_style == "backward":
                            # Convert to Windows-style backslashes
                            fixed_path = str(original_path).replace('/', '\\')
                        elif path_style == "relative":
                            # Make relative with ./
                            fixed_path = "./" + str(original_path).replace('\\', '/')
                        else:  # forward (default)
                            # Ensure forward slashes (cross-platform)
                            fixed_path = str(original_path).replace('\\', '/')
                        
                        fixed_images.append(fixed_path)
                        fixed_count += 1
                    else:
                        logger.warning(f"Missing file: {image_path}")
                        missing_files.append(image_path)
                
                # Update product with fixed paths
                product['images'] = fixed_images
                
                if not fixed_images:
                    logger.warning(f"Product '{product['name']}' has no valid images")
        
        logger.info(f"Fixed {fixed_count} image paths")
        if missing_files:
            logger.warning(f"Found {len(missing_files)} missing files")
            
    def remove_products_without_images(self) -> None:
        """Remove products that have no valid images"""
        original_count = len(self.products)
        self.products = [p for p in self.products if p.get('images')]
        removed_count = original_count - len(self.products)
        
        if removed_count > 0:
            logger.info(f"Removed {removed_count} products without images")
    
    def validate_all_paths(self) -> dict:
        """Validate all image paths exist"""
        validation_results = {
            'total_images': 0,
            'valid_images': 0,
            'missing_images': 0,
            'missing_files': []
        }
        
        for product in self.products:
            for image_path in product.get('images', []):
                validation_results['total_images'] += 1
                
                if Path(image_path).exists():
                    validation_results['valid_images'] += 1
                else:
                    validation_results['missing_images'] += 1
                    validation_results['missing_files'].append(image_path)
        
        return validation_results
    
    def save_fixed_json(self, output_file: str = None) -> None:
        """Save products with fixed paths"""
        if output_file is None:
            output_file = "haier_products_fixed_paths.json"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.products, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved {len(self.products)} products to {output_file}")
        except Exception as e:
            logger.error(f"Error saving fixed JSON: {e}")
    
    def print_summary(self) -> None:
        """Print summary of products and images"""
        total_products = len(self.products)
        total_images = sum(len(p.get('images', [])) for p in self.products)
        
        print("\n" + "="*60)
        print("HAIER PRODUCTS SUMMARY")
        print("="*60)
        print(f"Total products: {total_products}")
        print(f"Total images: {total_images}")
        
        if total_products > 0:
            avg_images = total_images / total_products
            print(f"Average images per product: {avg_images:.1f}")
        
        # Group by category
        categories = {}
        for product in self.products:
            category = product.get('category', 'Unknown')
            if category not in categories:
                categories[category] = {'count': 0, 'images': 0}
            categories[category]['count'] += 1
            categories[category]['images'] += len(product.get('images', []))
        
        print("\nProducts by category:")
        for category, data in sorted(categories.items()):
            print(f"  {category}: {data['count']} products, {data['images']} images")
        
        print("="*60)

def main():
    """Main function with user interaction"""
    print("🔧 Haier Products Image Path Fixer")
    print("="*50)
    
    fixer = PathFixer()
    fixer.load_products()
    
    if not fixer.products:
        print("No products loaded. Exiting.")
        return
    
    # Validate current paths
    print("\n📋 Validating current image paths...")
    validation = fixer.validate_all_paths()
    print(f"Total images: {validation['total_images']}")
    print(f"Valid images: {validation['valid_images']}")
    print(f"Missing images: {validation['missing_images']}")
    
    if validation['missing_images'] > 0:
        print(f"\n⚠️  Found {validation['missing_images']} missing image files!")
        print("Missing files:")
        for missing_file in validation['missing_files'][:10]:  # Show first 10
            print(f"  - {missing_file}")
        if len(validation['missing_files']) > 10:
            print(f"  ... and {len(validation['missing_files']) - 10} more")
    
    # Ask user for path style preference
    print("\n🎯 Choose path format:")
    print("1. Forward slashes (/) - Cross-platform, web-friendly")
    print("2. Backward slashes (\\) - Windows-style")
    print("3. Relative paths (./) - Relative to current directory")
    
    choice = input("\nEnter choice (1-3) [default: 1]: ").strip()
    
    path_styles = {
        '1': 'forward',
        '2': 'backward', 
        '3': 'relative',
        '': 'forward'  # default
    }
    
    path_style = path_styles.get(choice, 'forward')
    
    # Fix paths
    print(f"\n🔧 Fixing paths with {path_style} style...")
    fixer.fix_paths(path_style)
    
    # Ask about removing products without images
    if validation['missing_images'] > 0:
        remove_empty = input("\n❓ Remove products without valid images? (y/N): ").strip().lower()
        if remove_empty in ['y', 'yes']:
            fixer.remove_products_without_images()
    
    # Save fixed JSON
    fixer.save_fixed_json()
    
    # Print final summary
    fixer.print_summary()
    
    print("\n✅ Path fixing completed!")
    print("Fixed JSON saved as: haier_products_fixed_paths.json")

if __name__ == "__main__":
    main()
