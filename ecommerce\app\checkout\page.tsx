"use client";
import { useEffect, useState } from "react";
import { Progress } from "../../components/ui/progress";
import DeliveryForm from "../../components/checkout/DeliveryForm";
import { ShippingOptions } from "../../components/checkout/ShippingOptions";
import { PaymentForm } from "../../components/checkout/PaymentForm";
import { OrderReview } from "../../components/checkout/OrderReview";
import { Button } from "../../components/ui/button";
import { useToast } from "../../components/ui/use-toast";
import { useRouter } from "next/navigation";
import MainHOF from "../../layout/MainHOF";
import { DeliveryInfo } from "../../types/delivery.order";
import useApi from "@/hooks/useApi";
import { MAIN_URL, ORDERS, USER_CART, PAYMENTS_PHONEPE_INITIATE } from "@/constant/urls";
import SpinnerLoader from "@/components/ui/loading/SpinnerLoader";
import { useSession } from "next-auth/react";
import { set } from "react-hook-form";
import axios from "axios";
import useStorage from "@/hooks/useStorage";
import ClientOnly from "@/components/ClientOnly";

const steps = ["Delivery", "Shipping", "Payment", "Review"];

const Checkout = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [deliveryInfo, setDeliveryInfo] = useState<DeliveryInfo>({
    address_type: "",
    street_address: "",
    apartment: "",
    city: "",
    state: "",
    country: "",
    postal_code: "",
    is_default: false,
    order_user_phone: "",
    order_user_email: "",
  });
  const [shippingMethod, setShippingMethod] = useState<any>({});
  const [shippingAddress, setShippingAddress] = useState<any>({});
  const [orderDetails, setOrderDetails] = useState<any>({});
  const [items, setItems] = useState<any>([]);
  const [paymentInfo, setPaymentInfo] = useState({
    paymentMethod: "phonepe", // Only PhonePe is supported
  });
  const { toast } = useToast();
  const router = useRouter();
  const { status, data: session } = useSession();
  const { create, loading, error } = useApi(MAIN_URL);
  const { error: cartError, read } = useApi(MAIN_URL);
  const [cartLoading, setCartLoading] = useState(true);
  const [cartData, setCartData] = useState<any>({});
  const [promotion, setPromotion] = useState<any>(undefined);
  const storage = useStorage('local');

  const getUserCart = async () => {
    try {
      const response: any = await read(USER_CART);
      if (Boolean(response.total_items === 0)) {
        router.push("/shop");
      } else {
        setCartData(response);
      }
    } catch (error) {
    } finally {
      setCartLoading(false);
    }
  };

  useEffect(() => {
    if (status === "authenticated") {
      getUserCart();
    }
  }, [status]);

  // Safely get promotion from localStorage using our custom hook
  useEffect(() => {
    const getPromotionFromStorage = () => {
      try {
        const storedPromotion = storage.getItem("promotion");
        if (storedPromotion) {
          setPromotion(JSON.parse(storedPromotion));
        }
      } catch (error) {
        console.error("Error reading promotion from localStorage:", error);
      }
    };

    getPromotionFromStorage();
  }, [storage]);

  // Check if cart meets minimum order value
  useEffect(() => {
    if (cartData?.subtotal && Number(cartData.subtotal) < 150) {
      toast({
        variant: "warning",
        title: "Minimum order value required",
        description: "Minimum order value should be ₹150 to place an order.",
      });
      router.push("/cart");
    }
  }, [cartData]);

  const progress = ((currentStep + 1) / steps.length) * 100;
  const handleNext = (step: number) => {
    if (step < steps.length - 1) {
      setCurrentStep(step + 1);
    }
  };
  const createOrder = async () => {
    setItems(cartData?.items);
    setDeliveryInfo(shippingAddress);

    let total = Number(cartData?.subtotal) + Number(shippingMethod?.price);

    if (promotion) {
      total = total - Number(promotion?.discount);
    }
    const order = {
      subtotal: cartData?.subtotal,
      promo_discount: promotion?.discount,
      shipping_cost: shippingMethod?.price,
      total,
    };
    setOrderDetails(order);
  };
  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handlePlaceOrder = async () => {
    try {
      // Check if cart meets minimum order value
      if (Number(cartData?.subtotal) < 150) {
        toast({
          title: "Minimum order value required",
          description: "Minimum order value should be ₹150 to place an order.",
          variant: "destructive"
        });
        router.push("/cart");
        return;
      }

      let request: any = {
        shipping_address_id: shippingAddress?.id,
        billing_address_id: shippingAddress?.id,
        shipping_method_id: shippingMethod?.id,
        notes: "",
        payment_method: paymentInfo.paymentMethod,
      };

      if (promotion) {
        request["promo_code"] = promotion?.code;
      }

      const res: any = await create(ORDERS, request);

      if (Boolean(res?.id)) {
        if (paymentInfo.paymentMethod === 'phonepe') {
          // Initiate PhonePe payment
          try {
            const paymentRes = await axios.post(`${MAIN_URL}${PAYMENTS_PHONEPE_INITIATE}/${res.id}/`, {}, {
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${session?.user?.access}`
              }
            });

            if (paymentRes.status === 200) {
              const paymentData = paymentRes.data;
              // Redirect to PhonePe payment page
              window.location.href = paymentData.payment_url;
              return;
            } else {
              toast({
                variant: "destructive",
                title: "Payment initiation failed!",
                description: "Please try again later.",
              });
            }
          } catch (paymentError) {
            console.error("Payment error:", paymentError);
            let errorMessage = "Please try again later.";

            if (axios.isAxiosError(paymentError)) {
              console.error("Axios error details:", paymentError.response?.data);
              errorMessage = paymentError.response?.data?.message || errorMessage;
            }

            toast({
              variant: "destructive",
              title: "Payment initiation failed!",
              description: errorMessage,
            });
          }
        } else {
          // Regular flow for other payment methods
          toast({
            variant: "success",
            title: "Order placed successfully!",
            description: "Thank you for your purchase.",
          });
          router.push(`/order-confirmation?order_id=${res?.id}`);
        }
      } else {
        toast({
          variant: "destructive",
          title: "Something went wrong!",
          description: "Please try again later.",
        });
      }
    } catch (error) {
      console.log(error);
      toast({
        variant: "destructive",
        title: "Error placing order",
        description: "Please try again later.",
      });
    } finally {
      // Safely remove promotion from localStorage using our custom hook
      storage.removeItem("promotion");
    }
  };

  const onDeliveryInfoChange = (info: DeliveryInfo) => {};

  if (cartLoading) {
    return (
      <MainHOF>
        <div className="container h-96 flex justify-center items-center mx-auto px-4 py-8">
          <SpinnerLoader />
        </div>
      </MainHOF>
    );
  }

  return (
    <MainHOF>
      <div className="container mx-auto px-4 py-12 bg-gradient-to-b from-white to-gray-50">
        <div className="max-w-3xl mx-auto bg-white p-8 rounded-xl shadow-sm">
          <h1 className="text-3xl font-bold mb-8 text-center gradient-text">Checkout</h1>

          <div className="mb-12 px-4">
            <div className="flex justify-between mb-4 relative">
              {steps.map((step, index) => (
                <div key={step} className="flex flex-col items-center relative z-10">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center mb-2 transition-all duration-300 ${
                      index < currentStep
                        ? "bg-primary text-white shadow-md"
                        : index === currentStep
                        ? "bg-primary text-white shadow-lg ring-4 ring-primary/20"
                        : "bg-secondary text-muted-foreground"
                    }`}
                  >
                    {index < currentStep ? (
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <polyline points="20 6 9 17 4 12"></polyline>
                      </svg>
                    ) : (
                      index + 1
                    )}
                  </div>
                  <span
                    className={`text-sm font-medium transition-all duration-300 ${
                      index <= currentStep
                        ? "text-primary"
                        : "text-muted-foreground"
                    }`}
                  >
                    {step}
                  </span>
                </div>
              ))}

              {/* Connecting line */}
              <div className="absolute top-5 left-0 w-full h-0.5 bg-secondary -z-0">
                <div
                  className="h-full bg-primary transition-all duration-500 ease-in-out"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>
          </div>

          <div className="mt-8">
            {currentStep === 0 && (
              <DeliveryForm
                shippingAddress={shippingAddress}
                setShippingAddress={setShippingAddress}
                onDeliveryInfoChange={onDeliveryInfoChange}
                handleNext={handleNext}
              />
            )}
            {currentStep === 1 && (
              <ShippingOptions
                selectedMethod={shippingMethod}
                onMethodSelect={setShippingMethod}
                handleNext={handleNext}
                handleBack={handleBack}
                createOrder={createOrder}
              />
            )}
            {currentStep === 2 && (
              <PaymentForm
                handleNext={handleNext}
                handleBack={handleBack}
                paymentInfo={paymentInfo}
                onPaymentInfoChange={setPaymentInfo}
              />
            )}
            {currentStep === 3 && (
              <OrderReview
                items={items}
                orderDetails={orderDetails}
                deliveryInfo={deliveryInfo}
                shippingMethod={shippingMethod}
                paymentInfo={paymentInfo}
                handlePlaceOrder={handlePlaceOrder}
              />
            )}
          </div>

          {/* <div className="flex justify-between mt-8">
            <Button
              variant="outline"
              onClick={handleBack}
              disabled={currentStep === 0}
            >
              Back
            </Button>
            {currentStep === steps.length - 1 ? (
              <Button onClick={handlePlaceOrder}>Place Order</Button>
            ) : (
              <Button onClick={handleNext}>Continue</Button>
            )}
          </div> */}
        </div>
      </div>
    </MainHOF>
  );
};

export default Checkout;
