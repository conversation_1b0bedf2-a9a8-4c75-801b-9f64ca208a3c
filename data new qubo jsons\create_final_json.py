import json
import re

def clean_and_organize_products():
    """Create a clean, organized JSON with the best matched products"""
    
    # Load the scraped data
    with open('qubo_smart_home_with_images.json', 'r', encoding='utf-8') as f:
        products = json.load(f)
    
    # Filter and organize products
    final_products = []
    seen_names = set()
    
    for product in products:
        name = product.get('name', '')
        category = product.get('category', '')
        images = product.get('images', [])
        similarity_score = product.get('similarity_score', 0)
        
        # Skip duplicates
        if name in seen_names:
            continue
        seen_names.add(name)
        
        # Only include Smart Home products
        if category != 'Smart Home':
            continue
        
        # Filter out products with no images or very low similarity
        if not images and similarity_score == 0:
            continue
        
        # Clean up the product data
        clean_product = {
            'name': name,
            'category': category,
            'description': product.get('description', '').strip(),
            'images': images[:5] if images else [],  # Limit to 5 images per product
            'image_count': len(images) if images else 0,
            'similarity_score': similarity_score,
            'source_url': product.get('source_url', '')
        }
        
        final_products.append(clean_product)
    
    # Sort by similarity score (highest first) and then by image count
    final_products.sort(key=lambda x: (x['similarity_score'], x['image_count']), reverse=True)
    
    return final_products

def create_summary_stats(products):
    """Create summary statistics"""
    total_products = len(products)
    products_with_images = len([p for p in products if p['image_count'] > 0])
    total_images = sum(p['image_count'] for p in products)
    
    categories = {}
    for product in products:
        cat = product['category']
        if cat not in categories:
            categories[cat] = {'count': 0, 'with_images': 0, 'total_images': 0}
        categories[cat]['count'] += 1
        if product['image_count'] > 0:
            categories[cat]['with_images'] += 1
        categories[cat]['total_images'] += product['image_count']
    
    return {
        'total_products': total_products,
        'products_with_images': products_with_images,
        'total_images': total_images,
        'categories': categories
    }

if __name__ == "__main__":
    # Create the final organized JSON
    final_products = clean_and_organize_products()
    
    # Create summary
    summary = create_summary_stats(final_products)
    
    # Save the final JSON
    output_data = {
        'summary': summary,
        'products': final_products
    }
    
    with open('qubo_smart_home_final.json', 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)
    
    print(f"Created final JSON with {len(final_products)} products")
    print(f"Products with images: {summary['products_with_images']}")
    print(f"Total images collected: {summary['total_images']}")
    
    # Show top 10 products with most images
    print("\nTop 10 products with most images:")
    top_products = sorted(final_products, key=lambda x: x['image_count'], reverse=True)[:10]
    for i, product in enumerate(top_products, 1):
        print(f"{i}. {product['name']}: {product['image_count']} images")
