import os
import json
import time
import requests
import random
import logging
from PIL import Image
from io import BytesIO
from urllib.parse import quote_plus

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("duckduckgo_scraper.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants
MAX_IMAGES_PER_PRODUCT = 5
OUTPUT_DIR = "output/images"
JSON_FILE_PATH = "output/json/product_data.json"

# User agents to rotate
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0"
]

# Create output directory structure
os.makedirs(OUTPUT_DIR, exist_ok=True)

def get_random_user_agent():
    """Return a random user agent from the list."""
    return random.choice(USER_AGENTS)

def download_image(url, product_dir, filename):
    """Download an image from URL and save it to the specified directory."""
    try:
        headers = {
            "User-Agent": get_random_user_agent(),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5"
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            # Verify it's an image
            try:
                img = Image.open(BytesIO(response.content))
                img_path = os.path.join(product_dir, filename)
                img.save(img_path)
                logger.info(f"Downloaded image: {img_path}")
                return img_path
            except Exception as e:
                logger.error(f"Not a valid image: {url}, Error: {str(e)}")
                return None
        else:
            logger.error(f"Failed to download image: {url}, Status code: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error downloading image: {url}, Error: {str(e)}")
        return None

def search_duckduckgo_images(query):
    """Search for images using DuckDuckGo's API."""
    image_urls = []
    
    # Format the query for the URL
    formatted_query = quote_plus(query)
    
    # Create the DuckDuckGo API URL
    url = f"https://duckduckgo.com/"
    
    headers = {
        "User-Agent": get_random_user_agent(),
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5"
    }
    
    try:
        # First request to get the vqd token
        response = requests.get(url, headers=headers, params={"q": formatted_query})
        
        if response.status_code != 200:
            logger.error(f"Failed to get DuckDuckGo token. Status code: {response.status_code}")
            return image_urls
        
        # Extract the vqd token
        vqd_match = response.text.split("vqd='")
        if len(vqd_match) < 2:
            logger.error("Could not find vqd token in DuckDuckGo response")
            return image_urls
        
        vqd = vqd_match[1].split("'")[0]
        
        # Now make the actual image search request
        image_search_url = f"https://duckduckgo.com/i.js"
        params = {
            "q": formatted_query,
            "o": "json",
            "vqd": vqd,
            "f": ",,,",
            "p": "1"
        }
        
        image_response = requests.get(image_search_url, headers=headers, params=params)
        
        if image_response.status_code == 200:
            try:
                data = image_response.json()
                results = data.get("results", [])
                
                for result in results:
                    image_url = result.get("image")
                    if image_url and image_url not in image_urls:
                        image_urls.append(image_url)
                        if len(image_urls) >= MAX_IMAGES_PER_PRODUCT:
                            break
                
                logger.info(f"Found {len(image_urls)} images on DuckDuckGo for query: {query}")
            except Exception as e:
                logger.error(f"Error parsing DuckDuckGo JSON response: {str(e)}")
        else:
            logger.error(f"Failed to search DuckDuckGo Images. Status code: {image_response.status_code}")
    
    except Exception as e:
        logger.error(f"Error searching DuckDuckGo Images: {str(e)}")
    
    return image_urls

def process_product(product):
    """Process a single product to find and download images."""
    # Create a unique search query for the product
    model_number = product.get("model_number", "")
    name = product.get("name", "")
    brand = product.get("brand", "")
    category = product.get("category", "")
    
    # Create search query with all relevant information
    search_query = f"{brand} {name} {model_number} {category}"
    
    # Create directory for product images
    product_dir = os.path.join(OUTPUT_DIR, f"{brand}_{category}_{model_number}")
    os.makedirs(product_dir, exist_ok=True)
    
    logger.info(f"Processing product: {name} (Model: {model_number})")
    
    # Search for images using DuckDuckGo
    image_urls = search_duckduckgo_images(search_query)
    
    # Download images
    image_paths = []
    for i, url in enumerate(image_urls):
        filename = f"{model_number}_{i+1}.jpg"
        img_path = download_image(url, product_dir, filename)
        if img_path:
            # Convert to relative path
            rel_path = os.path.relpath(img_path, os.getcwd())
            image_paths.append(rel_path)
    
    # Update product with image paths
    product["images"] = image_paths
    
    logger.info(f"Completed processing {name} (Model: {model_number}). Found {len(image_paths)} images.")
    
    # Add a small delay to avoid being blocked
    time.sleep(random.uniform(1.0, 3.0))
    
    return product

def main():
    """Main function to process all products."""
    logger.info("Starting DuckDuckGo image scraping process")
    
    # Load product data from JSON file
    try:
        with open(JSON_FILE_PATH, 'r', encoding='utf-8') as f:
            data = json.load(f)
            products = data.get("products", [])
            logger.info(f"Loaded {len(products)} products from {JSON_FILE_PATH}")
    except Exception as e:
        logger.error(f"Error loading product data: {str(e)}")
        return
    
    # Process products one by one
    updated_products = []
    for i, product in enumerate(products):
        try:
            updated_product = process_product(product)
            updated_products.append(updated_product)
            logger.info(f"Completed {i+1}/{len(products)} products")
        except Exception as e:
            logger.error(f"Error processing product: {str(e)}")
            # Add the original product to maintain data integrity
            updated_products.append(product)
    
    # Update the JSON file with new image paths
    try:
        data["products"] = updated_products
        with open(JSON_FILE_PATH, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2)
        logger.info(f"Updated {JSON_FILE_PATH} with image paths")
    except Exception as e:
        logger.error(f"Error updating JSON file: {str(e)}")
        
        # Save a backup in case the main file update fails
        try:
            backup_path = JSON_FILE_PATH.replace(".json", "_updated.json")
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2)
            logger.info(f"Saved backup to {backup_path}")
        except Exception as e:
            logger.error(f"Error saving backup file: {str(e)}")
    
    logger.info("DuckDuckGo image scraping process completed")

if __name__ == "__main__":
    main()
