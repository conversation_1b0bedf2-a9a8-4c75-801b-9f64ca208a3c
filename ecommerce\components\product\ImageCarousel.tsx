import React, { ReactNode, useEffect, useId, useState } from "react";
import { EmblaOptionsType } from "embla-carousel";
import Carousel, { <PERSON><PERSON><PERSON>, SliderContainer, ThumsSlider } from "../ui/carousel";
import Image from "next/image";
import { AnimatePresence, motion, MotionConfig } from "framer-motion";
import useMediaQuery from "@/hooks/use-media-query";
import { XIcon } from "lucide-react";
import { getImageUrl } from "@/utils/imageUtils";

const transition = {
  type: "spring",
  duration: 0.4,
};

interface ImageCarouselProps {
  productImage: any[];
  brand?: {
    id?: number;
    name: string;
  } | string;
}

export default function ImageCarousel({ productImage, brand }: ImageCarouselProps) {
  const OPTIONS: EmblaOptionsType = { loop: true };
  const [isMediaModalOpen, setIsMediaModalOpen] = useState(undefined);
  const uniqueId = useId();

  const isDesktop = useMediaQuery("(min-width:768px)");

  useEffect(() => {
    if (isMediaModalOpen) {
      document.body.classList.add("overflow-hidden");
    } else {
      document.body.classList.remove("overflow-hidden");
    }

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setIsMediaModalOpen(undefined);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isMediaModalOpen]);

  return (
    <MotionConfig transition={transition}>
      <div className="w-full bg-background mx-auto">
        {/* Brand Badge - positioned at the top left of the carousel */}
        {brand && (
          <div className="absolute top-2 left-2 z-10">
            <div className="bg-theme-accent-primary/90 text-white text-xs px-2 py-1 rounded-md shadow-md">
              {typeof brand === 'string' ? brand : brand?.name || ''}
            </div>
          </div>
        )}
        <Carousel options={OPTIONS} className="relative" isAutoPlay={true}>
          <SliderContainer className="gap-2">
            {productImage.map((image: any) => (
              <Slider
                key={image.id}
                className="xl:h-[500px] sm:h-[450px] h-[400px] w-full"
                thumnailSrc={getImageUrl(image?.image)}
              >
                <div className="w-full h-full flex items-center justify-center">
                  <Image
                    onClick={() => {
                      setIsMediaModalOpen(getImageUrl(image?.image));
                    }}
                    src={getImageUrl(image?.image)}
                    width={1400}
                    height={800}
                    alt="image"
                    className="max-h-full max-w-full object-contain rounded-lg"
                    style={{ width: 'auto', height: 'auto' }}
                  />
                </div>
              </Slider>
            ))}
          </SliderContainer>
          <ThumsSlider />
        </Carousel>

        <AnimatePresence initial={false} mode="sync">
          {isMediaModalOpen && (
            <>
              <motion.div
                key={`backdrop-${uniqueId}`}
                className="fixed inset-0 h-full w-full backdrop-blur-sm z-50"
                variants={{ open: { opacity: 1 }, closed: { opacity: 0 } }}
                initial="closed"
                animate="open"
                exit="closed"
                onClick={() => {
                  setIsMediaModalOpen(undefined);
                }}
              />
              <motion.div
                key="dialog"
                className="pointer-events-none fixed inset-0 flex items-center justify-center z-50"
              >
                <motion.div
                  className="pointer-events-auto relative flex flex-col overflow-hidden dark:bg-gray-950 bg-gray-200 border w-[90%] h-[90%] max-w-5xl"
                  layoutId={`dialog-${uniqueId}`}
                  tabIndex={-1}
                  style={{
                    borderRadius: "24px",
                  }}
                >
                  {isMediaModalOpen && (
                    <motion.div
                      layoutId={`dialog-img-${uniqueId}`}
                      className="w-full h-full flex items-center justify-center p-4"
                    >
                      {/* eslint-disable-next-line @next/next/no-img-element */}
                      <img
                        src={isMediaModalOpen}
                        alt=""
                        className="max-h-full max-w-full object-contain"
                        style={{ width: 'auto', height: 'auto' }}
                      />
                    </motion.div>
                  )}

                  <button
                    onClick={() => setIsMediaModalOpen(undefined)}
                    className="absolute right-6 top-6 p-3 text-zinc-50 cursor-pointer dark:bg-gray-900 bg-gray-400 hover:bg-gray-500 rounded-full dark:hover:bg-gray-800"
                    type="button"
                    aria-label="Close dialog"
                  >
                    <XIcon size={24} />
                  </button>
                </motion.div>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </div>
    </MotionConfig>
  );
}