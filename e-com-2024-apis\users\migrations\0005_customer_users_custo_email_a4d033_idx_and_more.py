# Generated by Django 5.0.2 on 2025-05-19 11:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('users', '0004_contactmessage'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['email', 'is_active'], name='users_custo_email_a4d033_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['name'], name='users_custo_name_e9a5fc_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['phone_number'], name='users_custo_phone_n_782531_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['date_joined'], name='users_custo_date_jo_fdd25d_idx'),
        ),
    ]
