#!/usr/bin/env python3
"""
Add missing products that have images but aren't in the JSON
"""

import json
import os
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_available_image_folders():
    """Get all available image folders"""
    images_dir = Path("haier_images")
    if not images_dir.exists():
        return {}
    
    folders = {}
    for folder in images_dir.iterdir():
        if folder.is_dir():
            images = []
            for img_file in folder.iterdir():
                if img_file.is_file() and img_file.suffix.lower() in ['.jpg', '.jpeg', '.png', '.webp']:
                    relative_path = f"haier_images/{folder.name}/{img_file.name}"
                    images.append(relative_path)
            
            if images:
                folders[folder.name] = sorted(images)
    
    return folders

def load_existing_products():
    """Load existing products from JSON"""
    try:
        with open("haier_products_fixed_paths.json", 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error loading products: {e}")
        return []

def get_used_image_folders(products):
    """Get image folders already used in products"""
    used_folders = set()
    for product in products:
        for image_path in product.get('images', []):
            # Extract folder name from path
            parts = image_path.split('/')
            if len(parts) >= 2:
                folder_name = parts[1]  # haier_images/folder_name/image.jpg
                used_folders.add(folder_name)
    return used_folders

def create_product_from_folder(folder_name, images):
    """Create a product entry from folder name and images"""
    # Map folder names to product details
    product_mappings = {
        "65_litres_smart_sterilizer_25_litres_dish_warmer_his_x76swa1": {
            "name": "Haier Smart Sterilizer HIS-X76SWA1",
            "description": "Haier Smart Sterilizer HIS-X76SWA1\n\nProduct Specifications:\n• Capacity: 65 Litres sterilizer + 25 Litres dish warmer\n• Features: Smart sterilization technology\n• Model: HIS-X76SWA1\n\nAdvanced sterilization and dish warming solution for modern kitchens.",
            "price": 45990.0,
            "category": "Sterilizer",
            "subcategory": "Smart Sterilizer"
        },
        "built_in_smart_hob_3_burner_90_cm_hic_b3cgafh": {
            "name": "Haier Built-in Smart Hob HIC-B3CGAFH",
            "description": "Haier Built-in Smart Hob HIC-B3CGAFH\n\nProduct Specifications:\n• Burners: 3 Burner\n• Size: 90 cm\n• Type: Built-in Smart Hob\n• Model: HIC-B3CGAFH\n\nSmart cooking technology with precision control for professional results.",
            "price": 42990.0,
            "category": "Hob",
            "subcategory": "Smart Hob"
        },
        "hybrid_hob_3_burner_60_cm_hic_603fcf_o": {
            "name": "Haier Hybrid Hob HIC-603FCF-O",
            "description": "Haier Hybrid Hob HIC-603FCF-O\n\nProduct Specifications:\n• Burners: 3 Burner\n• Size: 60 cm\n• Type: Hybrid Hob\n• Model: HIC-603FCF-O\n\nCompact hybrid cooking solution with efficient burner design.",
            "price": 21990.0,
            "category": "Hob",
            "subcategory": "Hybrid Hob"
        },
        "t_shaped_smart_hood_90_cm_hih_t895": {
            "name": "Haier T-Shaped Smart Hood HIH-T895",
            "description": "Haier T-Shaped Smart Hood HIH-T895\n\nProduct Specifications:\n• Type: T-Shaped Smart Hood\n• Size: 90 cm\n• Model: HIH-T895\n• Features: Smart ventilation technology\n\nAdvanced smart hood with intelligent ventilation control.",
            "price": 28990.0,
            "category": "Hood",
            "subcategory": "Smart Hood"
        }
    }
    
    if folder_name in product_mappings:
        product_data = product_mappings[folder_name].copy()
    else:
        # Create generic product from folder name
        clean_name = folder_name.replace('_', ' ').title()
        product_data = {
            "name": f"Haier {clean_name}",
            "description": f"Haier {clean_name}\n\nHigh-quality kitchen appliance with advanced features.",
            "price": 25000.0,
            "category": "Kitchen Appliance",
            "subcategory": "Premium Series"
        }
    
    # Add common fields
    product_data.update({
        "stock": 10,
        "is_active": True,
        "brand": "Haier",
        "images": images
    })
    
    return product_data

def main():
    """Main function"""
    print("🔍 Finding missing products with images...")
    
    # Get available image folders
    available_folders = get_available_image_folders()
    logger.info(f"Found {len(available_folders)} image folders")
    
    # Load existing products
    existing_products = load_existing_products()
    logger.info(f"Loaded {len(existing_products)} existing products")
    
    # Get used folders
    used_folders = get_used_image_folders(existing_products)
    logger.info(f"Found {len(used_folders)} folders already in use")
    
    # Find missing folders
    missing_folders = set(available_folders.keys()) - used_folders
    logger.info(f"Found {len(missing_folders)} missing folders")
    
    if missing_folders:
        print(f"\n📋 Missing products with images:")
        for folder in sorted(missing_folders):
            image_count = len(available_folders[folder])
            print(f"  - {folder} ({image_count} images)")
        
        # Ask user if they want to add missing products
        add_missing = input(f"\n❓ Add {len(missing_folders)} missing products? (y/N): ").strip().lower()
        
        if add_missing in ['y', 'yes']:
            # Add missing products
            new_products = []
            for folder_name in sorted(missing_folders):
                images = available_folders[folder_name]
                product = create_product_from_folder(folder_name, images)
                new_products.append(product)
                logger.info(f"Created product for {folder_name}")
            
            # Combine with existing products
            all_products = existing_products + new_products
            
            # Save updated JSON
            output_file = "haier_products_complete.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(all_products, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved {len(all_products)} products to {output_file}")
            
            print(f"\n✅ Added {len(new_products)} missing products!")
            print(f"Total products: {len(all_products)}")
            print(f"Complete JSON saved as: {output_file}")
        else:
            print("No products added.")
    else:
        print("\n✅ All image folders are already represented in the JSON!")
        print("No missing products found.")

if __name__ == "__main__":
    main()
