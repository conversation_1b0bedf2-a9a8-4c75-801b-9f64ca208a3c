#!/usr/bin/env python
"""
Management command to update media paths for product images.

This command will:
1. Find all product images with paths starting with 'media/products/'
2. Update them to remove the 'media/' prefix, leaving just 'products/{image}'
3. Provide options for dry run and reporting

Usage:
    python manage.py update_media_paths [--dry-run] [--verbose] [--export-report]
"""

import os
import csv
import logging
from datetime import datetime
from django.core.management.base import BaseCommand
from django.db import transaction
from products.models import ProductImage, Brand, Category, SubCategorie

# Set up logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)


class Command(BaseCommand):
    help = 'Update media paths for product images to standardize format'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Perform a dry run without actually updating paths'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed information about each image being processed'
        )
        parser.add_argument(
            '--export-report',
            action='store_true',
            help='Export a CSV report of all changes made'
        )
        parser.add_argument(
            '--model',
            type=str,
            choices=['product', 'brand', 'category', 'subcategory', 'all'],
            default='all',
            help='Specify which model to update (product, brand, category, subcategory, or all)'
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        verbose = options['verbose']
        export_report = options['export_report']
        model_type = options['model']

        self.stdout.write(self.style.SUCCESS(f"Starting media path update..."))
        self.stdout.write(self.style.SUCCESS(f"Mode: {'DRY RUN' if dry_run else 'LIVE'}"))

        # Initialize report data if needed
        report_data = []

        # Process different model types based on the option
        stats = {
            'product_images': {'processed': 0, 'updated': 0, 'skipped': 0, 'errors': 0},
            'brands': {'processed': 0, 'updated': 0, 'skipped': 0, 'errors': 0},
            'categories': {'processed': 0, 'updated': 0, 'skipped': 0, 'errors': 0},
            'subcategories': {'processed': 0, 'updated': 0, 'skipped': 0, 'errors': 0},
        }

        # Update product images
        if model_type in ['product', 'all']:
            product_report = self.update_product_images(dry_run, verbose)
            report_data.extend(product_report)
            stats['product_images']['processed'] = len(product_report)
            stats['product_images']['updated'] = sum(1 for item in product_report if item['updated'])
            stats['product_images']['skipped'] = sum(1 for item in product_report if not item['updated'])

        # Update brand images
        if model_type in ['brand', 'all']:
            brand_report = self.update_brand_images(dry_run, verbose)
            report_data.extend(brand_report)
            stats['brands']['processed'] = len(brand_report)
            stats['brands']['updated'] = sum(1 for item in brand_report if item['updated'])
            stats['brands']['skipped'] = sum(1 for item in brand_report if not item['updated'])

        # Update category images
        if model_type in ['category', 'all']:
            category_report = self.update_category_images(dry_run, verbose)
            report_data.extend(category_report)
            stats['categories']['processed'] = len(category_report)
            stats['categories']['updated'] = sum(1 for item in category_report if item['updated'])
            stats['categories']['skipped'] = sum(1 for item in category_report if not item['updated'])

        # Update subcategory images
        if model_type in ['subcategory', 'all']:
            subcategory_report = self.update_subcategory_images(dry_run, verbose)
            report_data.extend(subcategory_report)
            stats['subcategories']['processed'] = len(subcategory_report)
            stats['subcategories']['updated'] = sum(1 for item in subcategory_report if item['updated'])
            stats['subcategories']['skipped'] = sum(1 for item in subcategory_report if not item['updated'])

        # Export report if requested
        if export_report and report_data:
            self.export_report(report_data)

        # Print summary
        self.stdout.write(self.style.SUCCESS("Media path update completed"))
        self.stdout.write(self.style.SUCCESS("Summary:"))

        for model_name, model_stats in stats.items():
            if model_stats['processed'] > 0:
                self.stdout.write(self.style.SUCCESS(
                    f"  {model_name}: {model_stats['processed']} processed, "
                    f"{model_stats['updated']} updated, "
                    f"{model_stats['skipped']} skipped, "
                    f"{model_stats['errors']} errors"
                ))

        if dry_run:
            self.stdout.write(self.style.WARNING(
                "This was a dry run. No changes were made. Run without --dry-run to apply changes."
            ))

    def update_product_images(self, dry_run, verbose):
        """Update product image paths to standardize format"""
        self.stdout.write(self.style.SUCCESS("Processing product images..."))

        # Get all product images
        product_images = ProductImage.objects.all()
        self.stdout.write(self.style.SUCCESS(f"Found {product_images.count()} product images"))

        report_data = []

        # Process each product image
        for img in product_images:
            old_path = img.image.name
            new_path = old_path
            needs_update = False

            # Check if path starts with 'media/products/'
            if old_path.startswith('media/products/'):
                new_path = old_path[6:]  # Remove the 'media/' prefix
                needs_update = True

            # Add to report data
            report_item = {
                'model_type': 'ProductImage',
                'id': img.id,
                'product_id': img.product.id if hasattr(img, 'product') else None,
                'product_name': img.product.name if hasattr(img, 'product') else None,
                'old_path': old_path,
                'new_path': new_path,
                'updated': needs_update
            }
            report_data.append(report_item)

            # Skip if no update needed
            if not needs_update:
                if verbose:
                    self.stdout.write(f"Skipping ProductImage {img.id}: Path already correct")
                continue

            # Log the change
            if verbose:
                self.stdout.write(f"ProductImage {img.id}: {old_path} -> {new_path}")

            # Update the path if not in dry run mode
            if not dry_run:
                try:
                    with transaction.atomic():
                        img.image.name = new_path
                        img.save(update_fields=['image'])
                        self.stdout.write(self.style.SUCCESS(f"Updated ProductImage {img.id}"))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"Error updating ProductImage {img.id}: {str(e)}"))
                    report_item['error'] = str(e)

        return report_data

    def update_brand_images(self, dry_run, verbose):
        """Update brand image paths to standardize format"""
        self.stdout.write(self.style.SUCCESS("Processing brand images..."))

        # Get all brands with images
        brands = Brand.objects.exclude(image='')
        self.stdout.write(self.style.SUCCESS(f"Found {brands.count()} brands with images"))

        report_data = []

        # Process each brand
        for brand in brands:
            old_path = brand.image.name
            new_path = old_path
            needs_update = False

            # Check if path starts with 'media/brands/'
            if old_path.startswith('media/brands/'):
                new_path = old_path[6:]  # Remove the 'media/' prefix
                needs_update = True

            # Add to report data
            report_item = {
                'model_type': 'Brand',
                'id': brand.id,
                'name': brand.name,
                'old_path': old_path,
                'new_path': new_path,
                'updated': needs_update
            }
            report_data.append(report_item)

            # Skip if no update needed
            if not needs_update:
                if verbose:
                    self.stdout.write(f"Skipping Brand {brand.id}: Path already correct")
                continue

            # Log the change
            if verbose:
                self.stdout.write(f"Brand {brand.id}: {old_path} -> {new_path}")

            # Update the path if not in dry run mode
            if not dry_run:
                try:
                    with transaction.atomic():
                        brand.image.name = new_path
                        brand.save(update_fields=['image'])
                        self.stdout.write(self.style.SUCCESS(f"Updated Brand {brand.id}"))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"Error updating Brand {brand.id}: {str(e)}"))
                    report_item['error'] = str(e)

        return report_data

    def update_category_images(self, dry_run, verbose):
        """Update category image paths to standardize format"""
        self.stdout.write(self.style.SUCCESS("Processing category images..."))

        # Get all categories with images
        categories = Category.objects.exclude(image='')
        self.stdout.write(self.style.SUCCESS(f"Found {categories.count()} categories with images"))

        report_data = []

        # Process each category
        for category in categories:
            old_path = category.image.name
            new_path = old_path
            needs_update = False

            # Check if path starts with 'media/categories/'
            if old_path.startswith('media/categories/'):
                new_path = old_path[6:]  # Remove the 'media/' prefix
                needs_update = True

            # Add to report data
            report_item = {
                'model_type': 'Category',
                'id': category.id,
                'name': category.name,
                'old_path': old_path,
                'new_path': new_path,
                'updated': needs_update
            }
            report_data.append(report_item)

            # Skip if no update needed
            if not needs_update:
                if verbose:
                    self.stdout.write(f"Skipping Category {category.id}: Path already correct")
                continue

            # Log the change
            if verbose:
                self.stdout.write(f"Category {category.id}: {old_path} -> {new_path}")

            # Update the path if not in dry run mode
            if not dry_run:
                try:
                    with transaction.atomic():
                        category.image.name = new_path
                        category.save(update_fields=['image'])
                        self.stdout.write(self.style.SUCCESS(f"Updated Category {category.id}"))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"Error updating Category {category.id}: {str(e)}"))
                    report_item['error'] = str(e)

        return report_data

    def update_subcategory_images(self, dry_run, verbose):
        """Update subcategory image paths to standardize format"""
        self.stdout.write(self.style.SUCCESS("Processing subcategory images..."))

        # Get all subcategories with images
        subcategories = SubCategorie.objects.exclude(image='')
        self.stdout.write(self.style.SUCCESS(f"Found {subcategories.count()} subcategories with images"))

        report_data = []

        # Process each subcategory
        for subcategory in subcategories:
            old_path = subcategory.image.name
            new_path = old_path
            needs_update = False

            # Check if path starts with 'media/subcategories/'
            if old_path.startswith('media/subcategories/'):
                new_path = old_path[6:]  # Remove the 'media/' prefix
                needs_update = True

            # Add to report data
            report_item = {
                'model_type': 'SubCategorie',
                'id': subcategory.id,
                'name': subcategory.name,
                'old_path': old_path,
                'new_path': new_path,
                'updated': needs_update
            }
            report_data.append(report_item)

            # Skip if no update needed
            if not needs_update:
                if verbose:
                    self.stdout.write(f"Skipping SubCategorie {subcategory.id}: Path already correct")
                continue

            # Log the change
            if verbose:
                self.stdout.write(f"SubCategorie {subcategory.id}: {old_path} -> {new_path}")

            # Update the path if not in dry run mode
            if not dry_run:
                try:
                    with transaction.atomic():
                        subcategory.image.name = new_path
                        subcategory.save(update_fields=['image'])
                        self.stdout.write(self.style.SUCCESS(f"Updated SubCategorie {subcategory.id}"))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"Error updating SubCategorie {subcategory.id}: {str(e)}"))
                    report_item['error'] = str(e)

        return report_data

    def export_report(self, report_data):
        """Export a CSV report of all changes made"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'media_path_update_report_{timestamp}.csv'

        self.stdout.write(self.style.SUCCESS(f"Exporting report to {filename}..."))

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['model_type', 'id', 'name', 'product_id', 'product_name', 'old_path', 'new_path', 'updated', 'error']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for item in report_data:
                # Ensure all values are properly encoded
                clean_item = {}
                for key, value in item.items():
                    if value is None:
                        clean_item[key] = ''
                    else:
                        clean_item[key] = str(value)
                writer.writerow(clean_item)

        self.stdout.write(self.style.SUCCESS(f"Report exported to {filename}"))
