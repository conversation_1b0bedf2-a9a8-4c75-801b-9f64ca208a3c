from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from .models import Address, PaymentMethod, Wishlist, ContactMessage
from django.contrib.auth import authenticate
from products.serializers import ProductDetailSerializer

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    display_image_url = serializers.SerializerMethodField()
    order_count = serializers.IntegerField(read_only=True)
    total_orders = serializers.IntegerField(read_only=True)
    last_order_date = serializers.DateTimeField(read_only=True)

    class Meta:
        model = User
        fields = (
            "id",
            "email",
            "name",
            "phone_number",
            "date_of_birth",
            "is_verified",
            "display_image_url",
            "order_count",
            "total_orders",
            "last_order_date",
        )
        read_only_fields = (
            "is_verified",
            "order_count",
            "total_orders",
            "last_order_date",
        )

    def get_display_image_url(self, obj):
        return obj.display_image_url


class SocialLoginSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    name = serializers.CharField(required=True)
    image_url = serializers.URLField(required=False, allow_blank=True, allow_null=True)
    provider = serializers.CharField(required=False, default="google")

    def validate(self, attrs):
        # Validate email format
        email = attrs.get('email')
        if not email:
            raise serializers.ValidationError({"email": "Email is required for social login"})

        # Ensure name is provided
        name = attrs.get('name')
        if not name:
            raise serializers.ValidationError({"name": "Name is required for social login"})

        return attrs


class UserRegistrationSerializer(serializers.ModelSerializer):
    password = serializers.CharField(
        write_only=True, required=True, validators=[validate_password]
    )

    class Meta:
        model = User
        fields = (
            "email",
            "password",
            "name",
            "phone_number",
            "date_of_birth",
        )

    def validate(self, attrs):
        return attrs

    def create(self, validated_data):
        user = User.objects.create_user(**validated_data)
        return user


class LoginSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    password = serializers.CharField(write_only=True, required=True)

    def validate(self, data):
        email = data.get("email")
        password = data.get("password")

        # Authenticate user
        user = authenticate(username=email, password=password)
        if user is None:
            raise serializers.ValidationError("Invalid email or password.")

        # Add the user to the validated data
        data["user"] = user
        return data


class AddressSerializer(serializers.ModelSerializer):
    class Meta:
        model = Address
        fields = "__all__"
        read_only_fields = ("user",)

    def create(self, validated_data):
        validated_data["user"] = self.context["request"].user
        return super().create(validated_data)


class PaymentMethodSerializer(serializers.ModelSerializer):
    class Meta:
        model = PaymentMethod
        fields = ("id", "card_type", "last_four", "is_default", "created_at")
        read_only_fields = ("user", "stripe_payment_method_id")


class WishlistSerializer(serializers.ModelSerializer):
    product = ProductDetailSerializer(read_only=True)

    class Meta:
        model = Wishlist
        fields = ["id", "product", "added_at"]
        read_only_fields = ("user",)


class ChangePasswordSerializer(serializers.Serializer):
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True, validators=[validate_password])

    def validate_old_password(self, value):
        user = self.context["request"].user
        if not user.check_password(value):
            raise serializers.ValidationError("Old password is incorrect")
        return value


class CustomerProfileSerializer(serializers.ModelSerializer):
    current_password = serializers.CharField(write_only=True, required=False)
    new_password = serializers.CharField(write_only=True, required=False)
    confirm_password = serializers.CharField(write_only=True, required=False)
    display_image_url = serializers.SerializerMethodField()
    image = serializers.ImageField(write_only=True, required=False)

    class Meta:
        model = User
        fields = [
            "id",
            "name",
            "email",
            "phone_number",
            "date_of_birth",
            "image",
            "display_image_url",
            "current_password",
            "new_password",
            "confirm_password",
        ]
        extra_kwargs = {
            "name": {"required": False},
            "email": {"required": False},
            "phone_number": {"required": False},
            "date_of_birth": {"required": False},
            "image": {"required": False},
        }

    def get_display_image_url(self, obj):
        if obj.image:
            return obj.image.url
        return None

    def validate(self, data):
        # Validate password change
        if "new_password" in data or "confirm_password" in data:
            if not data.get("current_password"):
                raise serializers.ValidationError(
                    {
                        "current_password": "Current password is required to change password."
                    }
                )
            if data["new_password"] != data["confirm_password"]:
                raise serializers.ValidationError(
                    {"confirm_password": "New passwords do not match."}
                )
            validate_password(data["new_password"])
        return data

    def update(self, instance, validated_data):
        # Update profile photo
        instance.image = validated_data.get("image", instance.image)

        # Update profile fields
        instance.name = validated_data.get("name", instance.name)
        instance.email = validated_data.get("email", instance.email)
        instance.phone_number = validated_data.get(
            "phone_number", instance.phone_number
        )
        instance.date_of_birth = validated_data.get(
            "date_of_birth", instance.date_of_birth
        )

        # Handle password change
        current_password = validated_data.get("current_password")
        new_password = validated_data.get("new_password")
        if current_password and new_password:
            if not instance.check_password(current_password):
                raise serializers.ValidationError(
                    {"current_password": "Current password is incorrect."}
                )
            instance.set_password(new_password)

        instance.save()
        return instance


class ContactMessageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContactMessage
        fields = ['id', 'name', 'email', 'subject', 'message', 'created_at']
        read_only_fields = ['created_at']