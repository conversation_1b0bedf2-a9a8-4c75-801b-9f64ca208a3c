# Haier High-Quality Image Scraper

This project contains scripts to scrape high-quality images for Haier kitchen products and update the JSON database with the image paths.

## Features

- **Multiple Scraping Engines**: Choose between Selenium (Chrome WebDriver) or Playwright (Chromium)
- **High-Quality Filtering**: Automatically filters images based on resolution (minimum 800x600)
- **Multiple Sources**: Searches official Haier websites, Google Images, and e-commerce sites
- **Smart Image Detection**: Uses various CSS selectors to find product images
- **Progress Saving**: Automatically saves progress every 5 products
- **Backup Creation**: Creates backups before modifying JSON files
- **Rate Limiting**: Includes delays to avoid overwhelming servers

## Files

- `haier_hq_image_scraper.py` - Selenium-based scraper
- `haier_hq_image_scraper_playwright.py` - Playwright-based scraper
- `run_hq_scraper.py` - Main runner script with menu options
- `test_scraper.py` - Test script to verify functionality
- `scraper_config.json` - Configuration file for customizing scraper behavior
- `requirements.txt` - Required Python packages

## Installation

1. **Install Python packages:**
   ```bash
   pip install -r requirements.txt
   ```

2. **For Playwright (if using <PERSON><PERSON> scraper):**
   ```bash
   pip install playwright
   playwright install
   ```

3. **For Selenium (if using Selenium scraper):**
   - Chrome browser must be installed
   - ChromeDriver will be automatically managed by webdriver-manager

## Usage

### Quick Start

1. **Run the main scraper:**
   ```bash
   python run_hq_scraper.py
   ```

2. **Choose your preferred scraper:**
   - Option 1: Selenium (Chrome WebDriver)
   - Option 2: Playwright (Chromium)

### Test the Setup

Before running the full scraper, test with a single product:
```bash
python test_scraper.py
```

### Direct Usage

**Selenium scraper:**
```bash
python haier_hq_image_scraper.py
```

**Playwright scraper:**
```bash
python haier_hq_image_scraper_playwright.py
```

## Configuration

Edit `scraper_config.json` to customize:

- **Image quality settings**: Minimum resolution, max images per product
- **Search sources**: Add/remove websites to search
- **Timeouts**: Page load and download timeouts
- **Rate limiting**: Delay between requests
- **File paths**: Input JSON and output directories

## Output

The scraper will:

1. **Create a new directory**: `haier_product_images_hq/`
2. **Download high-quality images** for each product in subdirectories
3. **Update the JSON file** with `hq_images` field containing paths to downloaded images
4. **Create backups** of the original JSON file

### Example JSON structure after scraping:

```json
{
  "name": "90 cm, T-Shaped-Smart Hood",
  "model": "HIH-V90HM-P1",
  "price": 30594.0,
  "images": ["existing/image/paths..."],
  "hq_images": [
    "haier_product_images_hq/HIH-V90HM-P1_90 cm, T-Shaped-Smart Hood/hq_image_1.jpg",
    "haier_product_images_hq/HIH-V90HM-P1_90 cm, T-Shaped-Smart Hood/hq_image_2.jpg",
    "haier_product_images_hq/HIH-V90HM-P1_90 cm, T-Shaped-Smart Hood/hq_image_3.jpg"
  ]
}
```

## Search Strategy

The scraper uses multiple strategies to find high-quality images:

1. **Official Haier Websites**: Searches official product pages
2. **Google Images**: Uses Google Image search with size filters
3. **E-commerce Sites**: Searches Amazon, Flipkart, Croma, etc.
4. **Image Quality Filtering**: Downloads and checks image dimensions

## Troubleshooting

### Common Issues

1. **ChromeDriver not found (Selenium)**:
   - The script uses webdriver-manager to auto-download ChromeDriver
   - Ensure Chrome browser is installed

2. **Playwright browsers not installed**:
   ```bash
   playwright install
   ```

3. **Permission errors**:
   - Run with administrator privileges if needed
   - Check file/folder permissions

4. **Network timeouts**:
   - Increase timeout values in `scraper_config.json`
   - Check internet connection

### Performance Tips

- **Use headless mode** (default) for faster scraping
- **Adjust rate limiting** in config if getting blocked
- **Run during off-peak hours** for better success rates
- **Monitor progress** - the script saves every 5 products

## Safety Features

- **Rate limiting** to avoid overwhelming servers
- **Error handling** to continue on individual failures
- **Progress saving** to resume interrupted sessions
- **Backup creation** before modifying files
- **Respectful scraping** with appropriate delays

## Legal Notice

This scraper is for educational and personal use only. Please:
- Respect robots.txt files
- Don't overwhelm servers with requests
- Follow website terms of service
- Use scraped images responsibly

## Support

If you encounter issues:
1. Run `test_scraper.py` to diagnose problems
2. Check the console output for error messages
3. Verify all dependencies are installed
4. Ensure the JSON file path is correct
