#!/usr/bin/env python
"""
Test script to verify GST functionality
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from decimal import Decimal
from products.models import GST, Product, Category, Brand
from orders.models import Order, OrderItem
from orders.gst_service import gst_service
from orders.invoice_service import invoice_service
from django.contrib.auth import get_user_model
from users.models import Address

User = get_user_model()

def test_gst_functionality():
    """Test GST calculation and invoice generation"""
    print("🧪 Testing GST Functionality...")
    
    # 1. Test GST Model
    print("\n1. Testing GST Model...")
    
    # Create or get default GST
    default_gst = GST.get_default_gst()
    print(f"✅ Default GST created: {default_gst}")
    print(f"   Rate: {default_gst.rate}%")
    print(f"   CGST: {default_gst.cgst_rate}%")
    print(f"   SGST: {default_gst.sgst_rate}%")
    
    # Test GST calculation
    base_amount = Decimal('1000.00')
    gst_breakdown = default_gst.calculate_gst_breakdown(base_amount)
    print(f"   GST breakdown for ₹{base_amount}:")
    print(f"   - CGST: ₹{gst_breakdown['cgst_amount']}")
    print(f"   - SGST: ₹{gst_breakdown['sgst_amount']}")
    print(f"   - Total GST: ₹{gst_breakdown['total_gst']}")
    
    # 2. Test Product GST Integration
    print("\n2. Testing Product GST Integration...")
    
    # Create test category and brand if they don't exist
    category, _ = Category.objects.get_or_create(
        name="Test Electronics",
        defaults={'description': 'Test category for electronics'}
    )
    
    brand, _ = Brand.objects.get_or_create(
        name="Test Brand",
        defaults={'description': 'Test brand'}
    )
    
    # Create test product
    product, created = Product.objects.get_or_create(
        name="Test Smart Lock",
        defaults={
            'description': 'Test smart lock product',
            'category': category,
            'brand': brand,
            'price': Decimal('2500.00'),
            'stock': 10,
            'gst': default_gst
        }
    )
    
    if created:
        print(f"✅ Test product created: {product.name}")
    else:
        print(f"✅ Using existing test product: {product.name}")
    
    # Test product GST calculation
    product_gst = product.calculate_gst_breakdown(quantity=2)
    print(f"   Product GST for 2 units (₹{product.price} each):")
    print(f"   - Base amount: ₹{product.price * 2}")
    print(f"   - CGST: ₹{product_gst['cgst_amount']}")
    print(f"   - SGST: ₹{product_gst['sgst_amount']}")
    print(f"   - Total GST: ₹{product_gst['total_gst']}")
    
    # 3. Test GST Service
    print("\n3. Testing GST Service...")
    
    subtotal = Decimal('5000.00')
    shipping_cost = Decimal('100.00')
    discount = Decimal('200.00')
    
    gst_calculation = gst_service.calculate_order_gst(
        subtotal=subtotal,
        shipping_cost=shipping_cost,
        discount=discount,
        is_inter_state=False
    )
    
    print(f"   Order GST calculation:")
    print(f"   - Subtotal: ₹{gst_calculation['subtotal']}")
    print(f"   - Discount: ₹{gst_calculation['discount']}")
    print(f"   - Discounted subtotal: ₹{gst_calculation['discounted_subtotal']}")
    print(f"   - CGST: ₹{gst_calculation['cgst_amount']}")
    print(f"   - SGST: ₹{gst_calculation['sgst_amount']}")
    print(f"   - Total GST: ₹{gst_calculation['gst_amount']}")
    print(f"   - Shipping: ₹{gst_calculation['shipping_cost']}")
    print(f"   - Final Total: ₹{gst_calculation['total']}")
    
    # 4. Test Inter-state GST
    print("\n4. Testing Inter-state GST (IGST)...")
    
    igst_calculation = gst_service.calculate_order_gst(
        subtotal=subtotal,
        shipping_cost=shipping_cost,
        discount=discount,
        is_inter_state=True
    )
    
    print(f"   Inter-state GST calculation:")
    print(f"   - IGST: ₹{igst_calculation['igst_amount']}")
    print(f"   - CGST: ₹{igst_calculation['cgst_amount']}")
    print(f"   - SGST: ₹{igst_calculation['sgst_amount']}")
    print(f"   - Total GST: ₹{igst_calculation['gst_amount']}")
    
    print("\n✅ All GST functionality tests passed!")
    print("\n📋 Summary:")
    print("   - GST model with dynamic rates: ✅")
    print("   - Product GST integration: ✅")
    print("   - Order GST calculation: ✅")
    print("   - Inter-state IGST support: ✅")
    print("   - Intra-state CGST+SGST support: ✅")
    
    print("\n🎯 GST Implementation Features:")
    print("   - 18% default GST rate (9% CGST + 9% SGST)")
    print("   - Dynamic GST rates per product")
    print("   - Inter-state IGST calculation")
    print("   - GST breakdown in UI components")
    print("   - Invoice generation with GST compliance")
    print("   - Database storage of GST amounts")

if __name__ == "__main__":
    test_gst_functionality()
