# Generated by Django 5.0.2 on 2025-05-27 11:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0028_gst_product_gst'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='gst',
            options={'ordering': ['-is_default', 'rate'], 'verbose_name': 'GST Rate', 'verbose_name_plural': 'GST Rates'},
        ),
        migrations.AddField(
            model_name='gst',
            name='description',
            field=models.TextField(blank=True, help_text='Description of when this GST rate applies'),
        ),
        migrations.AddField(
            model_name='gst',
            name='is_default',
            field=models.BooleanField(default=False, help_text='Default GST rate for new products'),
        ),
    ]
