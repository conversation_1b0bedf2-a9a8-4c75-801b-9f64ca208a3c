"""
Test settings for the e-commerce API.
This file overrides settings from the main settings.py for testing purposes.
"""

from backend.settings import *

# Use SQLite for testing
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': os.path.join(BASE_DIR, 'test_db.sqlite3'),
    }
}

# Use a faster password hasher for testing
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',
]

# Use the console email backend for testing
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Disable CSRF protection for testing
MIDDLEWARE = [m for m in MIDDLEWARE if 'CsrfViewMiddleware' not in m]

# Test-specific settings
PHONEPE_CLIENT_ID = 'TEST_CLIENT_ID'
PHONEPE_CLIENT_SECRET = 'TEST_CLIENT_SECRET'
PHONEPE_CALLBACK_URL = 'http://testserver/api/v1/payments/phonepe/callback/'
PHONEPE_ENVIRONMENT = 'UAT'
FRONTEND_URL = 'http://testserver'

# Database settings for tests
DB_NAME = 'test_triumph'
DB_USER = 'test_user'
DB_PASSWORD = 'test_password'
DB_HOST = 'localhost'
DB_PORT = '5432'

# Email settings for tests - use console backend
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
DEFAULT_FROM_EMAIL = '<EMAIL>'
EMAIL_HOST = 'localhost'
EMAIL_PORT = 25

# Email timeout settings to prevent long hanging connections
EMAIL_TIMEOUT = 30  # 30 seconds timeout
