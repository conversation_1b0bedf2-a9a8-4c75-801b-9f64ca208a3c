"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/embla-carousel-class-names";
exports.ids = ["vendor-chunks/embla-carousel-class-names"];
exports.modules = {

/***/ "(ssr)/./node_modules/embla-carousel-class-names/esm/embla-carousel-class-names.esm.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/embla-carousel-class-names/esm/embla-carousel-class-names.esm.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClassNames)\n/* harmony export */ });\nconst defaultOptions = {\n  active: true,\n  breakpoints: {},\n  snapped: 'is-snapped',\n  inView: 'is-in-view',\n  draggable: 'is-draggable',\n  dragging: 'is-dragging',\n  loop: 'is-loop'\n};\n\nfunction normalizeClassNames(classNames) {\n  const normalized = Array.isArray(classNames) ? classNames : [classNames];\n  return normalized.filter(Boolean);\n}\nfunction removeClass(node, classNames) {\n  if (!node || !classNames.length) return;\n  node.classList.remove(...classNames);\n}\nfunction addClass(node, classNames) {\n  if (!node || !classNames.length) return;\n  node.classList.add(...classNames);\n}\n\nfunction ClassNames(userOptions = {}) {\n  let options;\n  let emblaApi;\n  let root;\n  let slides;\n  let snappedIndexes = [];\n  let inViewIndexes = [];\n  const selectedEvents = ['select'];\n  const draggingEvents = ['pointerDown', 'pointerUp'];\n  const inViewEvents = ['slidesInView'];\n  const classNames = {\n    snapped: [],\n    inView: [],\n    draggable: [],\n    dragging: [],\n    loop: []\n  };\n  function init(emblaApiInstance, optionsHandler) {\n    emblaApi = emblaApiInstance;\n    const {\n      mergeOptions,\n      optionsAtMedia\n    } = optionsHandler;\n    const optionsBase = mergeOptions(defaultOptions, ClassNames.globalOptions);\n    const allOptions = mergeOptions(optionsBase, userOptions);\n    options = optionsAtMedia(allOptions);\n    root = emblaApi.rootNode();\n    slides = emblaApi.slideNodes();\n    const {\n      watchDrag,\n      loop\n    } = emblaApi.internalEngine().options;\n    const isDraggable = !!watchDrag;\n    if (options.loop && loop) {\n      classNames.loop = normalizeClassNames(options.loop);\n      addClass(root, classNames.loop);\n    }\n    if (options.draggable && isDraggable) {\n      classNames.draggable = normalizeClassNames(options.draggable);\n      addClass(root, classNames.draggable);\n    }\n    if (options.dragging) {\n      classNames.dragging = normalizeClassNames(options.dragging);\n      draggingEvents.forEach(evt => emblaApi.on(evt, toggleDraggingClass));\n    }\n    if (options.snapped) {\n      classNames.snapped = normalizeClassNames(options.snapped);\n      selectedEvents.forEach(evt => emblaApi.on(evt, toggleSnappedClasses));\n      toggleSnappedClasses();\n    }\n    if (options.inView) {\n      classNames.inView = normalizeClassNames(options.inView);\n      inViewEvents.forEach(evt => emblaApi.on(evt, toggleInViewClasses));\n      toggleInViewClasses();\n    }\n  }\n  function destroy() {\n    draggingEvents.forEach(evt => emblaApi.off(evt, toggleDraggingClass));\n    selectedEvents.forEach(evt => emblaApi.off(evt, toggleSnappedClasses));\n    inViewEvents.forEach(evt => emblaApi.off(evt, toggleInViewClasses));\n    removeClass(root, classNames.loop);\n    removeClass(root, classNames.draggable);\n    removeClass(root, classNames.dragging);\n    toggleSlideClasses([], snappedIndexes, classNames.snapped);\n    toggleSlideClasses([], inViewIndexes, classNames.inView);\n    Object.keys(classNames).forEach(classNameKey => {\n      const key = classNameKey;\n      classNames[key] = [];\n    });\n  }\n  function toggleDraggingClass(_, evt) {\n    const toggleClass = evt === 'pointerDown' ? addClass : removeClass;\n    toggleClass(root, classNames.dragging);\n  }\n  function toggleSlideClasses(addClassIndexes = [], removeClassIndexes = [], classNames) {\n    const removeClassSlides = removeClassIndexes.map(index => slides[index]);\n    const addClassSlides = addClassIndexes.map(index => slides[index]);\n    removeClassSlides.forEach(slide => removeClass(slide, classNames));\n    addClassSlides.forEach(slide => addClass(slide, classNames));\n    return addClassIndexes;\n  }\n  function toggleSnappedClasses() {\n    const {\n      slideRegistry\n    } = emblaApi.internalEngine();\n    const newSnappedIndexes = slideRegistry[emblaApi.selectedScrollSnap()];\n    snappedIndexes = toggleSlideClasses(newSnappedIndexes, snappedIndexes, classNames.snapped);\n  }\n  function toggleInViewClasses() {\n    const newInViewIndexes = emblaApi.slidesInView();\n    inViewIndexes = toggleSlideClasses(newInViewIndexes, inViewIndexes, classNames.inView);\n  }\n  const self = {\n    name: 'classNames',\n    options: userOptions,\n    init,\n    destroy\n  };\n  return self;\n}\nClassNames.globalOptions = undefined;\n\n\n//# sourceMappingURL=embla-carousel-class-names.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/embla-carousel-class-names/esm/embla-carousel-class-names.esm.js\n");

/***/ })

};
;