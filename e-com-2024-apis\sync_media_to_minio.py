#!/usr/bin/env python
"""
Script to sync media files to MinIO storage.
This script can be run manually or as part of a deployment process.
"""

import os
import sys
import argparse
import logging
from pathlib import Path
import mimetypes
import boto3
from botocore.client import Config
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('minio-sync')

# Load environment variables
load_dotenv()

def get_env_or_default(key, default):
    """Get environment variable or return default value"""
    return os.environ.get(key, default)

def sync_media_to_minio(media_dir, recursive=True, force=False):
    """
    Sync media files to MinIO storage

    Args:
        media_dir: Directory containing media files to sync
        recursive: Whether to recursively sync subdirectories
        force: Whether to force upload even if file exists
    """
    # Get MinIO configuration from environment variables
    endpoint = get_env_or_default('MINIO_STORAGE_ENDPOINT', 'minio:9000')
    access_key = get_env_or_default('MINIO_STORAGE_ACCESS_KEY', 'minioadmin')
    secret_key = get_env_or_default('MINIO_STORAGE_SECRET_KEY', 'minioadmin')
    use_https = get_env_or_default('MINIO_STORAGE_USE_HTTPS', 'False').lower() == 'true'
    bucket_name = get_env_or_default('MINIO_STORAGE_MEDIA_BUCKET_NAME', 'media')

    # Log configuration for debugging (without sensitive info)
    logger.info(f"MinIO Configuration: Endpoint={endpoint}, HTTPS={use_https}, Bucket={bucket_name}")

    # Create S3 client for MinIO
    s3_client = boto3.client(
        's3',
        endpoint_url=f"{'https' if use_https else 'http'}://{endpoint}",
        aws_access_key_id=access_key,
        aws_secret_access_key=secret_key,
        config=Config(
            signature_version='s3v4',
            s3={'addressing_style': 'path'}
        ),
        verify=False,  # Disable SSL verification for self-signed certificates
        region_name='us-east-1',  # MinIO doesn't require a specific region
    )

    # Suppress SSL warnings
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    # Check if bucket exists, create if it doesn't
    bucket_exists = False
    try:
        s3_client.head_bucket(Bucket=bucket_name)
        logger.info(f"Bucket '{bucket_name}' exists")
        bucket_exists = True
    except Exception as e:
        logger.info(f"Bucket check error: {str(e)}")
        logger.info(f"Attempting to create bucket '{bucket_name}'")

    # Skip bucket creation if it already exists
    if bucket_exists:
        logger.info(f"Using existing bucket '{bucket_name}'")
    else:
        try:
            # For MinIO, we don't need to specify a location constraint
            s3_client.create_bucket(Bucket=bucket_name)
            logger.info(f"Successfully created bucket '{bucket_name}'")
        except Exception as create_error:
            logger.error(f"Failed to create bucket: {str(create_error)}")
            logger.info("Continuing with uploads assuming bucket exists")

    # Always try to set the bucket policy (even if bucket already exists)
    try:
        policy = {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Effect": "Allow",
                    "Principal": "*",
                    "Action": ["s3:GetObject"],
                    "Resource": [f"arn:aws:s3:::{bucket_name}/*"]
                }
            ]
        }
        s3_client.put_bucket_policy(Bucket=bucket_name, Policy=str(policy).replace("'", '"'))
        logger.info(f"Set public read policy for bucket '{bucket_name}'")
    except Exception as policy_error:
        logger.warning(f"Could not set bucket policy: {str(policy_error)}")
        logger.warning("Continuing without setting bucket policy")

    # Walk through media directory and upload files
    media_path = Path(media_dir)
    if not media_path.exists():
        logger.error(f"Media directory '{media_dir}' does not exist")
        return

    # Get list of existing objects in the bucket
    existing_objects = set()
    try:
        paginator = s3_client.get_paginator('list_objects_v2')
        for page in paginator.paginate(Bucket=bucket_name):
            if 'Contents' in page:
                for obj in page['Contents']:
                    existing_objects.add(obj['Key'])
        logger.info(f"Found {len(existing_objects)} existing objects in bucket")
    except Exception as e:
        logger.warning(f"Error listing objects in bucket: {e}")
        logger.warning("Will attempt to upload all files")

    # Count files for reporting
    total_files = 0
    uploaded_files = 0
    skipped_files = 0
    error_files = 0

    # Upload files
    for file_path in media_path.glob('**/*' if recursive else '*'):
        if file_path.is_file():
            total_files += 1
            # Get relative path for S3 key
            relative_path = str(file_path.relative_to(media_path))

            # Replace backslashes with forward slashes for S3 paths (important for Windows)
            relative_path = relative_path.replace('\\', '/')

            # Skip if file exists and force is False
            if relative_path in existing_objects and not force:
                logger.info(f"Skipping existing file: {relative_path}")
                skipped_files += 1
                continue

            # Determine content type
            content_type = mimetypes.guess_type(file_path)[0]
            extra_args = {}
            if content_type:
                extra_args['ContentType'] = content_type
            else:
                # Default to binary/octet-stream if content type can't be determined
                extra_args['ContentType'] = 'application/octet-stream'

            # Add cache control header
            extra_args['CacheControl'] = 'max-age=86400'  # 24 hours

            # Add ACL for public read access
            extra_args['ACL'] = 'public-read'

            # Upload file
            logger.info(f"Uploading {file_path} to {bucket_name}/{relative_path}")
            try:
                with open(str(file_path), 'rb') as file_data:
                    s3_client.upload_fileobj(
                        file_data,
                        bucket_name,
                        relative_path,
                        ExtraArgs=extra_args
                    )
                uploaded_files += 1
                logger.info(f"Successfully uploaded {relative_path}")
            except Exception as e:
                logger.error(f"Error uploading {file_path}: {e}")
                error_files += 1

    # Log summary
    logger.info(f"Upload summary: {total_files} total files, {uploaded_files} uploaded, {skipped_files} skipped, {error_files} errors")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Sync media files to MinIO storage')
    parser.add_argument('--media-dir', default='media', help='Directory containing media files to sync')
    parser.add_argument('--no-recursive', action='store_false', dest='recursive', help='Do not recursively sync subdirectories')
    parser.add_argument('--force', action='store_true', help='Force upload even if file exists')
    parser.add_argument('--skip-errors', action='store_true', help='Continue execution even if bucket creation fails')

    args = parser.parse_args()

    try:
        logger.info(f"Starting MinIO sync for directory: {args.media_dir}")
        logger.info(f"Options: recursive={args.recursive}, force={args.force}")

        # Print environment variables for debugging (without sensitive info)
        endpoint = get_env_or_default('MINIO_STORAGE_ENDPOINT', 'minio:9000')
        use_https = get_env_or_default('MINIO_STORAGE_USE_HTTPS', 'False').lower() == 'true'
        bucket_name = get_env_or_default('MINIO_STORAGE_MEDIA_BUCKET_NAME', 'media')
        logger.info(f"Environment: MINIO_STORAGE_ENDPOINT={endpoint}, MINIO_STORAGE_USE_HTTPS={use_https}, MINIO_STORAGE_MEDIA_BUCKET_NAME={bucket_name}")

        # Run the sync function
        sync_media_to_minio(args.media_dir, args.recursive, args.force)
        logger.info("MinIO sync completed successfully")
    except Exception as e:
        logger.error(f"Error during MinIO sync: {str(e)}")
        if args.skip_errors:
            logger.warning("Continuing despite errors due to --skip-errors flag")
            sys.exit(0)
        else:
            logger.error("Sync failed. Use --skip-errors to continue despite errors.")
            sys.exit(1)
