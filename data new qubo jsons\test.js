
class Solution {
    minJumps(arr, n) {
        if (n <= 1) return 0;
        if (arr[0] === 0) return -1;

        let maxReach = arr[0];
        let steps = arr[0];
        let jumps = 1;

        for (let i = 1; i < n; i++) {
            if (i === n - 1) return jumps;

            maxReach = Math.max(max<PERSON>each, i + arr[i]);
            steps--;

            if (steps === 0) {
                jumps++;
                if (i >= maxReach) return -1;
                steps = maxReach - i;
            }
        }

        return -1;
    }
}
function Main(input) {
    const lines = input.trim().split("\n");
    const n = parseInt(lines[0]);
    const arr = lines[1].split(" ").map(Number);

    const solution = new Solution();
    console.log(solution.minJumps(arr, n));
}