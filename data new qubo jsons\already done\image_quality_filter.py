#!/usr/bin/env python3
"""
Image Quality Filter for Haier Product Images
Analyzes and removes low-quality images based on multiple criteria
"""

import os
import shutil
from pathlib import Path
from typing import List, Tuple, Dict
import logging
from PIL import Image, ImageStat
import hashlib

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('image_quality_filter.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ImageQualityFilter:
    def __init__(self, input_dir: str = "haier_images", backup_dir: str = "removed_images"):
        self.input_dir = Path(input_dir)
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(exist_ok=True)
        
        # Quality thresholds
        self.min_width = 300
        self.min_height = 300
        self.min_file_size = 10 * 1024  # 10KB
        self.max_file_size = 50 * 1024 * 1024  # 50MB
        self.min_aspect_ratio = 0.3  # Very wide or very tall images
        self.max_aspect_ratio = 3.0
        self.min_variance = 100  # Image variance (to detect blank/solid color images)
        
        # Statistics
        self.stats = {
            'total_images': 0,
            'removed_images': 0,
            'kept_images': 0,
            'duplicates_removed': 0,
            'reasons': {}
        }

    def calculate_image_hash(self, image_path: Path) -> str:
        """Calculate perceptual hash of image for duplicate detection"""
        try:
            with Image.open(image_path) as img:
                # Convert to grayscale and resize for consistent hashing
                img = img.convert('L').resize((8, 8), Image.Resampling.LANCZOS)
                pixels = list(img.getdata())
                # Create a simple hash based on pixel values
                hash_string = ''.join([str(1 if pixel > 128 else 0) for pixel in pixels])
                return hashlib.md5(hash_string.encode()).hexdigest()[:16]
        except Exception as e:
            logger.warning(f"Could not calculate hash for {image_path}: {e}")
            return ""

    def calculate_image_variance(self, image_path: Path) -> float:
        """Calculate image variance to detect blank or solid color images"""
        try:
            with Image.open(image_path) as img:
                # Convert to grayscale for variance calculation
                gray_img = img.convert('L')
                stat = ImageStat.Stat(gray_img)
                return stat.var[0]
        except Exception as e:
            logger.warning(f"Could not calculate variance for {image_path}: {e}")
            return 0

    def is_low_quality(self, image_path: Path) -> Tuple[bool, List[str]]:
        """Analyze image and determine if it's low quality"""
        reasons = []
        
        try:
            # Check file size
            file_size = image_path.stat().st_size
            if file_size < self.min_file_size:
                reasons.append(f"file_too_small_{file_size//1024}KB")
            elif file_size > self.max_file_size:
                reasons.append(f"file_too_large_{file_size//(1024*1024)}MB")
            
            # Open and analyze image
            with Image.open(image_path) as img:
                width, height = img.size
                
                # Check dimensions
                if width < self.min_width:
                    reasons.append(f"width_too_small_{width}px")
                if height < self.min_height:
                    reasons.append(f"height_too_small_{height}px")
                
                # Check aspect ratio
                aspect_ratio = width / height if height > 0 else 0
                if aspect_ratio < self.min_aspect_ratio:
                    reasons.append(f"too_tall_{aspect_ratio:.2f}")
                elif aspect_ratio > self.max_aspect_ratio:
                    reasons.append(f"too_wide_{aspect_ratio:.2f}")
                
                # Check for blank/solid color images
                variance = self.calculate_image_variance(image_path)
                if variance < self.min_variance:
                    reasons.append(f"low_variance_{variance:.1f}")
                
                # Check for corrupted images
                try:
                    img.verify()
                except Exception:
                    reasons.append("corrupted")
                    
        except Exception as e:
            reasons.append(f"read_error_{str(e)[:20]}")
            logger.error(f"Error analyzing {image_path}: {e}")
        
        return len(reasons) > 0, reasons

    def find_duplicates(self, image_paths: List[Path]) -> Dict[str, List[Path]]:
        """Find duplicate images using perceptual hashing"""
        hash_groups = {}
        
        logger.info("Calculating image hashes for duplicate detection...")
        for image_path in image_paths:
            img_hash = self.calculate_image_hash(image_path)
            if img_hash:
                if img_hash not in hash_groups:
                    hash_groups[img_hash] = []
                hash_groups[img_hash].append(image_path)
        
        # Return only groups with duplicates
        duplicates = {h: paths for h, paths in hash_groups.items() if len(paths) > 1}
        return duplicates

    def remove_image(self, image_path: Path, reasons: List[str]) -> bool:
        """Move image to backup directory"""
        try:
            # Create backup subdirectory structure
            relative_path = image_path.relative_to(self.input_dir)
            backup_path = self.backup_dir / relative_path
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Move file to backup
            shutil.move(str(image_path), str(backup_path))
            
            # Update statistics
            for reason in reasons:
                self.stats['reasons'][reason] = self.stats['reasons'].get(reason, 0) + 1
            
            logger.info(f"Removed: {image_path.name} - Reasons: {', '.join(reasons)}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to remove {image_path}: {e}")
            return False

    def process_folder(self, folder_path: Path) -> None:
        """Process all images in a folder"""
        logger.info(f"Processing folder: {folder_path.name}")
        
        # Get all image files
        image_extensions = {'.jpg', '.jpeg', '.png', '.webp', '.bmp', '.tiff'}
        image_files = [
            f for f in folder_path.iterdir() 
            if f.is_file() and f.suffix.lower() in image_extensions
        ]
        
        if not image_files:
            logger.warning(f"No images found in {folder_path}")
            return
        
        logger.info(f"Found {len(image_files)} images in {folder_path.name}")
        
        # Find and handle duplicates first
        duplicates = self.find_duplicates(image_files)
        if duplicates:
            logger.info(f"Found {len(duplicates)} groups of duplicate images")
            for hash_val, duplicate_paths in duplicates.items():
                # Keep the largest file, remove others
                largest_file = max(duplicate_paths, key=lambda p: p.stat().st_size)
                for dup_path in duplicate_paths:
                    if dup_path != largest_file:
                        if self.remove_image(dup_path, ["duplicate"]):
                            self.stats['duplicates_removed'] += 1
                            image_files.remove(dup_path)
        
        # Analyze remaining images for quality
        for image_path in image_files:
            self.stats['total_images'] += 1
            
            is_low_qual, reasons = self.is_low_quality(image_path)
            
            if is_low_qual:
                if self.remove_image(image_path, reasons):
                    self.stats['removed_images'] += 1
                else:
                    self.stats['kept_images'] += 1
            else:
                self.stats['kept_images'] += 1
                logger.debug(f"Kept: {image_path.name}")

    def process_all_folders(self) -> None:
        """Process all product folders"""
        if not self.input_dir.exists():
            logger.error(f"Input directory {self.input_dir} does not exist")
            return
        
        product_folders = [
            d for d in self.input_dir.iterdir() 
            if d.is_dir()
        ]
        
        if not product_folders:
            logger.error(f"No product folders found in {self.input_dir}")
            return
        
        logger.info(f"Found {len(product_folders)} product folders to process")
        
        for folder in sorted(product_folders):
            self.process_folder(folder)
        
        self.print_summary()

    def print_summary(self) -> None:
        """Print processing summary"""
        logger.info("\n" + "="*60)
        logger.info("IMAGE QUALITY FILTERING SUMMARY")
        logger.info("="*60)
        logger.info(f"Total images processed: {self.stats['total_images']}")
        logger.info(f"Images kept: {self.stats['kept_images']}")
        logger.info(f"Images removed: {self.stats['removed_images']}")
        logger.info(f"Duplicates removed: {self.stats['duplicates_removed']}")
        
        if self.stats['total_images'] > 0:
            kept_percentage = (self.stats['kept_images'] / self.stats['total_images']) * 100
            logger.info(f"Quality retention rate: {kept_percentage:.1f}%")
        
        if self.stats['reasons']:
            logger.info("\nRemoval reasons breakdown:")
            for reason, count in sorted(self.stats['reasons'].items()):
                logger.info(f"  {reason}: {count}")
        
        logger.info(f"\nRemoved images backed up to: {self.backup_dir}")
        logger.info("="*60)

    def restore_images(self) -> None:
        """Restore all removed images (undo operation)"""
        if not self.backup_dir.exists():
            logger.error("No backup directory found")
            return
        
        logger.info("Restoring removed images...")
        restored_count = 0
        
        for backup_file in self.backup_dir.rglob("*"):
            if backup_file.is_file():
                # Calculate original path
                relative_path = backup_file.relative_to(self.backup_dir)
                original_path = self.input_dir / relative_path
                
                # Create directory if needed
                original_path.parent.mkdir(parents=True, exist_ok=True)
                
                # Move file back
                try:
                    shutil.move(str(backup_file), str(original_path))
                    restored_count += 1
                    logger.info(f"Restored: {original_path}")
                except Exception as e:
                    logger.error(f"Failed to restore {backup_file}: {e}")
        
        logger.info(f"Restored {restored_count} images")
        
        # Remove empty backup directories
        try:
            shutil.rmtree(self.backup_dir)
            logger.info("Removed backup directory")
        except Exception as e:
            logger.warning(f"Could not remove backup directory: {e}")

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Filter low-quality images from Haier product collection")
    parser.add_argument("--input-dir", default="haier_images", help="Input directory containing product folders")
    parser.add_argument("--backup-dir", default="removed_images", help="Directory to store removed images")
    parser.add_argument("--restore", action="store_true", help="Restore previously removed images")
    parser.add_argument("--min-width", type=int, default=300, help="Minimum image width")
    parser.add_argument("--min-height", type=int, default=300, help="Minimum image height")
    parser.add_argument("--min-size", type=int, default=10, help="Minimum file size in KB")
    
    args = parser.parse_args()
    
    filter_tool = ImageQualityFilter(args.input_dir, args.backup_dir)
    
    # Update thresholds if provided
    filter_tool.min_width = args.min_width
    filter_tool.min_height = args.min_height
    filter_tool.min_file_size = args.min_size * 1024
    
    if args.restore:
        filter_tool.restore_images()
    else:
        filter_tool.process_all_folders()

if __name__ == "__main__":
    main()
