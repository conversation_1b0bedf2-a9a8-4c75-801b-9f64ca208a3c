#!/usr/bin/env python3
"""
Test script for dynamic GST functionality and invoice generation for paid orders only.
"""

import os
import sys
import django
from decimal import Decimal

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ecommerce.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

django.setup()

from django.contrib.auth import get_user_model
from products.models import Product, Category, Brand, GST
from orders.models import Order, OrderItem, ShippingMethod
from orders.gst_service import gst_service
from orders.invoice_service import invoice_service
from users.models import Address

User = get_user_model()

def test_dynamic_gst_functionality():
    """Test dynamic GST functionality with different product GST rates"""
    print("🧪 Testing Dynamic GST Functionality...")
    
    # 1. Create different GST rates
    print("\n1. Creating different GST rates...")
    
    # Electronics GST (18%)
    electronics_gst, _ = GST.objects.get_or_create(
        name="Electronics",
        defaults={
            'rate': Decimal('18.00'),
            'cgst_rate': Decimal('9.00'),
            'sgst_rate': Decimal('9.00'),
            'igst_rate': Decimal('18.00'),
            'hsn_code': '8471',
            'description': 'GST for electronics products',
            'is_active': True
        }
    )
    
    # Books GST (5%)
    books_gst, _ = GST.objects.get_or_create(
        name="Books",
        defaults={
            'rate': Decimal('5.00'),
            'cgst_rate': Decimal('2.50'),
            'sgst_rate': Decimal('2.50'),
            'igst_rate': Decimal('5.00'),
            'hsn_code': '4901',
            'description': 'GST for books and educational materials',
            'is_active': True
        }
    )
    
    # Clothing GST (12%)
    clothing_gst, _ = GST.objects.get_or_create(
        name="Clothing",
        defaults={
            'rate': Decimal('12.00'),
            'cgst_rate': Decimal('6.00'),
            'sgst_rate': Decimal('6.00'),
            'igst_rate': Decimal('12.00'),
            'hsn_code': '6109',
            'description': 'GST for clothing and textiles',
            'is_active': True
        }
    )
    
    print(f"✅ Created GST rates:")
    print(f"   Electronics: {electronics_gst.rate}%")
    print(f"   Books: {books_gst.rate}%")
    print(f"   Clothing: {clothing_gst.rate}%")
    
    # 2. Create categories and products with different GST rates
    print("\n2. Creating products with different GST rates...")
    
    # Create categories
    electronics_category, _ = Category.objects.get_or_create(
        name="Electronics",
        defaults={'slug': 'electronics', 'description': 'Electronic products'}
    )
    
    books_category, _ = Category.objects.get_or_create(
        name="Books",
        defaults={'slug': 'books', 'description': 'Books and educational materials'}
    )
    
    clothing_category, _ = Category.objects.get_or_create(
        name="Clothing",
        defaults={'slug': 'clothing', 'description': 'Clothing and fashion'}
    )
    
    # Create brand
    test_brand, _ = Brand.objects.get_or_create(
        name="Test Brand",
        defaults={'slug': 'test-brand', 'description': 'Test brand for GST testing'}
    )
    
    # Create products with different GST rates
    laptop = Product.objects.create(
        name="Test Laptop",
        slug="test-laptop",
        description="A test laptop for GST testing",
        category=electronics_category,
        brand=test_brand,
        price=Decimal('59000.00'),  # MRP inclusive of 18% GST
        gst=electronics_gst,
        stock=10,
        is_active=True
    )
    
    book = Product.objects.create(
        name="Test Book",
        slug="test-book",
        description="A test book for GST testing",
        category=books_category,
        brand=test_brand,
        price=Decimal('525.00'),  # MRP inclusive of 5% GST
        gst=books_gst,
        stock=50,
        is_active=True
    )
    
    tshirt = Product.objects.create(
        name="Test T-Shirt",
        slug="test-tshirt",
        description="A test t-shirt for GST testing",
        category=clothing_category,
        brand=test_brand,
        price=Decimal('1120.00'),  # MRP inclusive of 12% GST
        gst=clothing_gst,
        stock=25,
        is_active=True
    )
    
    print(f"✅ Created products:")
    print(f"   Laptop: ₹{laptop.price} (GST: {laptop.get_gst_rate().rate}%)")
    print(f"   Book: ₹{book.price} (GST: {book.get_gst_rate().rate}%)")
    print(f"   T-Shirt: ₹{tshirt.price} (GST: {tshirt.get_gst_rate().rate}%)")
    
    # 3. Test individual product GST calculations
    print("\n3. Testing individual product GST calculations...")
    
    for product in [laptop, book, tshirt]:
        gst_breakdown = product.calculate_gst_breakdown_from_mrp(quantity=1)
        print(f"\n   {product.name}:")
        print(f"     MRP: ₹{gst_breakdown['unit_mrp']}")
        print(f"     Base Price: ₹{gst_breakdown['unit_base_price']}")
        print(f"     GST Rate: {gst_breakdown['gst_rate']}%")
        print(f"     GST Amount: ₹{gst_breakdown['total_gst']}")
        print(f"     CGST: ₹{gst_breakdown['cgst_amount']}")
        print(f"     SGST: ₹{gst_breakdown['sgst_amount']}")
    
    # 4. Test cart with mixed GST rates
    print("\n4. Testing cart with mixed GST rates...")
    
    # Simulate cart items
    from orders.models import Cart, CartItem
    
    # Create test user
    test_user, _ = User.objects.get_or_create(
        email="<EMAIL>",
        defaults={'username': 'testuser', 'first_name': 'Test', 'last_name': 'User'}
    )
    
    # Create cart
    cart, _ = Cart.objects.get_or_create(user=test_user)
    cart.items.all().delete()  # Clear existing items
    
    # Add items to cart
    CartItem.objects.create(cart=cart, product=laptop, quantity=1)
    CartItem.objects.create(cart=cart, product=book, quantity=2)
    CartItem.objects.create(cart=cart, product=tshirt, quantity=3)
    
    # Calculate cart GST
    cart_gst = gst_service.calculate_cart_gst_from_mrp(cart.items.all())
    
    print(f"   Cart GST Breakdown:")
    print(f"     Subtotal (base): ₹{cart_gst['subtotal']}")
    print(f"     Total MRP: ₹{cart_gst['total_mrp']}")
    print(f"     Total GST: ₹{cart_gst['total_gst_amount']}")
    print(f"     CGST: ₹{cart_gst['total_cgst_amount']}")
    print(f"     SGST: ₹{cart_gst['total_sgst_amount']}")
    
    print(f"\n   Item-wise breakdown:")
    for item_detail in cart_gst['item_details']:
        product = item_detail['product']
        print(f"     {product.name} (x{item_detail['quantity']}): GST {item_detail['gst_rate']}% = ₹{item_detail['gst_amount']}")
    
    return {
        'products': [laptop, book, tshirt],
        'cart': cart,
        'test_user': test_user,
        'cart_gst': cart_gst
    }

def test_invoice_generation_for_paid_orders():
    """Test that invoices are only generated for paid orders"""
    print("\n\n🧾 Testing Invoice Generation for Paid Orders Only...")
    
    # Get test data from previous test
    test_data = test_dynamic_gst_functionality()
    test_user = test_data['test_user']
    cart = test_data['cart']
    
    # Create shipping method and address
    shipping_method, _ = ShippingMethod.objects.get_or_create(
        name="Standard Shipping",
        defaults={
            'description': 'Standard shipping method',
            'price': Decimal('50.00'),
            'estimated_days': 5,
            'is_active': True
        }
    )
    
    address, _ = Address.objects.get_or_create(
        user=test_user,
        defaults={
            'street_address': '123 Test Street',
            'city': 'Test City',
            'state': 'Test State',
            'postal_code': '123456',
            'country': 'India',
            'is_default': True
        }
    )
    
    # Create order with PENDING status
    cart_gst = test_data['cart_gst']
    order = Order.objects.create(
        user=test_user,
        status='PENDING',
        shipping_address=address,
        billing_address=address,
        shipping_method=shipping_method,
        subtotal=cart_gst['subtotal'],
        gst_amount=cart_gst['total_gst_amount'],
        cgst_amount=cart_gst['total_cgst_amount'],
        sgst_amount=cart_gst['total_sgst_amount'],
        igst_amount=cart_gst['total_igst_amount'],
        shipping_cost=shipping_method.price,
        total=cart_gst['total_mrp'] + shipping_method.price
    )
    
    # Create order items
    for item in cart.items.all():
        OrderItem.objects.create(
            order=order,
            product=item.product,
            quantity=item.quantity,
            unit_price=item.product.price,
            total_price=item.product.price * item.quantity,
            product_name=item.product.name
        )
    
    print(f"✅ Created order {order.id} with status: {order.status}")
    
    # 1. Test invoice generation for PENDING order (should fail)
    print("\n1. Testing invoice generation for PENDING order...")
    try:
        invoice = invoice_service.generate_invoice(order)
        print("❌ ERROR: Invoice was generated for PENDING order (should have failed)")
    except ValueError as e:
        print(f"✅ Correctly blocked invoice generation: {e}")
    
    # 2. Update order to PAID status and test invoice generation
    print("\n2. Updating order to PAID status and testing invoice generation...")
    order.status = 'PAID'
    order.save()
    
    try:
        invoice = invoice_service.generate_invoice(order)
        print(f"✅ Successfully generated invoice {invoice.invoice_number} for PAID order")
        print(f"   Invoice ID: {invoice.id}")
        print(f"   Generated at: {invoice.generated_at}")
        print(f"   PDF file: {invoice.pdf_file.name if invoice.pdf_file else 'Not generated'}")
    except Exception as e:
        print(f"❌ ERROR generating invoice for PAID order: {e}")
    
    return order

if __name__ == "__main__":
    print("🚀 Starting Dynamic GST and Invoice Tests...")
    
    try:
        # Test dynamic GST functionality
        test_data = test_dynamic_gst_functionality()
        
        # Test invoice generation
        order = test_invoice_generation_for_paid_orders()
        
        print("\n\n✅ All tests completed successfully!")
        print("\nSummary:")
        print("- Dynamic GST rates working correctly")
        print("- Product-specific GST calculations working")
        print("- Cart with mixed GST rates working")
        print("- Invoice generation restricted to PAID orders only")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
