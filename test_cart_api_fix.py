#!/usr/bin/env python3
"""
Test cart API JSON serialization fix
"""

import os
import sys
import django
import json
from decimal import Decimal

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

django.setup()

def test_cart_serialization():
    """Test that cart serialization works without JSON errors"""
    print("🔍 Testing Cart API JSON Serialization Fix...")
    
    from django.contrib.auth import get_user_model
    from orders.models import Cart, CartItem
    from orders.serializers import CartSerializer
    from products.models import Product, GST, Category, Brand
    from decimal import Decimal
    
    User = get_user_model()
    
    try:
        # 1. Create test user
        user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={'username': 'cart_test_user'}
        )
        print(f"✅ Test user: {user.email}")
        
        # 2. Create test GST rate
        test_gst, created = GST.objects.get_or_create(
            name="Test GST",
            defaults={
                'rate': Decimal('18.00'),
                'cgst_rate': Decimal('9.00'),
                'sgst_rate': Decimal('9.00'),
                'igst_rate': Decimal('18.00'),
                'hsn_code': '8471',
                'is_active': True
            }
        )
        print(f"✅ Test GST: {test_gst.rate}%")
        
        # 3. Create test product
        category, created = Category.objects.get_or_create(
            name="Test Category",
            defaults={'slug': 'test-category'}
        )
        
        brand, created = Brand.objects.get_or_create(
            name="Test Brand",
            defaults={'slug': 'test-brand'}
        )
        
        product, created = Product.objects.get_or_create(
            slug="test-product-cart",
            defaults={
                'name': "Test Product for Cart",
                'description': "Test product",
                'category': category,
                'brand': brand,
                'price': Decimal('1180.00'),  # MRP inclusive of 18% GST
                'gst': test_gst,
                'stock': 10,
                'is_active': True
            }
        )
        print(f"✅ Test product: {product.name} - ₹{product.price}")
        
        # 4. Create cart and add item
        cart, created = Cart.objects.get_or_create(user=user)
        cart.items.all().delete()  # Clear existing items
        
        cart_item, created = CartItem.objects.get_or_create(
            cart=cart,
            product=product,
            defaults={'quantity': 2}
        )
        print(f"✅ Cart item added: {cart_item.quantity}x {product.name}")
        
        # 5. Test cart serialization
        print("\n🧪 Testing cart serialization...")
        cart_serializer = CartSerializer(cart)
        cart_data = cart_serializer.data
        
        print("✅ Cart serialization successful")
        print(f"   Items: {len(cart_data.get('items', []))}")
        
        # 6. Test JSON serialization
        print("\n🧪 Testing JSON serialization...")
        json_string = json.dumps(cart_data, indent=2)
        print("✅ JSON serialization successful")
        
        # 7. Check GST breakdown
        if 'gst_breakdown' in cart_data:
            gst_breakdown = cart_data['gst_breakdown']
            print(f"\n📊 GST Breakdown:")
            print(f"   Total GST: ₹{gst_breakdown.get('total_gst_amount', 0)}")
            print(f"   CGST: ₹{gst_breakdown.get('total_cgst_amount', 0)}")
            print(f"   SGST: ₹{gst_breakdown.get('total_sgst_amount', 0)}")
            
            item_details = gst_breakdown.get('item_details', [])
            print(f"   Item details: {len(item_details)} items")
            
            for item in item_details:
                product_info = item.get('product', {})
                print(f"     - {product_info.get('name', 'Unknown')}: {item.get('gst_rate', 0)}% = ₹{item.get('gst_amount', 0)}")
        else:
            print("⚠️  No GST breakdown found")
        
        print("\n🎉 Cart API JSON serialization fix successful!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_cart_serialization()
    
    if success:
        print("\n✅ SUMMARY: Cart API JSON Serialization Fixed!")
        print("   🔹 Product objects replaced with serializable data")
        print("   🔹 Decimal values converted to float")
        print("   🔹 GST breakdown properly serialized")
        print("   🔹 Cart API should now work without 500 errors")
    
    sys.exit(0 if success else 1)
