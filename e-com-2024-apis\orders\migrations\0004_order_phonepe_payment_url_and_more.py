# Generated by Django 5.0.2 on 2025-05-12 06:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0003_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='phonepe_payment_url',
            field=models.URLField(blank=True, max_length=500),
        ),
        migrations.AddField(
            model_name='order',
            name='phonepe_transaction_id',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='payment',
            name='phonepe_transaction_details',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='payment',
            name='payment_method',
            field=models.CharField(choices=[('CARD', 'Credit/Debit Card'), ('STRIPE', 'Stripe'), ('PHONEPE', 'PhonePe'), ('COD', 'Cash on Delivery')], default='CARD', max_length=50),
        ),
    ]
