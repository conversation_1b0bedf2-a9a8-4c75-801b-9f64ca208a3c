import csv
import json
import requests
import re
from bs4 import BeautifulSoup
import time
import os

# Create output directory if it doesn't exist
os.makedirs('output/json', exist_ok=True)

# Function to fetch data from Haier API
def fetch_haier_api_data(page_no=1, page_size=12):
    url = f"https://www.haier.com/igs/front/overseas_product/getProduct?code=d8a86ae821b646d8b0404ad35591386b&searchWord=(channelId%3D41478)%20and%20(psale%3D1)&pageNo={page_no}&pageSize={page_size}&siteId=5&filterJsonUrl=https%3A%2F%2Fwww.haier.com%2Fin%2Fkitchen-appliance%2Ffilter_es.json&orderBy=docOrderPri%3Adesc%2CdocOrder%3Adesc&filter=&defaultSearch=(channelId%3D41478)%20and%20(psale%3D1)&retFilterJson=yes"
    
    response = requests.get(url)
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error fetching API data: {response.status_code}")
        return None

# Function to fetch product details from product page
def fetch_product_details(url):
    try:
        response = requests.get(url)
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Extract description
            description = ""
            description_div = soup.select_one('.product-desc')
            if description_div:
                description = description_div.get_text(strip=True)
            
            # Extract images
            images = []
            image_containers = soup.select('.swiper-slide img')
            for img in image_containers:
                if img.get('src'):
                    # Make sure we have absolute URLs
                    img_url = img['src']
                    if not img_url.startswith('http'):
                        img_url = 'https://www.haier.com' + img_url
                    images.append(img_url)
            
            return {
                "description": description,
                "images": images
            }
        else:
            print(f"Error fetching product details: {response.status_code}")
            return {"description": "", "images": []}
    except Exception as e:
        print(f"Error processing {url}: {str(e)}")
        return {"description": "", "images": []}

# Read CSV file
def read_csv_file(file_path):
    products = []
    with open(file_path, 'r', encoding='utf-8') as csvfile:
        reader = csv.reader(csvfile)
        # Skip header rows
        next(reader)  # Skip the first row (Haier)
        next(reader)  # Skip the second row (column headers)
        
        for row in reader:
            if len(row) >= 6 and row[2].strip():  # Check if model exists
                product = {
                    "classification": row[0].strip(),
                    "sap_code": row[1].strip(),
                    "model": row[2].strip(),
                    "mrp": row[3].strip().replace(",", ""),
                    "dp": row[4].strip().replace(",", "").replace('"', ''),
                    "mop": row[5].strip().replace(",", "").replace('"', '')
                }
                products.append(product)
    return products

# Main function
def main():
    # Read CSV file
    csv_products = read_csv_file('Haier KA.csv')
    
    # Fetch all products from API
    all_api_products = []
    page_no = 1
    total_pages = 1
    
    while page_no <= total_pages:
        api_data = fetch_haier_api_data(page_no)
        if api_data and 'page' in api_data:
            page_data = api_data['page']
            all_api_products.extend(page_data['data'])
            total_pages = page_data['totalPage']
            page_no += 1
        else:
            break
    
    # Match CSV products with API products
    matched_products = []
    
    for csv_product in csv_products:
        csv_model = csv_product['model']
        
        # Handle special case for models with variants
        base_model = csv_model.split('-')[0] if '-' in csv_model else csv_model
        if '(' in csv_model:
            base_model = csv_model.split('(')[0].strip()
        
        matched_api_product = None
        
        # Try to find exact match first
        for api_product in all_api_products:
            if api_product['modelno'] == csv_model:
                matched_api_product = api_product
                break
        
        # If no exact match, try partial match
        if not matched_api_product:
            for api_product in all_api_products:
                if base_model in api_product['modelno']:
                    matched_api_product = api_product
                    break
        
        if matched_api_product:
            # Fetch additional details from product page
            product_url = matched_api_product['docPubUrl']
            print(f"Fetching details for {csv_model} from {product_url}")
            additional_details = fetch_product_details(product_url)
            
            # Extract category from product name or description
            category = "Kitchen Appliance"
            product_name = matched_api_product['pname']
            
            if "Hood" in product_name:
                category = "Kitchen Appliance - Hood"
            elif "Hob" in product_name:
                category = "Kitchen Appliance - Hob"
            elif "Oven" in product_name:
                category = "Kitchen Appliance - Oven"
            elif "Microwave" in product_name:
                category = "Kitchen Appliance - Microwave"
            elif "Sterilizer" in product_name:
                category = "Kitchen Appliance - Sterilizer"
            
            # Format product data according to specified structure
            product_data = {
                "name": matched_api_product['pname'],
                "description": additional_details['description'],
                "price": float(csv_product['mrp']) if csv_product['mrp'] else matched_api_product['price'],
                "brand": "Haier",
                "category": category,
                "images": additional_details['images'],
                "model": matched_api_product['modelno'],
                "features": matched_api_product.get('ggdesc', '').split('|')
            }
            
            matched_products.append(product_data)
            
            # Add a small delay to avoid overwhelming the server
            time.sleep(1)
        else:
            print(f"No match found for {csv_model}")
    
    # Save the data to a JSON file
    with open('output/json/haier_kitchen_products.json', 'w', encoding='utf-8') as f:
        json.dump(matched_products, f, indent=2, ensure_ascii=False)
    
    print(f"Extracted data for {len(matched_products)} products")
    print(f"Data saved to output/json/haier_kitchen_products.json")

if __name__ == "__main__":
    main()
