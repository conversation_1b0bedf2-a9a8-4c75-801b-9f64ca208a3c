#!/usr/bin/env python3
"""
Setup script for Haier Image Scraper
Installs required dependencies and sets up Playwright
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed")
        print(f"Error: {e.stderr}")
        return False

def main():
    """Main setup function"""
    print("Setting up Haier Image Scraper...")
    
    # Install Python dependencies
    if not run_command("pip install playwright aiohttp aiofiles", "Installing Python dependencies"):
        print("Failed to install dependencies. Please install manually:")
        print("pip install playwright aiohttp aiofiles")
        return False
    
    # Install Playwright browsers
    if not run_command("playwright install chromium", "Installing Playwright Chromium browser"):
        print("Failed to install Playwright browser. Please install manually:")
        print("playwright install chromium")
        return False
    
    print("\n✓ Setup completed successfully!")
    print("\nYou can now run the scraper with:")
    print("python haier_image_scraper.py")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
