import { useSession } from "next-auth/react";
import { useState, useCallback, useRef } from "react";
import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

interface ApiResponse<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

interface UseApiReturn<T> extends ApiResponse<T> {
  create: (endpoint: string, body: unknown) => Promise<T | string | undefined>;
  read: (endpoint: string) => Promise<T | string | undefined>;
  update: (endpoint: string, body: unknown) => Promise<T | string | undefined>;
  remove: (endpoint: string) => Promise<T | string | undefined>;
}

// Simple cache implementation to prevent duplicate API calls
const apiCache: Record<string, { data: any; timestamp: number }> = {};
const CACHE_EXPIRY_MS = 60000; // Cache expires after 1 minute

const useApi = <T>(baseUrl: string): UseApiReturn<T> => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { data: session, status }: any = useSession();

  // Use a ref to track ongoing requests to prevent duplicate calls
  const pendingRequestsRef = useRef<Record<string, Promise<any>>>({});

  // Helper function to handle API responses and errors
  const handleAxiosResponse = (response: AxiosResponse<T>): T => {
    return response.data;
  };

  const handleAxiosError = (error: AxiosError): string => {
    if (error.response?.data) {
      const errorData = error.response.data as any;
      return errorData.message ||
             (errorData.email ? errorData.email[0] : null) ||
             JSON.stringify(errorData) ||
             "Something went wrong";
    }
    return error.message || "Something went wrong";
  };

  // Helper function to create config with headers and Authorization if authenticated
  const getAxiosConfig = (): AxiosRequestConfig => {
    const config: AxiosRequestConfig = {
      headers: {
        "Content-Type": "application/json",
      }
    };

    if (status === "authenticated") {
      config.headers = {
        ...config.headers,
        "Authorization": `Bearer ${session.user.access}`
      };
    }

    return config;
  };

  // Create (POST)
  const create = useCallback(
    async (
      endpoint: string,
      body: unknown
    ): Promise<T | string | undefined> => {
      setLoading(true);
      setError(null);
      try {
        // Make sure baseUrl doesn't end with null
        const cleanBaseUrl = baseUrl ? baseUrl.replace(/null$/, '') : '';

        // Split the endpoint into base path and query string
        const [basePath, queryString] = endpoint.split('?');

        // Ensure base path has trailing slash for Django URL compatibility
        const formattedBasePath = basePath.endsWith('/') ? basePath : `${basePath}/`;

        // Reconstruct the endpoint with properly formatted base path and query string
        const formattedEndpoint = queryString ? `${formattedBasePath}?${queryString}` : formattedBasePath;

        const url = `${cleanBaseUrl}${formattedEndpoint}`;
        const config = getAxiosConfig();

        const response = await axios.post(url, body, config);
        const result = handleAxiosResponse(response);
        setData(result);
        return result;
      } catch (err) {
        const errorMessage = err instanceof AxiosError
          ? handleAxiosError(err)
          : (err instanceof Error ? err.message : "Unknown error");
        setError(errorMessage);
        return errorMessage;
      } finally {
        setLoading(false);
      }
    },
    [baseUrl, session, status]
  );

  // Read (GET) with caching to prevent continuous API calls
  const read = useCallback(
    async (endpoint: string): Promise<T | string | undefined> => {
      setLoading(true);
      setError(null);

      try {
        // Make sure baseUrl doesn't end with null
        const cleanBaseUrl = baseUrl ? baseUrl.replace(/null$/, '') : '';

        // Split the endpoint into base path and query string
        const [basePath, queryString] = endpoint.split('?');

        // Ensure base path has trailing slash for Django URL compatibility
        const formattedBasePath = basePath.endsWith('/') ? basePath : `${basePath}/`;

        // Reconstruct the endpoint with properly formatted base path and query string
        const formattedEndpoint = queryString ? `${formattedBasePath}?${queryString}` : formattedBasePath;

        const url = `${cleanBaseUrl}${formattedEndpoint}`;
        const cacheKey = `${url}-${status === "authenticated" ? session?.user?.id : "anonymous"}`;

        // Determine if this endpoint should be cached
        // Don't cache cart-related endpoints to ensure fresh data
        const shouldCache = !endpoint.includes('/cart/');

        // Check if we have a valid cached response (only for cacheable endpoints)
        const cachedData = shouldCache ? apiCache[cacheKey] : null;
        const now = Date.now();

        if (cachedData && (now - cachedData.timestamp < CACHE_EXPIRY_MS)) {
          // Use cached data if it's still valid
          setData(cachedData.data);
          setLoading(false);
          return cachedData.data;
        }

        // Check if there's already a pending request for this URL
        // Only reuse pending requests for non-cart endpoints
        if (shouldCache && pendingRequestsRef.current[cacheKey]) {
          // Reuse the existing promise to avoid duplicate requests
          const result = await pendingRequestsRef.current[cacheKey];
          setData(result);
          return result;
        }

        // Create a new request and store it in pendingRequests
        const config = getAxiosConfig();
        // Pass shouldCache to the promise chain
        const requestPromise = axios.get(url, config)
          .then(response => {
            const result = handleAxiosResponse(response);

            // Cache the result only for non-cart endpoints
            if (shouldCache) {
              apiCache[cacheKey] = {
                data: result,
                timestamp: Date.now()
              };
            }

            // Remove from pending requests
            delete pendingRequestsRef.current[cacheKey];

            return result;
          })
          .catch(err => {
            // Remove from pending requests on error
            delete pendingRequestsRef.current[cacheKey];
            throw err;
          });

        // Store the promise only for non-cart endpoints
        if (shouldCache) {
          pendingRequestsRef.current[cacheKey] = requestPromise;
        }

        // Wait for the request to complete
        const result = await requestPromise;
        setData(result);
        return result;
      } catch (err) {
        const errorMessage = err instanceof AxiosError
          ? handleAxiosError(err)
          : (err instanceof Error ? err.message : "Unknown error");
        setError(errorMessage);
        return errorMessage;
      } finally {
        setLoading(false);
      }
    },
    [baseUrl, session, status]
  );

  // Update (PUT)
  const update = useCallback(
    async (
      endpoint: string,
      body: unknown
    ): Promise<T | string | undefined> => {
      setLoading(true);
      setError(null);
      try {
        const isFormData = body instanceof FormData; // Check if body is FormData
        // Make sure baseUrl doesn't end with null
        const cleanBaseUrl = baseUrl ? baseUrl.replace(/null$/, '') : '';

        // Split the endpoint into base path and query string
        const [basePath, queryString] = endpoint.split('?');

        // Ensure base path has trailing slash for Django URL compatibility
        const formattedBasePath = basePath.endsWith('/') ? basePath : `${basePath}/`;

        // Reconstruct the endpoint with properly formatted base path and query string
        const formattedEndpoint = queryString ? `${formattedBasePath}?${queryString}` : formattedBasePath;

        const url = `${cleanBaseUrl}${formattedEndpoint}`;
        const config = getAxiosConfig();

        // If it's FormData, we need to set the correct content type header
        if (isFormData) {
          config.headers = {
            ...config.headers,
            'Content-Type': 'multipart/form-data'
          };
        }

        const response = await axios.put(url, body, config);
        const result = handleAxiosResponse(response);
        setData(result);
        return result;
      } catch (err) {
        const errorMessage = err instanceof AxiosError
          ? handleAxiosError(err)
          : (err instanceof Error ? err.message : "Unknown error");
        setError(errorMessage);
        return errorMessage;
      } finally {
        setLoading(false);
      }
    },
    [baseUrl, session, status]
  );

  // Delete (DELETE)
  const remove = useCallback(
    async (endpoint: string): Promise<T | string | undefined> => {
      setLoading(true);
      setError(null);
      try {
        // Make sure baseUrl doesn't end with null
        const cleanBaseUrl = baseUrl ? baseUrl.replace(/null$/, '') : '';

        // Split the endpoint into base path and query string
        const [basePath, queryString] = endpoint.split('?');

        // Ensure base path has trailing slash for Django URL compatibility
        const formattedBasePath = basePath.endsWith('/') ? basePath : `${basePath}/`;

        // Reconstruct the endpoint with properly formatted base path and query string
        const formattedEndpoint = queryString ? `${formattedBasePath}?${queryString}` : formattedBasePath;

        const url = `${cleanBaseUrl}${formattedEndpoint}`;
        const config = getAxiosConfig();

        const response = await axios.delete(url, config);
        const result = handleAxiosResponse(response);
        setData(result);
        return result;
      } catch (err) {
        const errorMessage = err instanceof AxiosError
          ? handleAxiosError(err)
          : (err instanceof Error ? err.message : "Unknown error");
        setError(errorMessage);
        return errorMessage;
      } finally {
        setLoading(false);
      }
    },
    [baseUrl, session, status]
  );

  return {
    data,
    loading,
    error,
    create,
    read,
    update,
    remove,
  };
};

export default useApi;
