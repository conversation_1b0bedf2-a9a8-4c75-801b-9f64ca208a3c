#!/usr/bin/env python3
"""
Test email GST calculation fix - verify order confirmation email shows correct GST breakdown
"""

import os
import sys
import django
from decimal import Decimal

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

django.setup()

def test_email_gst_calculation_fix():
    """Test that order confirmation email shows correct GST calculations"""
    print("📧 Testing Email GST Calculation Fix...")
    
    from django.contrib.auth import get_user_model
    from orders.models import Order, OrderItem, ShippingMethod, Address
    from orders.utils import send_order_confirmation_email
    from products.models import Product, GST, Category, Brand
    from django.template.loader import render_to_string
    from decimal import Decimal
    
    User = get_user_model()
    
    try:
        # 1. Create test user
        user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'first_name': 'Email',
                'last_name': 'Test',
                'phone': '1234567890'
            }
        )
        print(f"✅ Test user: {user.email}")
        
        # 2. Create test GST rate (18%)
        test_gst, created = GST.objects.get_or_create(
            name="Test GST 18%",
            defaults={
                'rate': Decimal('18.00'),
                'cgst_rate': Decimal('9.00'),
                'sgst_rate': Decimal('9.00'),
                'igst_rate': Decimal('18.00'),
                'hsn_code': '8471',
                'is_active': True
            }
        )
        print(f"✅ Test GST: {test_gst.rate}%")
        
        # 3. Create test product with MRP ₹2270 (GST inclusive)
        category, created = Category.objects.get_or_create(
            name="Test Category Email",
            defaults={'slug': 'test-category-email'}
        )
        
        brand, created = Brand.objects.get_or_create(
            name="Test Brand Email",
            defaults={'slug': 'test-brand-email'}
        )
        
        product, created = Product.objects.get_or_create(
            slug="door-handle-email-test",
            defaults={
                'name': "Door Handle set Lock Body 2C Mortise Locks Mortise Locks",
                'description': "Test product for email GST calculation",
                'category': category,
                'brand': brand,
                'price': Decimal('2270.00'),  # MRP inclusive of 18% GST
                'gst': test_gst,
                'stock': 10,
                'is_active': True
            }
        )
        print(f"✅ Test product: {product.name}")
        print(f"   MRP (GST inclusive): ₹{product.price}")
        
        # 4. Create shipping method
        shipping_method, created = ShippingMethod.objects.get_or_create(
            name="Test Shipping",
            defaults={
                'description': 'Test shipping method',
                'price': Decimal('50.00'),
                'estimated_days': 3
            }
        )
        
        # 5. Create addresses
        shipping_address, created = Address.objects.get_or_create(
            user=user,
            defaults={
                'street_address': '123 Test Street',
                'city': 'Test City',
                'state': 'Test State',
                'postal_code': '123456',
                'country': 'India'
            }
        )
        
        billing_address = shipping_address
        
        # 6. Calculate expected values
        expected_base_price = Decimal('2270.00') / Decimal('1.18')
        expected_gst_amount = Decimal('2270.00') - expected_base_price
        expected_total = Decimal('2270.00') + Decimal('50.00')  # MRP + shipping
        
        print(f"\n📊 Expected Email Calculations:")
        print(f"   Base Price: ₹{expected_base_price:.2f}")
        print(f"   GST Amount: ₹{expected_gst_amount:.2f}")
        print(f"   Total (with shipping): ₹{expected_total:.2f}")
        
        # 7. Create test order with correct GST calculations
        order = Order.objects.create(
            user=user,
            shipping_address=shipping_address,
            billing_address=billing_address,
            shipping_method=shipping_method,
            subtotal=expected_base_price,  # Base price (GST exclusive)
            gst_amount=expected_gst_amount,
            cgst_amount=expected_gst_amount / 2,
            sgst_amount=expected_gst_amount / 2,
            igst_amount=Decimal('0.00'),
            shipping_cost=Decimal('50.00'),
            total=expected_total,
            status='PAID'
        )
        
        # 8. Create order item
        order_item = OrderItem.objects.create(
            order=order,
            product=product,
            quantity=1,
            unit_price=product.price,  # MRP
            total_price=product.price,  # MRP for 1 quantity
            product_name=product.name
        )
        
        print(f"\n✅ Test order created: #{order.id}")
        
        # 9. Test email context generation
        from orders.utils import send_order_confirmation_email
        
        # Get the context that would be used for email
        from orders.gst_service import gst_service
        
        item_gst_details = []
        for item in order.items.all():
            gst_breakdown = item.product.calculate_gst_breakdown_from_mrp(quantity=item.quantity)
            item_gst_details.append({
                'item': item,
                'base_price': gst_breakdown['unit_base_price'],
                'total_base_price': gst_breakdown['total_base_price'],
                'gst_rate': gst_breakdown['gst_rate'],
                'gst_amount': gst_breakdown['total_gst'],
                'cgst_amount': gst_breakdown['cgst_amount'],
                'sgst_amount': gst_breakdown['sgst_amount'],
                'igst_amount': gst_breakdown['igst_amount'],
                'total_with_gst': gst_breakdown['total_mrp']
            })
        
        context = {
            'order': order,
            'payment_method': 'PhonePe',
            'item_gst_details': item_gst_details,
            'payment_status': 'COMPLETED'
        }
        
        # 10. Render email template to test
        html_content = render_to_string('emails/order_confirmation.html', context)
        
        print(f"\n📧 Email Template Rendered Successfully!")
        
        # 11. Verify key values in rendered content
        gst_detail = item_gst_details[0]
        
        print(f"\n🧪 Email GST Details:")
        print(f"   Unit Price: ₹{gst_detail['item'].unit_price}")
        print(f"   Base Price: ₹{gst_detail['total_base_price']:.2f}")
        print(f"   GST Rate: {gst_detail['gst_rate']}%")
        print(f"   GST Amount: ₹{gst_detail['gst_amount']:.2f}")
        print(f"   Total with GST: ₹{gst_detail['total_with_gst']:.2f}")
        
        # 12. Check if values are correct
        base_correct = abs(float(gst_detail['total_base_price']) - float(expected_base_price)) < 0.01
        gst_correct = abs(float(gst_detail['gst_amount']) - float(expected_gst_amount)) < 0.01
        total_correct = abs(float(gst_detail['total_with_gst']) - 2270.00) < 0.01
        
        if base_correct and gst_correct and total_correct:
            print(f"\n🎉 Email GST Calculation Fix Successful!")
            print(f"   ✅ Email shows correct base price")
            print(f"   ✅ Email shows correct GST amount")
            print(f"   ✅ Email shows correct total")
            
            # Check if specific strings are in the rendered HTML
            if f"₹{expected_base_price:.2f}" in html_content:
                print(f"   ✅ Base price ₹{expected_base_price:.2f} found in email")
            if f"₹{expected_gst_amount:.2f}" in html_content:
                print(f"   ✅ GST amount ₹{expected_gst_amount:.2f} found in email")
            if f"₹{expected_total:.2f}" in html_content:
                print(f"   ✅ Total amount ₹{expected_total:.2f} found in email")
                
            return True
        else:
            print(f"\n❌ Email GST Calculation Issues:")
            if not base_correct:
                print(f"   ❌ Base price incorrect: got ₹{gst_detail['total_base_price']:.2f}, expected ₹{expected_base_price:.2f}")
            if not gst_correct:
                print(f"   ❌ GST amount incorrect: got ₹{gst_detail['gst_amount']:.2f}, expected ₹{expected_gst_amount:.2f}")
            if not total_correct:
                print(f"   ❌ Total incorrect: got ₹{gst_detail['total_with_gst']:.2f}, expected ₹2270.00")
            return False
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_email_gst_calculation_fix()
    
    if success:
        print("\n✅ SUMMARY: Email GST Calculation Fix Verified!")
        print("   🔹 Order confirmation email shows correct base price")
        print("   🔹 Email displays accurate GST breakdown")
        print("   🔹 Email template uses dynamic GST calculations")
        print("   🔹 No more incorrect totals in email confirmations")
    else:
        print("\n❌ Email GST calculation fix needs more work")
    
    sys.exit(0 if success else 1)
