["tests/test_api_optimization.py::TestAPIResponseOptimization::test_field_filtering", "tests/test_api_optimization.py::TestAPIResponseOptimization::test_gzip_compression", "tests/test_api_optimization.py::TestAPIResponseOptimization::test_pagination", "tests/test_configuration.py::TestConfiguration::test_email_configuration", "tests/test_configuration.py::TestConfiguration::test_email_sending", "tests/test_configuration.py::TestConfiguration::test_environment_variables", "tests/test_configuration.py::TestConfiguration::test_frontend_url_configuration", "tests/test_configuration.py::TestConfiguration::test_installed_apps", "tests/test_configuration.py::TestConfiguration::test_phonepe_configuration", "tests/test_email_notifications.py::TestEmailNotifications::test_contact_notification_email", "tests/test_email_notifications.py::TestEmailNotifications::test_email_error_handling", "tests/test_email_notifications.py::TestEmailNotifications::test_send_email", "tests/test_email_notifications.py::TestEmailNotifications::test_send_order_confirmation_email", "tests/test_email_notifications.py::TestEmailNotifications::test_send_payment_failure_email", "tests/test_email_notifications.py::TestEmailNotifications::test_send_payment_success_email", "tests/test_optimizations.py::TestCaching::test_active_categories_caching", "tests/test_optimizations.py::TestCaching::test_featured_products_caching", "tests/test_optimizations.py::TestDatabaseOptimization::test_order_detail_query_optimization", "tests/test_optimizations.py::TestDatabaseOptimization::test_product_list_query_optimization", "tests/test_order_processing.py::TestOrderProcessing::test_cancel_order", "tests/test_order_processing.py::TestOrderProcessing::test_create_order", "tests/test_order_processing.py::TestOrderProcessing::test_create_order_minimum_value", "tests/test_order_processing.py::TestOrderProcessing::test_get_order_details", "tests/test_order_processing.py::TestOrderProcessing::test_list_user_orders", "tests/test_order_processing.py::TestOrderProcessing::test_update_order_status", "tests/test_payment_processing.py::TestPaymentProcessing::test_initiate_payment", "tests/test_payment_processing.py::TestPaymentProcessing::test_initiate_payment_already_paid", "tests/test_payment_processing.py::TestPaymentProcessing::test_payment_callback_failure", "tests/test_payment_processing.py::TestPaymentProcessing::test_payment_callback_success", "tests/test_payment_processing.py::TestPaymentProcessing::test_webhook_processing", "tests/test_phonepe_service.py::TestPhonePeService::test_check_payment_status", "tests/test_phonepe_service.py::TestPhonePeService::test_check_payment_status_error", "tests/test_phonepe_service.py::TestPhonePeService::test_generate_transaction_id", "tests/test_phonepe_service.py::TestPhonePeService::test_get_client", "tests/test_phonepe_service.py::TestPhonePeService::test_get_client_error", "tests/test_phonepe_service.py::TestPhonePeService::test_initialization", "tests/test_phonepe_service.py::TestPhonePeService::test_initiate_payment", "tests/test_phonepe_service.py::TestPhonePeService::test_initiate_payment_error", "tests/test_stability_robustness.py::TestStabilityRobustness::test_concurrent_payment_processing", "tests/test_stability_robustness.py::TestStabilityRobustness::test_invalid_order_id_in_callback", "tests/test_stability_robustness.py::TestStabilityRobustness::test_invalid_signature_in_webhook", "tests/test_stability_robustness.py::TestStabilityRobustness::test_invalid_transaction_id_in_webhook", "tests/test_stability_robustness.py::TestStabilityRobustness::test_missing_order_id_in_callback", "tests/test_stability_robustness.py::TestStabilityRobustness::test_missing_signature_in_webhook", "tests/test_stability_robustness.py::TestStabilityRobustness::test_phonepe_payment_initiation_failure", "tests/test_stability_robustness.py::TestStabilityRobustness::test_phonepe_service_initialization_failure", "tests/test_transaction_management.py::TestTransactionManagement::test_optimistic_concurrency_control", "tests/test_transaction_management.py::TestTransactionManagement::test_order_cancellation_transaction", "tests/test_transaction_management.py::TestTransactionManagement::test_order_creation_transaction"]