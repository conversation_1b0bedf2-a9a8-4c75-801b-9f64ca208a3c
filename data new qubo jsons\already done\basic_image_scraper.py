import os
import json
import time
import requests
import random
from bs4 import BeautifulSoup
import logging
from PIL import Image
from io import BytesIO
import re
from urllib.parse import quote_plus, unquote

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("basic_image_scraper.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants
MAX_IMAGES_PER_PRODUCT = 5
OUTPUT_DIR = "output/images"
JSON_FILE_PATH = "output/json/product_data.json"

# User agents to rotate
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0"
]

# Create output directory structure
os.makedirs(OUTPUT_DIR, exist_ok=True)

def get_random_user_agent():
    """Return a random user agent from the list."""
    return random.choice(USER_AGENTS)

def download_image(url, product_dir, filename):
    """Download an image from URL and save it to the specified directory."""
    try:
        headers = {
            "User-Agent": get_random_user_agent(),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Referer": "https://www.google.com/"
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            # Verify it's an image
            try:
                img = Image.open(BytesIO(response.content))
                img_path = os.path.join(product_dir, filename)
                img.save(img_path)
                logger.info(f"Downloaded image: {img_path}")
                return img_path
            except Exception as e:
                logger.error(f"Not a valid image: {url}, Error: {str(e)}")
                return None
        else:
            logger.error(f"Failed to download image: {url}, Status code: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error downloading image: {url}, Error: {str(e)}")
        return None

def search_google_images(query):
    """Search for images on Google Images using requests and BeautifulSoup."""
    image_urls = []
    
    # Format the query for the URL
    formatted_query = quote_plus(query)
    
    # Create the Google Images search URL
    url = f"https://www.google.com/search?q={formatted_query}&tbm=isch"
    
    headers = {
        "User-Agent": get_random_user_agent(),
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5"
    }
    
    try:
        # Send the request
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            # Parse the HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find image elements
            img_tags = soup.find_all('img')
            
            # Extract image URLs
            for img in img_tags:
                # Skip the Google logo and small icons
                if img.get('src') and img.get('src').startswith('http') and not img.get('src').startswith('https://www.google.com'):
                    image_urls.append(img.get('src'))
            
            # If we didn't find enough images, try to extract from JSON data in the page
            if len(image_urls) < MAX_IMAGES_PER_PRODUCT:
                # Look for image URLs in script tags
                scripts = soup.find_all('script')
                for script in scripts:
                    if script.string and "var data = " in script.string:
                        # Extract URLs using regex
                        matches = re.findall(r'https?://\S+?\.(?:jpg|jpeg|png|gif|bmp|webp)', script.string)
                        for match in matches:
                            # Clean up the URL
                            clean_url = match.split('"')[0].split("'")[0].split('\\')[0]
                            if clean_url not in image_urls:
                                image_urls.append(clean_url)
            
            # Limit to the maximum number of images
            image_urls = image_urls[:MAX_IMAGES_PER_PRODUCT]
            
            logger.info(f"Found {len(image_urls)} images for query: {query}")
        else:
            logger.error(f"Failed to search Google Images. Status code: {response.status_code}")
    
    except Exception as e:
        logger.error(f"Error searching Google Images: {str(e)}")
    
    return image_urls

def search_bing_images(query):
    """Search for images on Bing Images using requests and BeautifulSoup."""
    image_urls = []
    
    # Format the query for the URL
    formatted_query = quote_plus(query)
    
    # Create the Bing Images search URL
    url = f"https://www.bing.com/images/search?q={formatted_query}&form=HDRSC2&first=1"
    
    headers = {
        "User-Agent": get_random_user_agent(),
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5"
    }
    
    try:
        # Send the request
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            # Parse the HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find image elements
            img_tags = soup.find_all('img', class_='mimg')
            
            # Extract image URLs
            for img in img_tags:
                if img.get('src') and img.get('src').startswith('http'):
                    image_urls.append(img.get('src'))
            
            # Limit to the maximum number of images
            image_urls = image_urls[:MAX_IMAGES_PER_PRODUCT]
            
            logger.info(f"Found {len(image_urls)} images on Bing for query: {query}")
        else:
            logger.error(f"Failed to search Bing Images. Status code: {response.status_code}")
    
    except Exception as e:
        logger.error(f"Error searching Bing Images: {str(e)}")
    
    return image_urls

def process_product(product):
    """Process a single product to find and download images."""
    # Create a unique search query for the product
    model_number = product.get("model_number", "")
    name = product.get("name", "")
    brand = product.get("brand", "")
    category = product.get("category", "")
    
    # Create search query with all relevant information
    search_query = f"{brand} {name} {model_number} {category}"
    
    # Create directory for product images
    product_dir = os.path.join(OUTPUT_DIR, f"{brand}_{category}_{model_number}")
    os.makedirs(product_dir, exist_ok=True)
    
    logger.info(f"Processing product: {name} (Model: {model_number})")
    
    # Search for images
    image_urls = []
    
    # Try Google Images first
    google_urls = search_google_images(search_query)
    image_urls.extend(google_urls)
    
    # If we need more images, try Bing
    if len(image_urls) < MAX_IMAGES_PER_PRODUCT:
        bing_urls = search_bing_images(search_query)
        for url in bing_urls:
            if url not in image_urls:
                image_urls.append(url)
                if len(image_urls) >= MAX_IMAGES_PER_PRODUCT:
                    break
    
    # Limit to the maximum number of images
    image_urls = image_urls[:MAX_IMAGES_PER_PRODUCT]
    
    # Download images
    image_paths = []
    for i, url in enumerate(image_urls):
        filename = f"{model_number}_{i+1}.jpg"
        img_path = download_image(url, product_dir, filename)
        if img_path:
            # Convert to relative path
            rel_path = os.path.relpath(img_path, os.getcwd())
            image_paths.append(rel_path)
    
    # Update product with image paths
    product["images"] = image_paths
    
    logger.info(f"Completed processing {name} (Model: {model_number}). Found {len(image_paths)} images.")
    
    # Add a small delay to avoid being blocked
    time.sleep(random.uniform(1.0, 3.0))
    
    return product

def main():
    """Main function to process all products."""
    logger.info("Starting basic image scraping process")
    
    # Load product data from JSON file
    try:
        with open(JSON_FILE_PATH, 'r', encoding='utf-8') as f:
            data = json.load(f)
            products = data.get("products", [])
            logger.info(f"Loaded {len(products)} products from {JSON_FILE_PATH}")
    except Exception as e:
        logger.error(f"Error loading product data: {str(e)}")
        return
    
    # Process products one by one
    updated_products = []
    for i, product in enumerate(products):
        try:
            updated_product = process_product(product)
            updated_products.append(updated_product)
            logger.info(f"Completed {i+1}/{len(products)} products")
        except Exception as e:
            logger.error(f"Error processing product: {str(e)}")
            # Add the original product to maintain data integrity
            updated_products.append(product)
    
    # Update the JSON file with new image paths
    try:
        data["products"] = updated_products
        with open(JSON_FILE_PATH, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2)
        logger.info(f"Updated {JSON_FILE_PATH} with image paths")
    except Exception as e:
        logger.error(f"Error updating JSON file: {str(e)}")
        
        # Save a backup in case the main file update fails
        try:
            backup_path = JSON_FILE_PATH.replace(".json", "_updated.json")
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2)
            logger.info(f"Saved backup to {backup_path}")
        except Exception as e:
            logger.error(f"Error saving backup file: {str(e)}")
    
    logger.info("Image scraping process completed")

if __name__ == "__main__":
    main()
