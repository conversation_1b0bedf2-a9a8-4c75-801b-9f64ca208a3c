#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to fix MinIO object paths by removing duplicate 'media/' prefixes.
This script will:
1. List all objects in the MinIO bucket
2. Identify objects with duplicate 'media/' prefixes
3. Copy them to the correct path
4. Optionally delete the original objects with incorrect paths
"""

import os
import sys
import argparse
import logging
from pathlib import Path
import boto3
from botocore.client import Config
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('minio-path-fix')

# Load environment variables
load_dotenv()

def get_env_or_default(key, default):
    """Get environment variable or return default value"""
    return os.environ.get(key, default)

def fix_minio_paths(dry_run=True, delete_originals=False, list_all=False):
    """
    Fix MinIO object paths by removing duplicate 'media/' prefixes

    Args:
        dry_run: If True, only show what would be done without making changes
        delete_originals: If True, delete original objects after copying
        list_all: If True, list all objects in the bucket
    """
    # Get MinIO configuration from environment variables
    endpoint = get_env_or_default('MINIO_STORAGE_ENDPOINT', 'minio:9000')
    access_key = get_env_or_default('MINIO_STORAGE_ACCESS_KEY', 'minioadmin')
    secret_key = get_env_or_default('MINIO_STORAGE_SECRET_KEY', 'minioadmin')
    use_https = get_env_or_default('MINIO_STORAGE_USE_HTTPS', 'False').lower() == 'true'
    bucket_name = get_env_or_default('MINIO_STORAGE_MEDIA_BUCKET_NAME', 'media')

    # Log configuration for debugging (without sensitive info)
    logger.info(f"MinIO Configuration: Endpoint={endpoint}, HTTPS={use_https}, Bucket={bucket_name}")
    logger.info(f"Mode: {'DRY RUN' if dry_run else 'LIVE'}, Delete originals: {delete_originals}")

    # Create S3 client for MinIO
    s3_client = boto3.client(
        's3',
        endpoint_url=f"{'https' if use_https else 'http'}://{endpoint}",
        aws_access_key_id=access_key,
        aws_secret_access_key=secret_key,
        config=Config(
            signature_version='s3v4',
            s3={'addressing_style': 'path'}
        ),
        verify=False,  # Disable SSL verification for self-signed certificates
        region_name='us-east-1',  # MinIO doesn't require a specific region
    )

    # Suppress SSL warnings
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    # Check if bucket exists
    try:
        s3_client.head_bucket(Bucket=bucket_name)
        logger.info(f"Bucket '{bucket_name}' exists")
    except Exception as e:
        logger.error(f"Bucket check error: {str(e)}")
        logger.error(f"Cannot access bucket '{bucket_name}'. Exiting.")
        return

    # Get list of all objects in the bucket
    all_objects = []
    try:
        paginator = s3_client.get_paginator('list_objects_v2')
        for page in paginator.paginate(Bucket=bucket_name):
            if 'Contents' in page:
                for obj in page['Contents']:
                    all_objects.append(obj['Key'])

        logger.info(f"Found {len(all_objects)} total objects in bucket")

        # List all objects if requested
        if list_all:
            logger.info("Listing all objects in bucket:")
            for key in all_objects:
                logger.info(f"  {key}")
    except Exception as e:
        logger.error(f"Error listing objects in bucket: {e}")
        return

    # Identify objects that need fixing
    objects_to_fix = []
    for key in all_objects:
        new_key = None
        needs_fix = False

        # Check for duplicate 'media/' prefix
        if key.startswith('media/media/'):
            new_key = key[6:]  # Remove the first 'media/' prefix
            needs_fix = True

        if needs_fix and new_key:
            objects_to_fix.append((key, new_key))

    logger.info(f"Found {len(objects_to_fix)} objects with paths that need fixing")

    # Process objects
    fixed_count = 0
    error_count = 0

    for old_key, new_key in objects_to_fix:
        logger.info(f"Processing: {old_key} -> {new_key}")

        if dry_run:
            logger.info(f"[DRY RUN] Would copy {old_key} to {new_key}")
            fixed_count += 1
            continue

        try:
            # Copy object to new location
            copy_source = {'Bucket': bucket_name, 'Key': old_key}
            s3_client.copy_object(
                CopySource=copy_source,
                Bucket=bucket_name,
                Key=new_key,
                MetadataDirective='COPY',  # Copy all metadata
                ACL='public-read'  # Ensure public read access
            )

            logger.info(f"Successfully copied {old_key} to {new_key}")

            # Delete original if requested
            if delete_originals:
                s3_client.delete_object(Bucket=bucket_name, Key=old_key)
                logger.info(f"Deleted original object {old_key}")

            fixed_count += 1
        except Exception as e:
            logger.error(f"Error processing {old_key}: {e}")
            error_count += 1

    # Log summary
    logger.info(f"Fix summary: {len(objects_to_fix)} objects found, {fixed_count} fixed, {error_count} errors")
    if dry_run:
        logger.info("This was a dry run. No changes were made. Run with --live to apply changes.")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Fix MinIO object paths by removing duplicate media/ prefixes')
    parser.add_argument('--live', action='store_true', help='Apply changes (default is dry run)')
    parser.add_argument('--delete-originals', action='store_true', help='Delete original objects after copying')
    parser.add_argument('--list-all', action='store_true', help='List all objects in the bucket')

    args = parser.parse_args()

    try:
        logger.info("Starting MinIO path fix")
        fix_minio_paths(dry_run=not args.live, delete_originals=args.delete_originals, list_all=args.list_all)
        logger.info("MinIO path fix completed")
    except Exception as e:
        logger.error(f"Error during MinIO path fix: {str(e)}")
        sys.exit(1)
