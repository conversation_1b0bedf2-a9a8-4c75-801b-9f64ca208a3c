// "use client";
// import * as React from "react";
// import Link from "next/link";

// const categories = [
//   {
//     title: "SALE",
//     href: "/sale",
//   },
//   {
//     title: "NEW IN",
//     href: "/new-in",
//   },
//   {
//     title: "WATCHES",
//     href: "/watches",
//     items: [
//       { title: "All Watches", href: "/watches" },
//       { title: "Women's Watches", href: "/watches/women" },
//       { title: "Men's Watches", href: "/watches/men" },
//     ],
//   },
//   {
//     title: "SUNGLASSES",
//     href: "/sunglasses",
//   },
//   {
//     title: "WATCH CHARMS",
//     href: "/watch-charms",
//   },
//   {
//     title: "COUPLE WATCHES",
//     href: "/couple-watches",
//   },
//   {
//     title: "LOVE TRIANGLES & STACKS",
//     href: "/love-triangles-stacks",
//   },
//   {
//     title: "WATCH BRACELET STACKS",
//     href: "/watch-bracelet-stacks",
//     items: [
//       { title: "All Bracelet Stacks", href: "/watch-bracelet-stacks" },
//       {
//         title: "Women's Bracelet Stacks",
//         href: "/watch-bracelet-stacks/women",
//       },
//       { title: "Men's Bracelet Stacks", href: "/watch-bracelet-stacks/men" },
//     ],
//   },
//   {
//     title: "JEWELLERY",
//     href: "/jewellery",
//   },
// ];

// export default function Navigation({ categories }: Category[]) {
//   const [openDropdown, setOpenDropdown] = React.useState<string | null>(null);
//   const timeoutRef = React.useRef<any>(null);

//   const handleMouseEnter = (categoryTitle: string) => {
//     if (timeoutRef.current) clearTimeout(timeoutRef.current);
//     setOpenDropdown(categoryTitle);
//   };

//   const handleMouseLeave = () => {
//     timeoutRef.current = setTimeout(() => {
//       setOpenDropdown(null);
//     }, 150); // Small delay before closing
//   };

//   return (
//     <nav className="w-full">
//       <ul className="flex flex-wrap justify-center gap-1 md:gap-2">
//         {categories.map((category: Category) => (
//           <li
//             key={category?.name}
//             className="relative"
//             onMouseEnter={() => handleMouseEnter(category?.name)}
//             onMouseLeave={handleMouseLeave}
//           >
//             {Array.isArray(category?.subcategories) &&
//             Boolean(category?.subcategories.length > 0) ? (
//               <>
//                 <button className="inline-flex h-9 items-center justify-center rounded-md bg-background px-4 py-2 text-xs md:text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none">
//                   {category?.name}
//                 </button>
//                 {openDropdown === category?.name && (
//                   <div
//                     className="absolute left-1/2 top-full z-50 -translate-x-1/2 transform"
//                     onMouseEnter={() => handleMouseEnter(category?.name)}
//                     onMouseLeave={handleMouseLeave}
//                   >
//                     {/* Added invisible bridge to prevent gap between button and dropdown */}
//                     <div className="h-2 -mt-1" />
//                     <ul className="min-w-[12rem] rounded-md border bg-white p-2 shadow-lg">
//                       {Array.isArray(category?.subcategories) &&
//                         Boolean(category?.subcategories.length > 0) &&
//                         category?.subcategories.map((subcategory) => (
//                           <li key={subcategory.name}>
//                             <Link
//                               href={`/products/categories/${subcategory.slug}`}
//                               className="block rounded-md px-4 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
//                             >
//                               {subcategory.name}
//                             </Link>
//                           </li>
//                         ))}
//                     </ul>
//                   </div>
//                 )}
//               </>
//             ) : (
//               <Link
//                 href={`/products/categories/${category.slug}`}
//                 className="inline-flex h-9 items-center justify-center rounded-md bg-background px-4 py-2 text-xs md:text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none"
//               >
//                 {category.name}
//               </Link>
//             )}
//           </li>
//         ))}
//       </ul>
//     </nav>
//   );
// }


"use client";
import * as React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Skeleton } from "@/components/ui/skeleton";
import axios from "axios";

export default function Navigation({ categories }: any) {
  const [openDropdown, setOpenDropdown] = React.useState<string | null>(null);
  const timeoutRef = React.useRef<any>(null);
  const isComponentMounted = React.useRef(false);
  const pathName = usePathname();

  React.useEffect(() => {
    isComponentMounted.current = true;

    return () => {
      isComponentMounted.current = false;
    };
  }, []);

  const handleMouseEnter = (categoryTitle: string) => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);

    if (isComponentMounted.current) {
      setOpenDropdown(categoryTitle);
    }
  };

  const handleMouseLeave = () => {
    if (isComponentMounted.current) {
      timeoutRef.current = setTimeout(() => {
        if (isComponentMounted.current) {
          setOpenDropdown(null);
        }
      }, 150); // Small delay before closing
    }
  };

  // State to track categories with products
  const [categoriesWithProducts, setCategoriesWithProducts] = React.useState<any[]>([]);
  const [isLoading, setIsLoading] = React.useState(true);

  // Cache for storing category product status to avoid redundant API calls
  const categoryProductCache = React.useRef<Record<string, boolean>>({});

  // Function to check if a category or subcategory has products with caching
  const checkForProducts = async (slug: string) => {
    // Check cache first
    if (categoryProductCache.current[slug] !== undefined) {
      return categoryProductCache.current[slug];
    }

    try {
      // Import the API URL constants
      const { CATEGORIZE_PRODUCTS, MAIN_URL } = await import("@/constant/urls");

      // Make an axios request to check if the category/subcategory has products
      const response = await axios.get(`${MAIN_URL}${CATEGORIZE_PRODUCTS(slug)}?page_size=1`);
      const data = response.data;

      // Check if there are products - ensure count is greater than 0 and products array exists and is not empty
      const hasProducts =
        (data?.count > 0) &&
        Array.isArray(data?.results?.products) &&
        data.results.products.length > 0;

      // Cache the result
      categoryProductCache.current[slug] = hasProducts;

      return hasProducts;
    } catch (error) {
      console.error(`Error checking products for slug ${slug}:`, error);
      if (axios.isAxiosError(error)) {
        console.error('Axios error details:', error.response?.data);
      }
      return false;
    }
  };

  // Batch process categories to reduce API load
  const processCategoriesInBatches = async (categories: any[]) => {
    const batchSize = 3; // Process 3 categories at a time
    const results = [];

    for (let i = 0; i < categories.length; i += batchSize) {
      const batch = categories.slice(i, i + batchSize);
      const batchResults = await Promise.all(
        batch.map(async (category) => {
          // Check if the category has products directly
          const categoryHasProducts = await checkForProducts(category.slug);

          // If the category has subcategories, check each one
          let subcategoriesWithProducts = [];
          if (Array.isArray(category.subcategories) && category.subcategories.length > 0) {
            // Process subcategories in smaller batches
            const subBatchSize = 5;
            const subResults = [];

            for (let j = 0; j < category.subcategories.length; j += subBatchSize) {
              const subBatch = category.subcategories.slice(j, j + subBatchSize);
              const subBatchResults = await Promise.all(
                subBatch.map(async (subcategory: any) => {
                  const hasProducts = await checkForProducts(subcategory.slug);
                  return hasProducts ? subcategory : null;
                })
              );
              subResults.push(...subBatchResults);
            }

            // Filter out null values (subcategories without products)
            subcategoriesWithProducts = subResults.filter(Boolean);
          }

          // If the category has products directly or has subcategories with products, include it
          if ((categoryHasProducts === true) || (subcategoriesWithProducts.length > 0)) {
            // Double check that we have actual products before including this category
            console.log(`Category ${category.name}: has products directly: ${categoryHasProducts}, subcategories with products: ${subcategoriesWithProducts.length}`);

            // Return the category with filtered subcategories and hasProducts flag
            return {
              ...category,
              hasProducts: categoryHasProducts,
              subcategories: subcategoriesWithProducts
            };
          }

          // Otherwise, don't include this category
          return null;
        })
      );

      results.push(...batchResults);
    }

    return results;
  };

  // Effect to check all categories and their subcategories for products
  React.useEffect(() => {
    const fetchCategoriesWithProducts = async () => {
      if (!Array.isArray(categories) || categories.length === 0) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);

      try {
        // Check if we have cached categories in sessionStorage
        if (typeof window !== 'undefined') {
          const cachedData = sessionStorage.getItem('navigationCategories');
          const cachedTimestamp = sessionStorage.getItem('navigationCategoriesTimestamp');

          // Use cache if it exists and is less than 5 minutes old
          if (cachedData && cachedTimestamp) {
            const timestamp = parseInt(cachedTimestamp, 10);
            const now = Date.now();
            const fiveMinutesInMs = 5 * 60 * 1000;

            if (now - timestamp < fiveMinutesInMs) {
              try {
                const parsedData = JSON.parse(cachedData);

                // Verify that the cached data contains categories with products
                if (Array.isArray(parsedData) && parsedData.length > 0) {
                  // Extra validation to ensure cached categories have products
                  const validCachedCategories = parsedData.filter(category => {
                    const hasSubcategoriesWithProducts = Array.isArray(category.subcategories) && category.subcategories.length > 0;
                    return hasSubcategoriesWithProducts || category.hasProducts === true;
                  });

                  if (validCachedCategories.length > 0) {
                    setCategoriesWithProducts(validCachedCategories);
                    setIsLoading(false);
                    return;
                  } else {
                    // Clear invalid cache
                    sessionStorage.removeItem('navigationCategories');
                    sessionStorage.removeItem('navigationCategoriesTimestamp');
                  }
                } else {
                  // Clear invalid cache
                  sessionStorage.removeItem('navigationCategories');
                  sessionStorage.removeItem('navigationCategoriesTimestamp');
                }
              } catch (e) {
                console.error("Error parsing cached categories:", e);
                // Continue with normal loading if parsing fails
              }
            }
          }
        }

        // Process categories in batches to reduce concurrent API calls
        const processedCategories = await processCategoriesInBatches(categories);

        // Filter out null values (categories without products)
        const validCategories = processedCategories.filter(Boolean);

        // Extra check to ensure we're not displaying categories with no products
        const finalCategories = validCategories.filter(category => {
          // If this is a category with no products and no subcategories with products, filter it out
          const hasSubcategoriesWithProducts = Array.isArray(category.subcategories) && category.subcategories.length > 0;
          return hasSubcategoriesWithProducts || category.hasProducts === true;
        });

        console.log(`Filtered categories: ${validCategories.length} -> ${finalCategories.length}`);

        setCategoriesWithProducts(finalCategories);

        // Cache the results in sessionStorage
        if (typeof window !== 'undefined') {
          try {
            // Only cache if we have valid categories with products
            if (finalCategories.length > 0) {
              sessionStorage.setItem('navigationCategories', JSON.stringify(finalCategories));
              sessionStorage.setItem('navigationCategoriesTimestamp', Date.now().toString());
            } else {
              // Clear cache if we don't have valid categories
              sessionStorage.removeItem('navigationCategories');
              sessionStorage.removeItem('navigationCategoriesTimestamp');
            }
          } catch (e) {
            console.error("Error caching categories:", e);
          }
        }
      } catch (error) {
        console.error("Error processing categories:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategoriesWithProducts();
  }, [categories]);

  // Show skeleton loader when categories are loading
  if (isLoading) {
    return (
      <nav className="w-full min-h-[40px]"> {/* Set minimum height to prevent layout shift */}
        <ul className="flex flex-wrap justify-center gap-1 md:gap-2">
          {Array.from({ length: 6 }).map((_, index) => (
            <li key={index} className="relative">
              <div className="inline-flex h-10 items-center justify-center rounded-md bg-white/80 backdrop-blur-sm px-4 py-2 shadow-sm border border-gray-100">
                <Skeleton className="w-16 h-4" />
              </div>
              {/* No dropdown skeletons by default */}
            </li>
          ))}
        </ul>
      </nav>
    );
  }

  // Don't render navigation if no categories with products
  if (!categoriesWithProducts || categoriesWithProducts.length === 0) {
    return null;
  }

  return (
    <nav className="w-full">
      <ul className="flex flex-wrap justify-center gap-1 md:gap-2">
        {categoriesWithProducts.map((category:any) => (
          <li
            key={category?.name}
            className="relative"
            onMouseEnter={() => handleMouseEnter(category?.name)}
            onMouseLeave={handleMouseLeave}
          >
            {Array.isArray(category?.subcategories) &&
            Boolean(category?.subcategories.length > 0) ? (
              <>
                <Link
                  href={`/products/categories/${category.slug}?callbackUrl=%2F${pathName}`}
                  className="inline-flex h-10 items-center justify-center rounded-md bg-white/80 backdrop-blur-sm px-4 py-2 text-xs md:text-sm font-medium transition-all duration-300 hover:bg-gray-100 hover:text-gray-900 hover:shadow-md hover:-translate-y-[1px] focus:bg-gray-100 focus:text-gray-900 focus:outline-none shadow-sm border border-gray-100"
                >
                  {category.name}
                </Link>
                {openDropdown === category?.name && (
                  <div
                    className="absolute left-1/2 top-full z-50 -translate-x-1/2 transform"
                    onMouseEnter={() => handleMouseEnter(category?.name)}
                    onMouseLeave={handleMouseLeave}
                  >
                    {/* Added invisible bridge to prevent gap between button and dropdown */}
                    <div className="h-2 -mt-1" />
                    <ul className="min-w-[12rem] rounded-md border bg-white p-2 shadow-lg">
                      {/* Subcategories are already filtered to only include those with products */}
                      {category?.subcategories.map((subcategory:any) => (
                        <li key={subcategory.name}>
                          <Link
                            href={`/products/categories/${subcategory.slug}?callbackUrl=%2F${pathName}`}
                            className="block rounded-md px-4 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
                          >
                            {subcategory.name}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </>
            ) : (
              <Link
                href={`/products/categories/${category.slug}?callbackUrl=%2F${pathName}`}
                className="inline-flex h-9 items-center justify-center rounded-md bg-background px-4 py-2 text-xs md:text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none"
              >
                {category.name}
              </Link>
            )}
          </li>
        ))}
      </ul>
    </nav>
  );
}