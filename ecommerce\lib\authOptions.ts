import { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import Cred<PERSON><PERSON><PERSON>rovider from "next-auth/providers/credentials";
import { MAIN_URL, TOKEN_REFFRESH, USER_SOCIAL_LOGIN } from "../constant/urls";
import axios from "axios";

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || process.env.GOOGLE_CLIENT_ID || "",
      clientSecret: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET || process.env.GOOGLE_CLIENT_SECRET || "",
    }),
    CredentialsProvider({
      // Configure to use the custom API response
      name: "Credentials",
      credentials: {},
      async authorize(credentials) {
        try {
          const data: any = { ...credentials };
          if (data) {
            const user = {
              id: data.id || "",
              email: data.email || "",
              name: data.name || "",
              access: data.accessToken || "",
              refresh: data.refreshToken || "",
              phone: data.phone_number,
              dob: data.date_of_birth,
            };
            return user;
          }
        } catch (error) {
          console.error("Error in authorize:", error);
          return null;
        }
        return null;
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account, profile }: any) {
      if (account?.provider === "google") {
        try {
          // Show loading state in the console
          console.log("Google sign-in in progress...");
          console.log("Google profile data:", profile);

          // Send Google user data to your backend API
          const res = await axios.post(`${MAIN_URL}${USER_SOCIAL_LOGIN}`, {
            email: user.email,
            name: user.name,
            image_url: user.image || profile?.picture,
            provider: "google",
          }, {
            headers: { "Content-Type": "application/json" }
          });

          if (res.status === 200) {
            const data = res.data;
            console.log("Social login success response:", data);

            // Attach custom tokens to user object
            user.access = data.accessToken;
            user.refresh = data.refreshToken;
            user.phone = data.phone_number;
            user.dob = data.date_of_birth;
            user.id = Number(data.id);
            user.email = data.email;
            user.name = data.name;

            console.log("Google sign-in successful, redirecting...");
            return true;
          } else {
            console.error("Backend login failed with status:", res.status);
            return false;
          }
        } catch (error) {
          console.error("Error during Google sign-in:", error);
          if (axios.isAxiosError(error)) {
            console.error("Axios error details:", error.response?.data);
          }
          return false;
        }
      }
      return true;
    },
    async jwt({ token, user }: any) {
      if (user) {
        token.access = user.access;
        token.refresh = user.refresh;
        token.phone = user.phone;
        token.dob = user.dob;
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;
        token.accessTokenExpires = Date.now() + 60 * 60 * 1000;
      }

      if (Date.now() < token.accessTokenExpires) {
        return token;
      }

      // Access token expired, try to update it
      return refreshAccessToken(token);
    },
    async session({ session, token }: any) {
      session.user = {
        ...session.user,
        access: token.access,
        refresh: token.refresh,
        phone: token.phone,
        dob: token.dob,
        id: token.id,
        email: token.email ?? "",
        name: token.name ?? "",
      };
      return session;
    },
  },
  pages: {
    signIn: "/auth/login",
  },
};

async function refreshAccessToken(token: any) {
  try {
    const res = await axios.post(MAIN_URL + TOKEN_REFFRESH,
      { refresh: token.refresh },
      { headers: { "Content-Type": "application/json" } }
    );

    const refreshedTokens = res.data;

    return {
      ...token,
      access: refreshedTokens.access,
      accessTokenExpires: Date.now() + 60 * 60 * 1000, // 1 hour from now
    };
  } catch (error) {
    console.error("Error refreshing access token:", error);
    if (axios.isAxiosError(error)) {
      console.error("Axios error details:", error.response?.data);
    }
    return { ...token, error: "RefreshAccessTokenError" };
  }
}
