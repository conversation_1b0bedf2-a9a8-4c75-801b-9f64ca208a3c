#!/usr/bin/env python3
"""
Test script for dynamic GST functionality using SQLite database.
"""

import os
import sys
import django
from decimal import Decimal

# Setup Django environment with SQLite
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Override database settings to use SQLite for testing
os.environ['DATABASE_URL'] = 'sqlite:///test_gst.db'

django.setup()

from django.db import connection
from django.contrib.auth import get_user_model
from products.models import Product, Category, Brand, GST
from orders.models import Order, OrderItem, ShippingMethod, Cart, CartItem
from orders.gst_service import gst_service
from orders.invoice_service import invoice_service
from orders.serializers import CartSerializer
from products.serializers import ProductSerializer
from users.models import Address

User = get_user_model()

def setup_test_database():
    """Setup test database with required tables"""
    print("🔧 Setting up test database...")
    
    # Run migrations to create tables
    from django.core.management import execute_from_command_line
    execute_from_command_line(['manage.py', 'migrate', '--run-syncdb'])
    
    print("✅ Database tables created")

def test_gst_models():
    """Test GST model creation and functionality"""
    print("\n📊 Testing GST Models...")
    
    # Create different GST rates
    electronics_gst = GST.objects.create(
        name="Electronics",
        rate=Decimal('18.00'),
        cgst_rate=Decimal('9.00'),
        sgst_rate=Decimal('9.00'),
        igst_rate=Decimal('18.00'),
        hsn_code='8471',
        description='GST for electronics products',
        is_active=True
    )
    
    books_gst = GST.objects.create(
        name="Books",
        rate=Decimal('5.00'),
        cgst_rate=Decimal('2.50'),
        sgst_rate=Decimal('2.50'),
        igst_rate=Decimal('5.00'),
        hsn_code='4901',
        description='GST for books',
        is_active=True
    )
    
    clothing_gst = GST.objects.create(
        name="Clothing",
        rate=Decimal('12.00'),
        cgst_rate=Decimal('6.00'),
        sgst_rate=Decimal('6.00'),
        igst_rate=Decimal('12.00'),
        hsn_code='6109',
        description='GST for clothing',
        is_active=True
    )
    
    print(f"✅ Created GST rates:")
    print(f"   Electronics: {electronics_gst.rate}%")
    print(f"   Books: {books_gst.rate}%")
    print(f"   Clothing: {clothing_gst.rate}%")
    
    return electronics_gst, books_gst, clothing_gst

def test_products_with_gst(electronics_gst, books_gst, clothing_gst):
    """Test product creation with different GST rates"""
    print("\n🛍️ Testing Products with GST...")
    
    # Create categories
    electronics_category = Category.objects.create(
        name="Electronics",
        slug='electronics',
        description='Electronic products'
    )
    
    books_category = Category.objects.create(
        name="Books",
        slug='books',
        description='Books and educational materials'
    )
    
    clothing_category = Category.objects.create(
        name="Clothing",
        slug='clothing',
        description='Clothing and fashion'
    )
    
    # Create brand
    test_brand = Brand.objects.create(
        name="Test Brand",
        slug='test-brand',
        description='Test brand for GST testing'
    )
    
    # Create products with different GST rates
    laptop = Product.objects.create(
        name="Test Laptop",
        slug="test-laptop",
        description="A test laptop for GST testing",
        category=electronics_category,
        brand=test_brand,
        price=Decimal('59000.00'),  # MRP inclusive of 18% GST
        gst=electronics_gst,
        stock=10,
        is_active=True
    )
    
    book = Product.objects.create(
        name="Test Book",
        slug="test-book",
        description="A test book for GST testing",
        category=books_category,
        brand=test_brand,
        price=Decimal('525.00'),  # MRP inclusive of 5% GST
        gst=books_gst,
        stock=50,
        is_active=True
    )
    
    tshirt = Product.objects.create(
        name="Test T-Shirt",
        slug="test-tshirt",
        description="A test t-shirt for GST testing",
        category=clothing_category,
        brand=test_brand,
        price=Decimal('1120.00'),  # MRP inclusive of 12% GST
        gst=clothing_gst,
        stock=25,
        is_active=True
    )
    
    print(f"✅ Created products:")
    print(f"   Laptop: ₹{laptop.price} (GST: {laptop.get_gst_rate().rate}%)")
    print(f"   Book: ₹{book.price} (GST: {book.get_gst_rate().rate}%)")
    print(f"   T-Shirt: ₹{tshirt.price} (GST: {tshirt.get_gst_rate().rate}%)")
    
    return laptop, book, tshirt

def test_product_serializers(laptop, book, tshirt):
    """Test product serializers include GST information"""
    print("\n🔍 Testing Product Serializers...")
    
    for product in [laptop, book, tshirt]:
        serializer = ProductSerializer(product)
        data = serializer.data
        
        print(f"\n   {data['name']}:")
        print(f"     MRP: ₹{data['price']}")
        print(f"     Base Price: ₹{data['base_price']}")
        print(f"     GST Rate: {data['gst_rate']}%")
        print(f"     GST Amount: ₹{data['gst_amount']}")
    
    print("✅ Product serializers working correctly")

def test_cart_with_mixed_gst(laptop, book, tshirt):
    """Test cart functionality with products having different GST rates"""
    print("\n🛒 Testing Cart with Mixed GST Rates...")
    
    # Create test user
    user = User.objects.create_user(
        username='testuser',
        email='<EMAIL>',
        first_name='Test',
        last_name='User'
    )
    
    # Create cart
    cart = Cart.objects.create(user=user)
    
    # Add items to cart
    CartItem.objects.create(cart=cart, product=laptop, quantity=1)
    CartItem.objects.create(cart=cart, product=book, quantity=2)
    CartItem.objects.create(cart=cart, product=tshirt, quantity=3)
    
    # Test cart serializer with GST breakdown
    cart_serializer = CartSerializer(cart)
    cart_data = cart_serializer.data
    
    print(f"   Cart Items: {len(cart_data['items'])}")
    
    if 'gst_breakdown' in cart_data:
        gst_breakdown = cart_data['gst_breakdown']
        print(f"   Cart GST Breakdown:")
        print(f"     Subtotal (base): ₹{gst_breakdown['subtotal']}")
        print(f"     Total MRP: ₹{gst_breakdown['total_mrp']}")
        print(f"     Total GST: ₹{gst_breakdown['total_gst_amount']}")
        print(f"     CGST: ₹{gst_breakdown['total_cgst_amount']}")
        print(f"     SGST: ₹{gst_breakdown['total_sgst_amount']}")
        
        print(f"\n   Item-wise GST breakdown:")
        for item_detail in gst_breakdown['item_details']:
            product = item_detail['product']
            print(f"     {product['name']} (x{item_detail['quantity']}): {item_detail['gst_rate']}% = ₹{item_detail['gst_amount']}")
        
        print("✅ Cart GST breakdown working correctly")
    else:
        print("❌ GST breakdown not found in cart data")
    
    return user, cart

def test_invoice_generation(user):
    """Test invoice generation restrictions for paid orders only"""
    print("\n🧾 Testing Invoice Generation...")
    
    # Create required objects
    address = Address.objects.create(
        user=user,
        street_address='123 Test Street',
        city='Test City',
        state='Test State',
        postal_code='123456',
        country='India',
        is_default=True
    )
    
    shipping_method = ShippingMethod.objects.create(
        name="Standard Shipping",
        description='Standard shipping method',
        price=Decimal('50.00'),
        estimated_days=5,
        is_active=True
    )
    
    # Create order with PENDING status
    order = Order.objects.create(
        user=user,
        status='PENDING',
        shipping_address=address,
        billing_address=address,
        shipping_method=shipping_method,
        subtotal=Decimal('1000.00'),
        gst_amount=Decimal('180.00'),
        cgst_amount=Decimal('90.00'),
        sgst_amount=Decimal('90.00'),
        igst_amount=Decimal('0.00'),
        shipping_cost=shipping_method.price,
        total=Decimal('1230.00')
    )
    
    print(f"   Created order {order.id} with status: {order.status}")
    
    # Test invoice generation for PENDING order (should fail)
    print("   Testing invoice generation for PENDING order...")
    try:
        invoice = invoice_service.generate_invoice(order)
        print("❌ ERROR: Invoice was generated for PENDING order (should have failed)")
        return False
    except ValueError as e:
        print(f"✅ Correctly blocked invoice generation: {str(e)[:50]}...")
    
    # Update order to PAID status and test invoice generation
    print("   Updating order to PAID status...")
    order.status = 'PAID'
    order.save()
    
    try:
        invoice = invoice_service.generate_invoice(order)
        print(f"✅ Successfully generated invoice {invoice.invoice_number} for PAID order")
        return True
    except Exception as e:
        print(f"❌ ERROR generating invoice for PAID order: {e}")
        return False

def run_all_tests():
    """Run all GST implementation tests"""
    print("🚀 Starting Dynamic GST Implementation Tests with SQLite...")
    
    try:
        # Setup database
        setup_test_database()
        
        # Test GST models
        electronics_gst, books_gst, clothing_gst = test_gst_models()
        
        # Test products with GST
        laptop, book, tshirt = test_products_with_gst(electronics_gst, books_gst, clothing_gst)
        
        # Test product serializers
        test_product_serializers(laptop, book, tshirt)
        
        # Test cart with mixed GST
        user, cart = test_cart_with_mixed_gst(laptop, book, tshirt)
        
        # Test invoice generation
        invoice_success = test_invoice_generation(user)
        
        print("\n\n🎉 All Dynamic GST Tests Completed Successfully!")
        print("\nSummary:")
        print("✅ GST models created and working")
        print("✅ Products with different GST rates working")
        print("✅ Product serializers include GST information")
        print("✅ Cart with mixed GST rates working")
        print("✅ Invoice generation restricted to PAID orders only" if invoice_success else "❌ Invoice generation test failed")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
