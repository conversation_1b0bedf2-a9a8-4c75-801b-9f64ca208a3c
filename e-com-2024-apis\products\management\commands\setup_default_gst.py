"""
Management command to set up default GST rates
"""
from django.core.management.base import BaseCommand
from products.models import GST


class Command(BaseCommand):
    help = 'Set up default GST rates for the ecommerce application'

    def handle(self, *args, **options):
        self.stdout.write('Setting up default GST rates...')
        
        # Create default 18% GST rate
        default_gst, created = GST.objects.get_or_create(
            name="Default Rate",
            defaults={
                'rate': 18.00,
                'cgst_rate': 9.00,
                'sgst_rate': 9.00,
                'igst_rate': 18.00,
                'hsn_code': '8471',  # Default HSN for electronics
                'is_active': True
            }
        )
        
        if created:
            self.stdout.write(
                self.style.SUCCESS(f'Created default GST rate: {default_gst}')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'Default GST rate already exists: {default_gst}')
            )
        
        # Create additional common GST rates
        gst_rates = [
            {
                'name': 'Electronics & Appliances',
                'rate': 18.00,
                'cgst_rate': 9.00,
                'sgst_rate': 9.00,
                'igst_rate': 18.00,
                'hsn_code': '8471',
            },
            {
                'name': 'Home & Kitchen',
                'rate': 18.00,
                'cgst_rate': 9.00,
                'sgst_rate': 9.00,
                'igst_rate': 18.00,
                'hsn_code': '8516',
            },
            {
                'name': 'Books & Stationery',
                'rate': 12.00,
                'cgst_rate': 6.00,
                'sgst_rate': 6.00,
                'igst_rate': 12.00,
                'hsn_code': '4901',
            },
            {
                'name': 'Clothing & Textiles',
                'rate': 12.00,
                'cgst_rate': 6.00,
                'sgst_rate': 6.00,
                'igst_rate': 12.00,
                'hsn_code': '6109',
            },
            {
                'name': 'Food Items',
                'rate': 5.00,
                'cgst_rate': 2.50,
                'sgst_rate': 2.50,
                'igst_rate': 5.00,
                'hsn_code': '2106',
            },
        ]
        
        for gst_data in gst_rates:
            gst_rate, created = GST.objects.get_or_create(
                name=gst_data['name'],
                defaults={
                    'rate': gst_data['rate'],
                    'cgst_rate': gst_data['cgst_rate'],
                    'sgst_rate': gst_data['sgst_rate'],
                    'igst_rate': gst_data['igst_rate'],
                    'hsn_code': gst_data['hsn_code'],
                    'is_active': True
                }
            )
            
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Created GST rate: {gst_rate}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'GST rate already exists: {gst_rate}')
                )
        
        self.stdout.write(
            self.style.SUCCESS('Successfully set up GST rates!')
        )
