#!/usr/bin/env python3
"""
Low Quality Image Removal <PERSON> for Haier Products
This script removes low-quality images from directories and updates the JSON file
"""

import json
import os
import shutil
from PIL import Image
import glob
from datetime import datetime

class ImageQualityFilter:
    def __init__(self, config=None):
        # Default quality thresholds
        self.min_width = config.get('min_width', 800) if config else 800
        self.min_height = config.get('min_height', 600) if config else 600
        self.min_file_size = config.get('min_file_size', 50000) if config else 50000  # 50KB
        self.max_file_size = config.get('max_file_size', 10000000) if config else 10000000  # 10MB
        self.min_aspect_ratio = config.get('min_aspect_ratio', 0.5) if config else 0.5
        self.max_aspect_ratio = config.get('max_aspect_ratio', 2.0) if config else 2.0
        
        # Directories to process
        self.image_dirs = [
            'haier_product_images_downloaded',
            'haier_product_images_hq'
        ]
        
        # JSON file path
        self.json_file_path = 'output/json/haier_kitchen_products.json'
        
        # Statistics
        self.stats = {
            'total_images_checked': 0,
            'low_quality_removed': 0,
            'corrupted_removed': 0,
            'products_updated': 0,
            'directories_cleaned': 0
        }

    def is_low_quality_image(self, image_path):
        """Check if an image is low quality based on various criteria"""
        try:
            # Check file size
            file_size = os.path.getsize(image_path)
            if file_size < self.min_file_size or file_size > self.max_file_size:
                return True, f"File size {file_size} bytes (min: {self.min_file_size}, max: {self.max_file_size})"
            
            # Check image dimensions and quality
            with Image.open(image_path) as img:
                width, height = img.size
                
                # Check minimum dimensions
                if width < self.min_width or height < self.min_height:
                    return True, f"Dimensions {width}x{height} (min: {self.min_width}x{self.min_height})"
                
                # Check aspect ratio
                aspect_ratio = width / height
                if aspect_ratio < self.min_aspect_ratio or aspect_ratio > self.max_aspect_ratio:
                    return True, f"Aspect ratio {aspect_ratio:.2f} (range: {self.min_aspect_ratio}-{self.max_aspect_ratio})"
                
                # Check if image is too blurry or low quality (basic check)
                if self.is_image_blurry(img):
                    return True, "Image appears to be blurry or low quality"
                
                return False, "Good quality"
                
        except Exception as e:
            return True, f"Corrupted or unreadable: {str(e)}"

    def is_image_blurry(self, img):
        """Basic blur detection using image variance"""
        try:
            # Convert to grayscale
            gray = img.convert('L')
            
            # Resize for faster processing
            gray = gray.resize((100, 100))
            
            # Calculate variance of pixel values (low variance = blurry)
            pixels = list(gray.getdata())
            mean = sum(pixels) / len(pixels)
            variance = sum((p - mean) ** 2 for p in pixels) / len(pixels)
            
            # Threshold for blur detection (adjust as needed)
            blur_threshold = 1000
            return variance < blur_threshold
            
        except Exception:
            return False  # If we can't determine, assume it's not blurry

    def clean_directory(self, directory_path):
        """Clean low-quality images from a directory"""
        if not os.path.exists(directory_path):
            print(f"Directory not found: {directory_path}")
            return []
        
        print(f"\nCleaning directory: {directory_path}")
        removed_images = []
        
        # Find all image files
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', '*.webp']
        image_files = []
        
        for ext in image_extensions:
            image_files.extend(glob.glob(os.path.join(directory_path, '**', ext), recursive=True))
        
        print(f"Found {len(image_files)} images to check")
        
        for image_path in image_files:
            self.stats['total_images_checked'] += 1
            
            is_low_quality, reason = self.is_low_quality_image(image_path)
            
            if is_low_quality:
                try:
                    print(f"Removing: {os.path.basename(image_path)} - {reason}")
                    os.remove(image_path)
                    removed_images.append(image_path)
                    
                    if "Corrupted" in reason:
                        self.stats['corrupted_removed'] += 1
                    else:
                        self.stats['low_quality_removed'] += 1
                        
                except Exception as e:
                    print(f"Error removing {image_path}: {e}")
        
        # Clean empty directories
        self.remove_empty_directories(directory_path)
        
        return removed_images

    def remove_empty_directories(self, root_dir):
        """Remove empty directories recursively"""
        for dirpath, dirnames, filenames in os.walk(root_dir, topdown=False):
            if not filenames and not dirnames and dirpath != root_dir:
                try:
                    os.rmdir(dirpath)
                    print(f"Removed empty directory: {dirpath}")
                    self.stats['directories_cleaned'] += 1
                except Exception as e:
                    print(f"Error removing directory {dirpath}: {e}")

    def update_json_file(self, removed_images):
        """Update JSON file to remove references to deleted images"""
        if not os.path.exists(self.json_file_path):
            print(f"JSON file not found: {self.json_file_path}")
            return
        
        print(f"\nUpdating JSON file: {self.json_file_path}")
        
        # Create backup
        backup_path = self.json_file_path.replace('.json', f'_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        shutil.copy2(self.json_file_path, backup_path)
        print(f"Created backup: {backup_path}")
        
        # Load JSON
        try:
            with open(self.json_file_path, 'r', encoding='utf-8') as f:
                products = json.load(f)
        except Exception as e:
            print(f"Error loading JSON: {e}")
            return
        
        # Convert removed images to relative paths for comparison
        removed_relative_paths = set()
        for img_path in removed_images:
            relative_path = os.path.relpath(img_path).replace('\\', '/')
            removed_relative_paths.add(relative_path)
        
        # Update products
        updated_products = []
        for product in products:
            updated_product = product.copy()
            product_updated = False
            
            # Update regular images
            if 'images' in updated_product:
                original_images = updated_product['images']
                filtered_images = [img for img in original_images if img not in removed_relative_paths]
                
                if len(filtered_images) != len(original_images):
                    updated_product['images'] = filtered_images
                    product_updated = True
                    print(f"Updated images for {updated_product.get('model', 'Unknown')}: {len(original_images)} -> {len(filtered_images)}")
            
            # Update HQ images
            if 'hq_images' in updated_product:
                original_hq_images = updated_product['hq_images']
                filtered_hq_images = [img for img in original_hq_images if img not in removed_relative_paths]
                
                if len(filtered_hq_images) != len(original_hq_images):
                    updated_product['hq_images'] = filtered_hq_images
                    product_updated = True
                    print(f"Updated HQ images for {updated_product.get('model', 'Unknown')}: {len(original_hq_images)} -> {len(filtered_hq_images)}")
            
            if product_updated:
                self.stats['products_updated'] += 1
            
            updated_products.append(updated_product)
        
        # Save updated JSON
        try:
            with open(self.json_file_path, 'w', encoding='utf-8') as f:
                json.dump(updated_products, f, indent=2)
            print(f"Successfully updated JSON file")
        except Exception as e:
            print(f"Error saving JSON: {e}")

    def print_statistics(self):
        """Print cleaning statistics"""
        print("\n" + "="*50)
        print("CLEANING STATISTICS")
        print("="*50)
        print(f"Total images checked: {self.stats['total_images_checked']}")
        print(f"Low quality images removed: {self.stats['low_quality_removed']}")
        print(f"Corrupted images removed: {self.stats['corrupted_removed']}")
        print(f"Products updated in JSON: {self.stats['products_updated']}")
        print(f"Empty directories cleaned: {self.stats['directories_cleaned']}")
        print(f"Total images removed: {self.stats['low_quality_removed'] + self.stats['corrupted_removed']}")

    def run_cleanup(self, dry_run=False):
        """Run the complete cleanup process"""
        print("Haier Image Quality Cleanup Tool")
        print("="*40)
        print(f"Quality thresholds:")
        print(f"  Minimum dimensions: {self.min_width}x{self.min_height}")
        print(f"  File size range: {self.min_file_size/1000:.0f}KB - {self.max_file_size/1000000:.1f}MB")
        print(f"  Aspect ratio range: {self.min_aspect_ratio} - {self.max_aspect_ratio}")
        
        if dry_run:
            print("\n*** DRY RUN MODE - No files will be deleted ***")
        
        all_removed_images = []
        
        # Clean each directory
        for directory in self.image_dirs:
            if os.path.exists(directory):
                if not dry_run:
                    removed_images = self.clean_directory(directory)
                    all_removed_images.extend(removed_images)
                else:
                    print(f"\n[DRY RUN] Would clean directory: {directory}")
                    # In dry run, just check images without removing
                    self.check_directory_dry_run(directory)
            else:
                print(f"Directory not found: {directory}")
        
        # Update JSON file
        if not dry_run and all_removed_images:
            self.update_json_file(all_removed_images)
        elif dry_run:
            print("\n[DRY RUN] Would update JSON file with removed image references")
        
        # Print statistics
        self.print_statistics()

    def check_directory_dry_run(self, directory_path):
        """Check directory for low-quality images without removing them (dry run)"""
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', '*.webp']
        image_files = []
        
        for ext in image_extensions:
            image_files.extend(glob.glob(os.path.join(directory_path, '**', ext), recursive=True))
        
        print(f"Found {len(image_files)} images to check")
        
        for image_path in image_files:
            self.stats['total_images_checked'] += 1
            
            is_low_quality, reason = self.is_low_quality_image(image_path)
            
            if is_low_quality:
                print(f"[WOULD REMOVE] {os.path.basename(image_path)} - {reason}")
                
                if "Corrupted" in reason:
                    self.stats['corrupted_removed'] += 1
                else:
                    self.stats['low_quality_removed'] += 1


def load_config():
    """Load configuration from file if it exists"""
    config_file = 'image_quality_config.json'
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading config: {e}")
    
    return {}


def create_default_config():
    """Create a default configuration file"""
    config = {
        "min_width": 800,
        "min_height": 600,
        "min_file_size": 50000,
        "max_file_size": 10000000,
        "min_aspect_ratio": 0.5,
        "max_aspect_ratio": 2.0
    }
    
    with open('image_quality_config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    print("Created default configuration file: image_quality_config.json")


def main():
    """Main function"""
    print("Haier Image Quality Cleanup Tool")
    print("="*40)
    
    # Check if config exists, create if not
    if not os.path.exists('image_quality_config.json'):
        create_config = input("Create default configuration file? (y/n): ").lower().strip()
        if create_config == 'y':
            create_default_config()
    
    # Load configuration
    config = load_config()
    
    # Initialize filter
    filter_tool = ImageQualityFilter(config)
    
    # Ask user for dry run
    dry_run = input("\nRun in dry-run mode first? (y/n): ").lower().strip() == 'y'
    
    # Run cleanup
    filter_tool.run_cleanup(dry_run=dry_run)
    
    if dry_run:
        proceed = input("\nProceed with actual cleanup? (y/n): ").lower().strip()
        if proceed == 'y':
            filter_tool = ImageQualityFilter(config)  # Reset stats
            filter_tool.run_cleanup(dry_run=False)


if __name__ == "__main__":
    main()
