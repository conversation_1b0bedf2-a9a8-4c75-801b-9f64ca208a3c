/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "(rsc)/./app/api/auth/[...nextauth]/route.ts":
/*!*********************************************!*\
  !*** ./app/api/auth/[...nextauth]/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler),\n/* harmony export */   handler: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_authOptions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../lib/authOptions */ \"(rsc)/./lib/authOptions.ts\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_1___default()(_lib_authOptions__WEBPACK_IMPORTED_MODULE_0__.authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2F1dGgvWy4uLm5leHRhdXRoXS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEQ7QUFDekI7QUFFMUIsTUFBTUUsVUFBVUQsZ0RBQVFBLENBQUNELHlEQUFXQSxFQUFFO0FBRUoiLCJzb3VyY2VzIjpbIkQ6XFxUcml1bXBoXFxlY29tbWVyY2VcXGFwcFxcYXBpXFxhdXRoXFxbLi4ubmV4dGF1dGhdXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gJy4uLy4uLy4uLy4uL2xpYi9hdXRoT3B0aW9ucyc7XHJcbmltcG9ydCBOZXh0QXV0aCBmcm9tICduZXh0LWF1dGgnO1xyXG5cclxuZXhwb3J0IGNvbnN0IGhhbmRsZXIgPSBOZXh0QXV0aChhdXRoT3B0aW9ucyk7XHJcblxyXG5leHBvcnQgeyBoYW5kbGVyIGFzIEdFVCwgaGFuZGxlciBhcyBQT1NUfSJdLCJuYW1lcyI6WyJhdXRoT3B0aW9ucyIsIk5leHRBdXRoIiwiaGFuZGxlciIsIkdFVCIsIlBPU1QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./constant/urls.ts":
/*!**************************!*\
  !*** ./constant/urls.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADD_TO_CART: () => (/* binding */ ADD_TO_CART),\n/* harmony export */   ADD_TO_WISHLIST: () => (/* binding */ ADD_TO_WISHLIST),\n/* harmony export */   BRANDS: () => (/* binding */ BRANDS),\n/* harmony export */   CATEGORIES: () => (/* binding */ CATEGORIES),\n/* harmony export */   CATEGORIZE_PRODUCTS: () => (/* binding */ CATEGORIZE_PRODUCTS),\n/* harmony export */   CONTACT_FORM: () => (/* binding */ CONTACT_FORM),\n/* harmony export */   FUTURED_PRODUCTS: () => (/* binding */ FUTURED_PRODUCTS),\n/* harmony export */   GET_PROMO_CODE: () => (/* binding */ GET_PROMO_CODE),\n/* harmony export */   MAIN_URL: () => (/* binding */ MAIN_URL),\n/* harmony export */   ORDERS: () => (/* binding */ ORDERS),\n/* harmony export */   PAYMENTS_PHONEPE_INITIATE: () => (/* binding */ PAYMENTS_PHONEPE_INITIATE),\n/* harmony export */   PRODUCTS: () => (/* binding */ PRODUCTS),\n/* harmony export */   PROFILE_UPDATE: () => (/* binding */ PROFILE_UPDATE),\n/* harmony export */   PROMOCODE_APPLY: () => (/* binding */ PROMOCODE_APPLY),\n/* harmony export */   RANDOM_PRODUCTS: () => (/* binding */ RANDOM_PRODUCTS),\n/* harmony export */   REMOVE_FROM_WISHLIST: () => (/* binding */ REMOVE_FROM_WISHLIST),\n/* harmony export */   SHIPPING_METHODS: () => (/* binding */ SHIPPING_METHODS),\n/* harmony export */   TOKEN_REFFRESH: () => (/* binding */ TOKEN_REFFRESH),\n/* harmony export */   UPDATE_CART: () => (/* binding */ UPDATE_CART),\n/* harmony export */   USER_ADDRESS: () => (/* binding */ USER_ADDRESS),\n/* harmony export */   USER_CART: () => (/* binding */ USER_CART),\n/* harmony export */   USER_DETAIL: () => (/* binding */ USER_DETAIL),\n/* harmony export */   USER_LOGIN: () => (/* binding */ USER_LOGIN),\n/* harmony export */   USER_LOGOUT: () => (/* binding */ USER_LOGOUT),\n/* harmony export */   USER_REFFRESH_BLACKLIST: () => (/* binding */ USER_REFFRESH_BLACKLIST),\n/* harmony export */   USER_SIGNUP: () => (/* binding */ USER_SIGNUP),\n/* harmony export */   USER_SOCIAL_LOGIN: () => (/* binding */ USER_SOCIAL_LOGIN)\n/* harmony export */ });\n// const isProd = true;\n// process.env.NODE_ENV === \"production\";\nconst isProd = \"false\";\nconst devUrl = \"http://localhost:8000\";\n// const prodUrl = \"https://api-e-com.TRIUMPH ENTERPRISES.in\";\nconst prodUrl = \"http://localhost:8000\";\nconsole.log(\"process.env.IS_PROD\", \"false\");\nconsole.log(\"process.env.API_BACKEND_URL\", \"http://localhost:8000\");\nconst MAIN_URL = isProd ? prodUrl : devUrl;\nconsole.log(\"MAIN_URL\", MAIN_URL);\nconst version = \"/api/v1/\";\nconst PRODUCTS = `${version}products/`;\nconst CATEGORIES = `${version}products/categories/`;\nconst BRANDS = `${version}products/brands/`;\nconst USER_SIGNUP = `${version}users/`;\nconst USER_LOGIN = `${version}users/login/`;\nconst USER_LOGOUT = `${version}users/logout/`;\nconst USER_SOCIAL_LOGIN = `${version}users/social/login/`;\nconst USER_CART = `${version}orders/cart/`;\nconst ADD_TO_CART = `${version}orders/cart/add-item/`;\nconst UPDATE_CART = `${version}orders/cart/update-item/`;\nconst TOKEN_REFFRESH = `${version}users/token/refresh/`;\nconst USER_DETAIL = `${version}users/detail/`;\nconst USER_REFFRESH_BLACKLIST = `${version}users/token/blacklist/`;\nconst USER_ADDRESS = `${version}users/addresses/`;\nconst ORDERS = `${version}orders/`;\nconst ADD_TO_WISHLIST = `${version}users/wishlist/`;\nconst FUTURED_PRODUCTS = `${version}products/feature/products/`;\nconst RANDOM_PRODUCTS = `${version}products/feature/products/?random=true`;\nconst REMOVE_FROM_WISHLIST = `${version}users/remove/wishlist/`;\nconst CATEGORIZE_PRODUCTS = (slug)=>{\n    return `${version}products/categories/${slug}/products/`;\n};\nconst SHIPPING_METHODS = `${version}orders/shipping-methods/`;\nconst PROMOCODE_APPLY = `${version}promotions/apply/code/`;\nconst PROFILE_UPDATE = `${version}users/profile/update/`;\nconst GET_PROMO_CODE = `${version}promotions/get/single/promotion/`;\n// Payment URLs\nconst PAYMENTS_PHONEPE_INITIATE = `${version}payments/phonepe/initiate`;\n// Contact Form URL\nconst CONTACT_FORM = `${version}users/contact/`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./constant/urls.ts\n");

/***/ }),

/***/ "(rsc)/./lib/authOptions.ts":
/*!****************************!*\
  !*** ./lib/authOptions.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../constant/urls */ \"(rsc)/./constant/urls.ts\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            clientId: \"407412310348-82l3qbdon56udh9650abktvatl082hun.apps.googleusercontent.com\" || 0 || 0,\n            clientSecret: \"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\" || 0 || 0\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            // Configure to use the custom API response\n            name: \"Credentials\",\n            credentials: {},\n            async authorize (credentials) {\n                try {\n                    const data = {\n                        ...credentials\n                    };\n                    if (data) {\n                        const user = {\n                            id: data.id || \"\",\n                            email: data.email || \"\",\n                            name: data.name || \"\",\n                            access: data.accessToken || \"\",\n                            refresh: data.refreshToken || \"\",\n                            phone: data.phone_number,\n                            dob: data.date_of_birth\n                        };\n                        return user;\n                    }\n                } catch (error) {\n                    console.error(\"Error in authorize:\", error);\n                    return null;\n                }\n                return null;\n            }\n        })\n    ],\n    callbacks: {\n        async signIn ({ user, account, profile }) {\n            if (account?.provider === \"google\") {\n                try {\n                    // Show loading state in the console\n                    console.log(\"Google sign-in in progress...\");\n                    console.log(\"Google profile data:\", profile);\n                    // Send Google user data to your backend API\n                    const res = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(`${_constant_urls__WEBPACK_IMPORTED_MODULE_2__.MAIN_URL}${_constant_urls__WEBPACK_IMPORTED_MODULE_2__.USER_SOCIAL_LOGIN}`, {\n                        email: user.email,\n                        name: user.name,\n                        image_url: user.image || profile?.picture,\n                        provider: \"google\"\n                    }, {\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        }\n                    });\n                    if (res.status === 200) {\n                        const data = res.data;\n                        console.log(\"Social login success response:\", data);\n                        // Attach custom tokens to user object\n                        user.access = data.accessToken;\n                        user.refresh = data.refreshToken;\n                        user.phone = data.phone_number;\n                        user.dob = data.date_of_birth;\n                        user.id = Number(data.id);\n                        user.email = data.email;\n                        user.name = data.name;\n                        console.log(\"Google sign-in successful, redirecting...\");\n                        return true;\n                    } else {\n                        console.error(\"Backend login failed with status:\", res.status);\n                        return false;\n                    }\n                } catch (error) {\n                    console.error(\"Error during Google sign-in:\", error);\n                    if (axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(error)) {\n                        console.error(\"Axios error details:\", error.response?.data);\n                    }\n                    return false;\n                }\n            }\n            return true;\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                token.access = user.access;\n                token.refresh = user.refresh;\n                token.phone = user.phone;\n                token.dob = user.dob;\n                token.id = user.id;\n                token.email = user.email;\n                token.name = user.name;\n                token.accessTokenExpires = Date.now() + 60 * 60 * 1000;\n            }\n            if (Date.now() < token.accessTokenExpires) {\n                return token;\n            }\n            // Access token expired, try to update it\n            return refreshAccessToken(token);\n        },\n        async session ({ session, token }) {\n            session.user = {\n                ...session.user,\n                access: token.access,\n                refresh: token.refresh,\n                phone: token.phone,\n                dob: token.dob,\n                id: token.id,\n                email: token.email ?? \"\",\n                name: token.name ?? \"\"\n            };\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/login\"\n    }\n};\nasync function refreshAccessToken(token) {\n    try {\n        const res = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(_constant_urls__WEBPACK_IMPORTED_MODULE_2__.MAIN_URL + _constant_urls__WEBPACK_IMPORTED_MODULE_2__.TOKEN_REFFRESH, {\n            refresh: token.refresh\n        }, {\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        const refreshedTokens = res.data;\n        return {\n            ...token,\n            access: refreshedTokens.access,\n            accessTokenExpires: Date.now() + 60 * 60 * 1000\n        };\n    } catch (error) {\n        console.error(\"Error refreshing access token:\", error);\n        if (axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(error)) {\n            console.error(\"Axios error details:\", error.response?.data);\n        }\n        return {\n            ...token,\n            error: \"RefreshAccessTokenError\"\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/authOptions.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CTriumph%5Cecommerce%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CTriumph%5Cecommerce&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CTriumph%5Cecommerce%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CTriumph%5Cecommerce&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Triumph_ecommerce_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\api\\\\auth\\\\[...nextauth]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Triumph_ecommerce_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CTriumph%5Cecommerce%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CTriumph%5Cecommerce&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/delayed-stream","vendor-chunks/has-flag","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/uuid","vendor-chunks/oauth","vendor-chunks/@panva","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/oidc-token-hash","vendor-chunks/preact","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CTriumph%5Cecommerce%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CTriumph%5Cecommerce&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();