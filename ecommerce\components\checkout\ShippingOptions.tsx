import { MAIN_URL, SHIPPING_METHODS } from "@/constant/urls";
import { Label } from "../../components/ui/label";
import { RadioGroup, RadioGroupItem } from "../../components/ui/radio-group";
import { Button } from "../ui/button";
import useApi from "@/hooks/useApi";
import { useSession } from "next-auth/react";
import { useEffect } from "react";
import { Truck, Clock, ArrowLeft, ArrowRight } from "lucide-react";

const shippingMethods = [
  {
    id: "standard",
    name: "Standard Shipping",
    price: 50,
    estimate: "5-7 business days",
  },
  {
    id: "express",
    name: "Express Shipping",
    price: 159,
    estimate: "2-3 business days",
  },
  {
    id: "overnight",
    name: "Overnight Shipping",
    price: 299.9,
    estimate: "Next business day",
  },
];

interface ShippingOptionsProps {
  selectedMethod: any;
  onMethodSelect: (method: string) => void;
  handleNext: (step: number) => void;
  handleBack: (step: number) => void;
  createOrder: () => void;
}

export const ShippingOptions = ({
  selectedMethod,
  onMethodSelect,
  handleNext,
  handleBack,
  createOrder,
}: ShippingOptionsProps) => {
  const { read, data, loading } = useApi(MAIN_URL);
  const { status } = useSession();

  useEffect(() => {
    if (status === "authenticated") {
      read(SHIPPING_METHODS);
    }
  }, [status]);

  const handleCreateorderAndNext = (step:number) => {
    handleNext(step);
     createOrder();
  };
  return (
    <div className="space-y-8">
      <h2 className="text-2xl font-semibold text-center mb-6 gradient-text flex items-center justify-center gap-2">
        <Truck className="h-6 w-6" />
        Shipping Method
      </h2>

      <RadioGroup value={selectedMethod} onValueChange={onMethodSelect}>
        <div className="space-y-5">
          {Array.isArray(data) &&
            data.map((method) => (
              <div
                key={method.id}
                className={`flex items-center space-x-4 border rounded-lg p-5 transition-all duration-300 ${
                  selectedMethod?.id === method.id
                    ? 'border-primary shadow-md bg-primary/5'
                    : 'hover:border-gray-300 hover:shadow-sm'
                }`}
              >
                <RadioGroupItem value={method} id={method.id} className="h-5 w-5" />
                <Label
                  htmlFor={method.name}
                  className="flex flex-1 justify-between cursor-pointer"
                >
                  <div className="flex items-start gap-3">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      selectedMethod?.id === method.id
                        ? 'bg-primary text-white'
                        : 'bg-secondary text-muted-foreground'
                    }`}>
                      <Truck className="h-5 w-5" />
                    </div>
                    <div>
                      <div className="font-medium text-lg">{method.name}</div>
                      <div className="text-sm text-muted-foreground flex items-center gap-1 mt-1">
                        <Clock className="h-3 w-3" />
                        {method.description}
                      </div>
                    </div>
                  </div>
                  <div className="font-medium text-lg">
                    ₹{Number(method.price).toFixed(2)}
                  </div>
                </Label>
              </div>
            ))}

          <div className="flex justify-between mt-8">
            <Button
              variant="outline"
              onClick={() => handleBack(1)}
              className="flex items-center gap-2 px-6"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <Button
              disabled={!Boolean(selectedMethod?.id)}
              onClick={() => handleCreateorderAndNext(1)}
              className="flex items-center gap-2 px-6"
            >
              Next
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </RadioGroup>
    </div>
  );
};
