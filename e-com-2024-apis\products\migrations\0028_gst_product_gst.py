# Generated by Django 5.0.2 on 2025-05-27 11:44

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0027_alter_brand_image'),
    ]

    operations = [
        migrations.CreateModel(
            name='GST',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="GST category name (e.g., 'Standard Rate', 'Electronics')", max_length=100)),
                ('rate', models.DecimalField(decimal_places=2, default=18.0, help_text='GST rate in percentage', max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('cgst_rate', models.DecimalField(decimal_places=2, default=9.0, help_text='CGST rate in percentage', max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(50)])),
                ('sgst_rate', models.DecimalField(decimal_places=2, default=9.0, help_text='SGST rate in percentage', max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(50)])),
                ('igst_rate', models.DecimalField(decimal_places=2, default=18.0, help_text='IGST rate in percentage (for inter-state)', max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('hsn_code', models.CharField(blank=True, help_text='HSN/SAC code for this GST category', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'GST Rate',
                'verbose_name_plural': 'GST Rates',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='product',
            name='gst',
            field=models.ForeignKey(blank=True, help_text='GST rate for this product. If not set, default 18% will be used.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products', to='products.gst'),
        ),
    ]
