"use client";
import { useEffect, useState } from "react";
import { ProductInfo } from "../../../components/product/ProductInfo";
import { RelatedProducts } from "../../../components/product/RelatedProducts";
import useApi from "../../../hooks/useApi";
import { MAIN_URL, PRODUCTS } from "../../../constant/urls";
import { useParams } from "next/navigation";
import MainHOF from "../../../layout/MainHOF";
import { Swiper, SwiperSlide } from "swiper/react";
import "../../../styles/product.style.css";

// Import Swiper styles
import "swiper/css";
import "swiper/css/free-mode";
import "swiper/css/navigation";
import "swiper/css/thumbs";

// import required modules
import { FreeMode, Navigation, Thumbs } from "swiper/modules";
import SingleProductLoading from "@/components/ui/loading/SingleProductLoading";
import Image from "next/image";
import { MediaModal } from "@/components/ui/MediaModal";
import ImageCarousel from "@/components/product/ImageCarousel";
import { ProductMetadata } from "./ProductMetadata";

const ProductDetail = () => {
  const { slug } = useParams();
  const [selectedColor, setSelectedColor] = useState("");
  const [selectedSize, setSelectedSize] = useState("");
  const [quantity, setQuantity] = useState(1);
  const { data, loading, read } = useApi(MAIN_URL);
  const [thumbsSwiper, setThumbsSwiper] = useState<any>(null);

  useEffect(() => {
    console.log("Fetching product details for slug:", slug);
    read(PRODUCTS + slug);
  }, [slug]);

  // Log the product data when it changes
  useEffect(() => {
    if (data) {
      console.log("Product data loaded:", data);
    }
  }, [data]);
  // Mock product data - in a real app, this would come from an API
  const product: any = data ?? undefined;

  if (loading) {
    return (
      <MainHOF>
        <div className="container mx-auto px-4 py-8">
          <SingleProductLoading />
        </div>
      </MainHOF>
    );
  }

  return (
    <MainHOF>
      {product && <ProductMetadata product={product} />}
      <div className="container mx-auto px-4 py-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {product && (
            <>
              <div className="w-full p-4 border rounded-md border-slate-200 flex items-center justify-center">
                {Array.isArray(product.images) && (
                  <ImageCarousel
                    productImage={product.images}
                    brand={product.brand}
                  />
                )}
              </div>
              <ProductInfo
                product={product}
                selectedColor={selectedColor}
                selectedSize={selectedSize}
                quantity={quantity}
                onColorChange={setSelectedColor}
                onSizeChange={setSelectedSize}
                onQuantityChange={setQuantity}
              />
            </>
          )}
        </div>
        {/* Render RelatedProducts component */}
        <RelatedProducts category_slug={product?.category?.slug || ""} />
      </div>
    </MainHOF>
  );
};

export default ProductDetail;
