2025-05-20 12:28:50,322 - INFO - Starting image scraping process
2025-05-20 12:28:50,325 - INFO - Loaded 187 products from output/json/product_data.json
2025-05-20 12:28:50,343 - INFO - Processing product: Nav-tal 6 Levers (2 keys) Furniture Locks Furniture Locks (Model: 3278)
2025-05-20 12:28:50,346 - INFO - Processing product: Nav-tal 5 Levers (2 keys) Furniture Locks Furniture Locks (Model: 3276)
2025-05-20 12:28:50,349 - INFO - Processing product: Nav-tal 5 Levers (3 keys) Furniture Locks Furniture Locks (Model: 3277)
2025-05-20 12:28:50,349 - INFO - Processing product: Nav-tal 5 Levers (3 keys) - Blister Furniture Locks Furniture Locks (Model: 3060)
2025-05-20 12:28:55,190 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:28:56,834 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:28:56,895 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:28:57,714 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:29:05,498 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:29:05,498 - INFO - Searching Google for Nav-tal 5 Levers (2 keys) Furniture Locks Furniture Locks (Model: 3276)
2025-05-20 12:29:11,549 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:29:11,550 - INFO - Searching Google for Nav-tal 5 Levers (3 keys) Furniture Locks Furniture Locks (Model: 3277)
2025-05-20 12:29:12,643 - INFO - Completed processing Nav-tal 5 Levers (2 keys) Furniture Locks Furniture Locks (Model: 3276). Found 0 images.
2025-05-20 12:29:13,336 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:29:13,336 - INFO - Searching Google for Nav-tal 6 Levers (2 keys) Furniture Locks Furniture Locks (Model: 3278)
2025-05-20 12:29:13,499 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:29:13,499 - INFO - Searching Google for Nav-tal 5 Levers (3 keys) - Blister Furniture Locks Furniture Locks (Model: 3060)
2025-05-20 12:29:19,111 - INFO - Completed processing Nav-tal 5 Levers (3 keys) Furniture Locks Furniture Locks (Model: 3277). Found 0 images.
2025-05-20 12:29:19,118 - INFO - Completed 1/187 products
2025-05-20 12:29:19,118 - INFO - Processing product: Nav-tal 6 Levers (3 keys) - Blister Furniture Locks Furniture Locks (Model: 3061)
2025-05-20 12:29:19,787 - INFO - Completed processing Nav-tal 5 Levers (3 keys) - Blister Furniture Locks Furniture Locks (Model: 3060). Found 0 images.
2025-05-20 12:29:20,899 - INFO - Completed processing Nav-tal 6 Levers (2 keys) Furniture Locks Furniture Locks (Model: 3278). Found 0 images.
2025-05-20 12:29:21,982 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:29:23,788 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:29:23,789 - INFO - Searching Google for Nav-tal 6 Levers (3 keys) - Blister Furniture Locks Furniture Locks (Model: 3061)
2025-05-20 12:29:25,378 - INFO - Completed 2/187 products
2025-05-20 12:29:25,381 - INFO - Processing product: Nav-tal 6 Levers - Long Shackle (3 keys) Furniture Locks Furniture Locks (Model: 8870)
2025-05-20 12:29:26,008 - INFO - Completed 3/187 products
2025-05-20 12:29:26,009 - INFO - Processing product: Nav-tal 6 Levers - Long Shackle (2 keys) Furniture Locks Furniture Locks (Model: 3280)
2025-05-20 12:29:27,175 - INFO - Processing product: Nav-tal 6 Levers (3 keys) Furniture Locks Furniture Locks (Model: 3279)
2025-05-20 12:29:27,175 - INFO - Completed 4/187 products
2025-05-20 12:29:28,566 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:29:28,990 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:29:30,635 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:29:31,222 - INFO - Completed processing Nav-tal 6 Levers (3 keys) - Blister Furniture Locks Furniture Locks (Model: 3061). Found 0 images.
2025-05-20 12:29:32,338 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:29:32,338 - INFO - Searching Google for Nav-tal 6 Levers - Long Shackle (2 keys) Furniture Locks Furniture Locks (Model: 3280)
2025-05-20 12:29:32,854 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:29:32,855 - INFO - Searching Google for Nav-tal 6 Levers - Long Shackle (3 keys) Furniture Locks Furniture Locks (Model: 8870)
2025-05-20 12:29:33,399 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:29:33,400 - INFO - Searching Google for Nav-tal 6 Levers (3 keys) Furniture Locks Furniture Locks (Model: 3279)
2025-05-20 12:29:37,466 - INFO - Completed 5/187 products
2025-05-20 12:29:37,467 - INFO - Processing product: Nav-tal 7 Levers Hardened (3 keys) - Blister Furniture Locks Furniture Locks (Model: 3062)
2025-05-20 12:29:39,680 - INFO - Completed processing Nav-tal 6 Levers - Long Shackle (2 keys) Furniture Locks Furniture Locks (Model: 3280). Found 0 images.
2025-05-20 12:29:39,954 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:29:40,191 - INFO - Completed processing Nav-tal 6 Levers - Long Shackle (3 keys) Furniture Locks Furniture Locks (Model: 8870). Found 0 images.
2025-05-20 12:29:40,618 - INFO - Completed processing Nav-tal 6 Levers (3 keys) Furniture Locks Furniture Locks (Model: 3279). Found 0 images.
2025-05-20 12:29:42,903 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:29:42,904 - INFO - Searching Google for Nav-tal 7 Levers Hardened (3 keys) - Blister Furniture Locks Furniture Locks (Model: 3062)
2025-05-20 12:29:45,950 - INFO - Completed 6/187 products
2025-05-20 12:29:45,950 - INFO - Processing product: Nav-tal 7 Levers Hardened (2 keys) Furniture Locks Furniture Locks (Model: 3290)
2025-05-20 12:29:46,457 - INFO - Completed 7/187 products
2025-05-20 12:29:46,457 - INFO - Processing product: Nav-tal 7 Lever Shut Height 3cm (30mm) (3 Keys) Furniture Locks Furniture Locks (Model: 3596)
2025-05-20 12:29:46,846 - INFO - Completed 8/187 products
2025-05-20 12:29:46,846 - INFO - Processing product: Nav-tal 7 Levers Hardened (4 keys) Furniture Locks Furniture Locks (Model: 7169)
2025-05-20 12:29:48,466 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:29:48,740 - INFO - Completed processing Nav-tal 7 Levers Hardened (3 keys) - Blister Furniture Locks Furniture Locks (Model: 3062). Found 0 images.
2025-05-20 12:29:48,865 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:29:49,113 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:29:51,167 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:29:51,167 - INFO - Searching Google for Nav-tal 7 Lever Shut Height 3cm (30mm) (3 Keys) Furniture Locks Furniture Locks (Model: 3596)
2025-05-20 12:29:52,342 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:29:52,343 - INFO - Searching Google for Nav-tal 7 Levers Hardened (2 keys) Furniture Locks Furniture Locks (Model: 3290)
2025-05-20 12:29:53,053 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:29:53,053 - INFO - Searching Google for Nav-tal 7 Levers Hardened (4 keys) Furniture Locks Furniture Locks (Model: 7169)
2025-05-20 12:29:55,012 - INFO - Completed 9/187 products
2025-05-20 12:29:55,013 - INFO - Processing product: Nav-tal 7 Lever Shut Height 5cm (50mm) (3 Keys) Furniture Locks Furniture Locks (Model: 3597)
2025-05-20 12:29:55,018 - INFO - Completed processing Nav-tal 7 Lever Shut Height 3cm (30mm) (3 Keys) Furniture Locks Furniture Locks (Model: 3596). Found 0 images.
2025-05-20 12:29:57,295 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:29:58,265 - INFO - Completed processing Nav-tal 7 Levers Hardened (2 keys) Furniture Locks Furniture Locks (Model: 3290). Found 0 images.
2025-05-20 12:29:59,195 - INFO - Completed processing Nav-tal 7 Levers Hardened (4 keys) Furniture Locks Furniture Locks (Model: 7169). Found 0 images.
2025-05-20 12:29:59,201 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:29:59,202 - INFO - Searching Google for Nav-tal 7 Lever Shut Height 5cm (50mm) (3 Keys) Furniture Locks Furniture Locks (Model: 3597)
2025-05-20 12:30:01,291 - INFO - Completed 10/187 products
2025-05-20 12:30:01,292 - INFO - Processing product: Freedom 5 Levers (2 keys) Furniture Locks Furniture Locks (Model: 3285)
2025-05-20 12:30:03,667 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:30:04,606 - INFO - Completed 11/187 products
2025-05-20 12:30:04,607 - INFO - Processing product: Nav-tal 8 Levers Hardened (2 keys) Furniture Locks Furniture Locks (Model: 3281)
2025-05-20 12:30:04,966 - INFO - Completed processing Nav-tal 7 Lever Shut Height 5cm (50mm) (3 Keys) Furniture Locks Furniture Locks (Model: 3597). Found 0 images.
2025-05-20 12:30:05,524 - INFO - Completed 12/187 products
2025-05-20 12:30:05,525 - INFO - Processing product: Nav-tal 8 Levers Deluxe Hardened (3 keys) Furniture Locks Furniture Locks (Model: 3282)
2025-05-20 12:30:06,884 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:30:07,519 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:30:07,520 - INFO - Searching Google for Freedom 5 Levers (2 keys) Furniture Locks Furniture Locks (Model: 3285)
2025-05-20 12:30:08,023 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:30:09,102 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:30:09,103 - INFO - Searching Google for Nav-tal 8 Levers Hardened (2 keys) Furniture Locks Furniture Locks (Model: 3281)
2025-05-20 12:30:11,223 - INFO - Completed 13/187 products
2025-05-20 12:30:11,224 - INFO - Processing product: Freedom 5 Levers (2 keys) - Blister Furniture Locks Furniture Locks (Model: 3064)
2025-05-20 12:30:11,966 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:30:11,967 - INFO - Searching Google for Nav-tal 8 Levers Deluxe Hardened (3 keys) Furniture Locks Furniture Locks (Model: 3282)
2025-05-20 12:30:13,080 - INFO - Completed processing Freedom 5 Levers (2 keys) Furniture Locks Furniture Locks (Model: 3285). Found 0 images.
2025-05-20 12:30:13,996 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:30:16,622 - INFO - Completed processing Nav-tal 8 Levers Hardened (2 keys) Furniture Locks Furniture Locks (Model: 3281). Found 0 images.
2025-05-20 12:30:17,821 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:30:17,822 - INFO - Searching Google for Freedom 5 Levers (2 keys) - Blister Furniture Locks Furniture Locks (Model: 3064)
2025-05-20 12:30:19,346 - INFO - Completed 14/187 products
2025-05-20 12:30:19,346 - INFO - Processing product: Freedom 6 Levers (2 keys) - Blister Furniture Locks Furniture Locks (Model: 3065)
2025-05-20 12:30:19,530 - INFO - Completed processing Nav-tal 8 Levers Deluxe Hardened (3 keys) Furniture Locks Furniture Locks (Model: 3282). Found 0 images.
2025-05-20 12:30:21,871 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:30:22,843 - INFO - Completed 15/187 products
2025-05-20 12:30:22,844 - INFO - Processing product: Freedom 7 Levers (2 keys) Furniture Locks Furniture Locks (Model: 3287)
2025-05-20 12:30:23,563 - INFO - Completed processing Freedom 5 Levers (2 keys) - Blister Furniture Locks Furniture Locks (Model: 3064). Found 0 images.
2025-05-20 12:30:23,585 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:30:23,585 - INFO - Searching Google for Freedom 6 Levers (2 keys) - Blister Furniture Locks Furniture Locks (Model: 3065)
2025-05-20 12:30:25,643 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:30:25,811 - INFO - Completed 16/187 products
2025-05-20 12:30:25,812 - INFO - Processing product: Freedom 6 Levers (2 keys) Furniture Locks Furniture Locks (Model: 3286)
2025-05-20 12:30:28,432 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:30:29,649 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:30:29,649 - INFO - Searching Google for Freedom 7 Levers (2 keys) Furniture Locks Furniture Locks (Model: 3287)
2025-05-20 12:30:29,848 - INFO - Completed 17/187 products
2025-05-20 12:30:29,849 - INFO - Processing product: Freedom 7 Levers (3 keys) Furniture Locks Furniture Locks (Model: 7665)
2025-05-20 12:30:31,200 - INFO - Completed processing Freedom 6 Levers (2 keys) - Blister Furniture Locks Furniture Locks (Model: 3065). Found 0 images.
2025-05-20 12:30:32,199 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:30:32,334 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:30:32,335 - INFO - Searching Google for Freedom 6 Levers (2 keys) Furniture Locks Furniture Locks (Model: 3286)
2025-05-20 12:30:34,178 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:30:34,180 - INFO - Searching Google for Freedom 7 Levers (3 keys) Furniture Locks Furniture Locks (Model: 7665)
2025-05-20 12:30:35,475 - INFO - Completed processing Freedom 7 Levers (2 keys) Furniture Locks Furniture Locks (Model: 3287). Found 0 images.
2025-05-20 12:30:37,454 - INFO - Completed 18/187 products
2025-05-20 12:30:37,455 - INFO - Processing product: Freedom 7 Levers (2 keys) - Blister Furniture Locks Furniture Locks (Model: 3066)
2025-05-20 12:30:38,274 - INFO - Completed processing Freedom 6 Levers (2 keys) Furniture Locks Furniture Locks (Model: 3286). Found 0 images.
2025-05-20 12:30:40,137 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:30:41,513 - INFO - Completed processing Freedom 7 Levers (3 keys) Furniture Locks Furniture Locks (Model: 7665). Found 0 images.
2025-05-20 12:30:41,729 - INFO - Completed 19/187 products
2025-05-20 12:30:41,729 - INFO - Processing product: Godrej No.1 - 4cm (40mm) Furniture Locks Furniture Locks (Model: 2774)
2025-05-20 12:30:43,972 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:30:43,972 - INFO - Searching Google for Freedom 7 Levers (2 keys) - Blister Furniture Locks Furniture Locks (Model: 3066)
2025-05-20 12:30:44,461 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:30:44,553 - INFO - Completed 20/187 products
2025-05-20 12:30:44,553 - INFO - Processing product: Godrej No.1 - 5cm (50mm) Furniture Locks Furniture Locks (Model: 2775)
2025-05-20 12:30:46,247 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:30:46,248 - INFO - Searching Google for Godrej No.1 - 4cm (40mm) Furniture Locks Furniture Locks (Model: 2774)
2025-05-20 12:30:46,938 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:30:47,748 - INFO - Completed 21/187 products
2025-05-20 12:30:47,749 - INFO - Processing product: Kaditala 20cm (200mm) Texture Brown (3 Keys) Furniture Locks Furniture Locks (Model: 5091)
2025-05-20 12:30:49,646 - INFO - Completed processing Freedom 7 Levers (2 keys) - Blister Furniture Locks Furniture Locks (Model: 3066). Found 0 images.
2025-05-20 12:30:50,989 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:30:50,990 - INFO - Searching Google for Godrej No.1 - 5cm (50mm) Furniture Locks Furniture Locks (Model: 2775)
2025-05-20 12:30:51,942 - INFO - Completed processing Godrej No.1 - 4cm (40mm) Furniture Locks Furniture Locks (Model: 2774). Found 0 images.
2025-05-20 12:30:53,453 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:30:55,309 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:30:55,310 - INFO - Searching Google for Kaditala 20cm (200mm) Texture Brown (3 Keys) Furniture Locks Furniture Locks (Model: 5091)
2025-05-20 12:30:55,977 - INFO - Completed 22/187 products
2025-05-20 12:30:55,977 - INFO - Processing product: Sherlock 6cm (60mm) Ultra - 3 Keys (Blister) Furniture Locks Furniture Locks (Model: 7676)
2025-05-20 12:30:58,199 - INFO - Completed 23/187 products
2025-05-20 12:30:58,200 - INFO - Processing product: Godrej No.1 - 6cm (60mm) Furniture Locks Furniture Locks (Model: 2776)
2025-05-20 12:30:58,366 - INFO - Completed processing Godrej No.1 - 5cm (50mm) Furniture Locks Furniture Locks (Model: 2775). Found 0 images.
2025-05-20 12:30:59,444 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:31:02,355 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:31:02,750 - INFO - Completed processing Kaditala 20cm (200mm) Texture Brown (3 Keys) Furniture Locks Furniture Locks (Model: 5091). Found 0 images.
2025-05-20 12:31:03,527 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:31:03,529 - INFO - Searching Google for Sherlock 6cm (60mm) Ultra - 3 Keys (Blister) Furniture Locks Furniture Locks (Model: 7676)
2025-05-20 12:31:04,592 - INFO - Completed 24/187 products
2025-05-20 12:31:04,593 - INFO - Processing product: Godrej No.1 - 7cm (70mm) Furniture Locks Furniture Locks (Model: 2777)
2025-05-20 12:31:06,745 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:31:06,746 - INFO - Searching Google for Godrej No.1 - 6cm (60mm) Furniture Locks Furniture Locks (Model: 2776)
2025-05-20 12:31:09,542 - INFO - Completed 25/187 products
2025-05-20 12:31:09,542 - INFO - Processing product: Nav-tal 5 Levers (2 keys) Rim Locks Rim Locks (Model: 3276)
2025-05-20 12:31:09,716 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:31:10,893 - INFO - Completed processing Sherlock 6cm (60mm) Ultra - 3 Keys (Blister) Furniture Locks Furniture Locks (Model: 7676). Found 0 images.
2025-05-20 12:31:12,800 - INFO - Completed processing Godrej No.1 - 6cm (60mm) Furniture Locks Furniture Locks (Model: 2776). Found 0 images.
2025-05-20 12:31:13,562 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:31:13,563 - INFO - Searching Google for Godrej No.1 - 7cm (70mm) Furniture Locks Furniture Locks (Model: 2777)
2025-05-20 12:31:13,618 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:31:16,599 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:31:16,601 - INFO - Searching Google for Nav-tal 5 Levers (2 keys) Rim Locks Rim Locks (Model: 3276)
2025-05-20 12:31:17,176 - INFO - Completed 26/187 products
2025-05-20 12:31:17,178 - INFO - Processing product: Kaditala 27.5 cm (275mm) Chrome (3 Keys) Furniture Locks Furniture Locks (Model: 5094)
2025-05-20 12:31:19,700 - INFO - Completed processing Godrej No.1 - 7cm (70mm) Furniture Locks Furniture Locks (Model: 2777). Found 0 images.
2025-05-20 12:31:20,917 - INFO - Completed 27/187 products
2025-05-20 12:31:20,918 - INFO - Processing product: Kaditala 27.5 cm (275mm) Texture Brown (3 Keys) Furniture Locks Furniture Locks (Model: 5092)
2025-05-20 12:31:21,908 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:31:22,119 - INFO - Completed processing Nav-tal 5 Levers (2 keys) Rim Locks Rim Locks (Model: 3276). Found 0 images.
2025-05-20 12:31:24,340 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:31:24,340 - INFO - Searching Google for Kaditala 27.5 cm (275mm) Chrome (3 Keys) Furniture Locks Furniture Locks (Model: 5094)
2025-05-20 12:31:25,592 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:31:27,849 - INFO - Completed 28/187 products
2025-05-20 12:31:27,849 - INFO - Processing product: Kaditala 20cm (200mm) Chrome (3 Keys) Furniture Locks Furniture Locks (Model: 5093)
2025-05-20 12:31:28,417 - INFO - Completed 29/187 products
2025-05-20 12:31:28,417 - INFO - Processing product: Nav-tal 5 Levers (3 keys) Rim Locks Rim Locks (Model: 3277)
2025-05-20 12:31:29,401 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:31:29,402 - INFO - Searching Google for Kaditala 27.5 cm (275mm) Texture Brown (3 Keys) Furniture Locks Furniture Locks (Model: 5092)
2025-05-20 12:31:30,128 - INFO - Completed processing Kaditala 27.5 cm (275mm) Chrome (3 Keys) Furniture Locks Furniture Locks (Model: 5094). Found 0 images.
2025-05-20 12:31:31,236 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:31:32,584 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:31:33,188 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:31:33,189 - INFO - Searching Google for Kaditala 20cm (200mm) Chrome (3 Keys) Furniture Locks Furniture Locks (Model: 5093)
2025-05-20 12:31:35,912 - INFO - Completed processing Kaditala 27.5 cm (275mm) Texture Brown (3 Keys) Furniture Locks Furniture Locks (Model: 5092). Found 0 images.
2025-05-20 12:31:36,374 - INFO - Processing product: Nav-tal 5 Levers (3 keys) - Blister Rim Locks Rim Locks (Model: 3060)
2025-05-20 12:31:36,381 - INFO - Completed 30/187 products
2025-05-20 12:31:36,545 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:31:36,545 - INFO - Searching Google for Nav-tal 5 Levers (3 keys) Rim Locks Rim Locks (Model: 3277)
2025-05-20 12:31:38,906 - INFO - Completed processing Kaditala 20cm (200mm) Chrome (3 Keys) Furniture Locks Furniture Locks (Model: 5093). Found 0 images.
2025-05-20 12:31:40,492 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:31:42,318 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:31:42,318 - INFO - Searching Google for Nav-tal 5 Levers (3 keys) - Blister Rim Locks Rim Locks (Model: 3060)
2025-05-20 12:31:42,886 - INFO - Completed processing Nav-tal 5 Levers (3 keys) Rim Locks Rim Locks (Model: 3277). Found 0 images.
2025-05-20 12:31:43,276 - INFO - Completed 31/187 products
2025-05-20 12:31:43,277 - INFO - Processing product: Nav-tal 6 Levers (2 keys) Rim Locks Rim Locks (Model: 3278)
2025-05-20 12:31:45,150 - INFO - Completed 32/187 products
2025-05-20 12:31:45,150 - INFO - Processing product: Nav-tal 6 Levers (3 keys) Rim Locks Rim Locks (Model: 3279)
2025-05-20 12:31:47,977 - INFO - Completed processing Nav-tal 5 Levers (3 keys) - Blister Rim Locks Rim Locks (Model: 3060). Found 0 images.
2025-05-20 12:31:49,162 - INFO - Completed 33/187 products
2025-05-20 12:31:49,163 - INFO - Processing product: Nav-tal 6 Levers - Long Shackle (2 keys) Rim Locks Rim Locks (Model: 3280)
2025-05-20 12:31:49,544 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:31:50,078 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:31:51,363 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:31:51,363 - INFO - Searching Google for Nav-tal 6 Levers (3 keys) Rim Locks Rim Locks (Model: 3279)
2025-05-20 12:31:53,042 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:31:53,995 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:31:53,996 - INFO - Searching Google for Nav-tal 6 Levers (2 keys) Rim Locks Rim Locks (Model: 3278)
2025-05-20 12:31:54,244 - INFO - Completed 34/187 products
2025-05-20 12:31:54,244 - INFO - Processing product: Nav-tal 6 Levers - Long Shackle (3 keys) Rim Locks Rim Locks (Model: 8870)
2025-05-20 12:31:57,364 - INFO - Completed processing Nav-tal 6 Levers (3 keys) Rim Locks Rim Locks (Model: 3279). Found 0 images.
2025-05-20 12:31:57,611 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:31:57,612 - INFO - Searching Google for Nav-tal 6 Levers - Long Shackle (2 keys) Rim Locks Rim Locks (Model: 3280)
2025-05-20 12:31:57,877 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:32:00,196 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:32:00,196 - INFO - Searching Google for Nav-tal 6 Levers - Long Shackle (3 keys) Rim Locks Rim Locks (Model: 8870)
2025-05-20 12:32:01,518 - INFO - Completed processing Nav-tal 6 Levers (2 keys) Rim Locks Rim Locks (Model: 3278). Found 0 images.
2025-05-20 12:32:03,981 - INFO - Completed 35/187 products
2025-05-20 12:32:03,982 - INFO - Processing product: Nav-tal 6 Levers (3 keys) - Blister Rim Locks Rim Locks (Model: 3061)
2025-05-20 12:32:04,832 - INFO - Completed processing Nav-tal 6 Levers - Long Shackle (2 keys) Rim Locks Rim Locks (Model: 3280). Found 0 images.
2025-05-20 12:32:07,311 - INFO - Completed processing Nav-tal 6 Levers - Long Shackle (3 keys) Rim Locks Rim Locks (Model: 8870). Found 0 images.
2025-05-20 12:32:07,436 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:32:09,609 - INFO - Processing product: Nav-tal 7 Levers Hardened (2 keys) Rim Locks Rim Locks (Model: 3290)
2025-05-20 12:32:09,614 - INFO - Completed 36/187 products
2025-05-20 12:32:09,728 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:32:09,729 - INFO - Searching Google for Nav-tal 6 Levers (3 keys) - Blister Rim Locks Rim Locks (Model: 3061)
2025-05-20 12:32:11,078 - INFO - Completed 37/187 products
2025-05-20 12:32:11,078 - INFO - Processing product: Nav-tal 7 Levers Hardened (4 keys) Rim Locks Rim Locks (Model: 7169)
2025-05-20 12:32:13,450 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:32:13,619 - INFO - Completed 38/187 products
2025-05-20 12:32:13,620 - INFO - Processing product: Nav-tal 7 Levers Hardened (3 keys) - Blister Rim Locks Rim Locks (Model: 3062)
2025-05-20 12:32:13,895 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:32:16,350 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:32:17,359 - INFO - Completed processing Nav-tal 6 Levers (3 keys) - Blister Rim Locks Rim Locks (Model: 3061). Found 0 images.
2025-05-20 12:32:17,414 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:32:17,414 - INFO - Searching Google for Nav-tal 7 Levers Hardened (2 keys) Rim Locks Rim Locks (Model: 3290)
2025-05-20 12:32:18,239 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:32:18,240 - INFO - Searching Google for Nav-tal 7 Levers Hardened (4 keys) Rim Locks Rim Locks (Model: 7169)
2025-05-20 12:32:20,627 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:32:20,627 - INFO - Searching Google for Nav-tal 7 Levers Hardened (3 keys) - Blister Rim Locks Rim Locks (Model: 3062)
2025-05-20 12:32:24,391 - INFO - Completed 39/187 products
2025-05-20 12:32:24,392 - INFO - Processing product: Nav-tal 7 Lever Shut Height 5cm (50mm) (3 Keys) Rim Locks Rim Locks (Model: 3597)
2025-05-20 12:32:25,369 - INFO - Completed processing Nav-tal 7 Levers Hardened (2 keys) Rim Locks Rim Locks (Model: 3290). Found 0 images.
2025-05-20 12:32:25,650 - INFO - Completed processing Nav-tal 7 Levers Hardened (4 keys) Rim Locks Rim Locks (Model: 7169). Found 0 images.
2025-05-20 12:32:26,742 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:32:27,966 - INFO - Completed processing Nav-tal 7 Levers Hardened (3 keys) - Blister Rim Locks Rim Locks (Model: 3062). Found 0 images.
2025-05-20 12:32:28,730 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:32:28,731 - INFO - Searching Google for Nav-tal 7 Lever Shut Height 5cm (50mm) (3 Keys) Rim Locks Rim Locks (Model: 3597)
2025-05-20 12:32:31,666 - INFO - Completed 40/187 products
2025-05-20 12:32:31,667 - INFO - Processing product: Nav-tal 7 Lever Shut Height 3cm (30mm) (3 Keys) Rim Locks Rim Locks (Model: 3596)
2025-05-20 12:32:31,958 - INFO - Completed 41/187 products
2025-05-20 12:32:31,958 - INFO - Processing product: Nav-tal 8 Levers Deluxe Hardened (3 keys) Rim Locks Rim Locks (Model: 3282)
2025-05-20 12:32:34,805 - INFO - Completed 42/187 products
2025-05-20 12:32:34,807 - INFO - Processing product: Nav-tal 8 Levers Hardened (2 keys) Rim Locks Rim Locks (Model: 3281)
2025-05-20 12:32:35,270 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:32:35,656 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:32:35,996 - INFO - Completed processing Nav-tal 7 Lever Shut Height 5cm (50mm) (3 Keys) Rim Locks Rim Locks (Model: 3597). Found 0 images.
2025-05-20 12:32:38,480 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:32:38,481 - INFO - Searching Google for Nav-tal 8 Levers Deluxe Hardened (3 keys) Rim Locks Rim Locks (Model: 3282)
2025-05-20 12:32:39,212 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:32:39,213 - INFO - Searching Google for Nav-tal 7 Lever Shut Height 3cm (30mm) (3 Keys) Rim Locks Rim Locks (Model: 3596)
2025-05-20 12:32:40,320 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:32:42,339 - INFO - Completed 43/187 products
2025-05-20 12:32:42,339 - INFO - Processing product: Freedom 5 Levers (2 keys) Rim Locks Rim Locks (Model: 3285)
2025-05-20 12:32:44,160 - INFO - Completed processing Nav-tal 8 Levers Deluxe Hardened (3 keys) Rim Locks Rim Locks (Model: 3282). Found 0 images.
2025-05-20 12:32:44,198 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:32:44,199 - INFO - Searching Google for Nav-tal 8 Levers Hardened (2 keys) Rim Locks Rim Locks (Model: 3281)
2025-05-20 12:32:44,815 - INFO - Completed processing Nav-tal 7 Lever Shut Height 3cm (30mm) (3 Keys) Rim Locks Rim Locks (Model: 3596). Found 0 images.
2025-05-20 12:32:47,533 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:32:49,326 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:32:49,326 - INFO - Searching Google for Freedom 5 Levers (2 keys) Rim Locks Rim Locks (Model: 3285)
2025-05-20 12:32:51,181 - INFO - Completed 44/187 products
2025-05-20 12:32:51,181 - INFO - Processing product: Freedom 5 Levers (2 keys) - Blister Rim Locks Rim Locks (Model: 3064)
2025-05-20 12:32:51,540 - INFO - Completed processing Nav-tal 8 Levers Hardened (2 keys) Rim Locks Rim Locks (Model: 3281). Found 0 images.
2025-05-20 12:32:52,910 - INFO - Completed 45/187 products
2025-05-20 12:32:52,910 - INFO - Processing product: Freedom 6 Levers (2 keys) Rim Locks Rim Locks (Model: 3286)
2025-05-20 12:32:55,276 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:32:56,775 - INFO - Completed processing Freedom 5 Levers (2 keys) Rim Locks Rim Locks (Model: 3285). Found 0 images.
2025-05-20 12:32:58,138 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:32:59,147 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:32:59,147 - INFO - Searching Google for Freedom 5 Levers (2 keys) - Blister Rim Locks Rim Locks (Model: 3064)
2025-05-20 12:32:59,582 - INFO - Completed 46/187 products
2025-05-20 12:32:59,582 - INFO - Processing product: Freedom 6 Levers (2 keys) - Blister Rim Locks Rim Locks (Model: 3065)
2025-05-20 12:33:00,169 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:33:00,170 - INFO - Searching Google for Freedom 6 Levers (2 keys) Rim Locks Rim Locks (Model: 3286)
2025-05-20 12:33:03,041 - INFO - Processing product: Freedom 7 Levers (2 keys) Rim Locks Rim Locks (Model: 3287)
2025-05-20 12:33:03,043 - INFO - Completed 47/187 products
2025-05-20 12:33:03,962 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:33:04,076 - INFO - Completed processing Freedom 6 Levers (2 keys) Rim Locks Rim Locks (Model: 3286). Found 0 images.
2025-05-20 12:33:06,265 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:33:06,265 - INFO - Searching Google for Freedom 6 Levers (2 keys) - Blister Rim Locks Rim Locks (Model: 3065)
2025-05-20 12:33:06,344 - INFO - Completed processing Freedom 5 Levers (2 keys) - Blister Rim Locks Rim Locks (Model: 3064). Found 0 images.
2025-05-20 12:33:06,922 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:33:10,300 - INFO - Processing product: Freedom 7 Levers (2 keys) - Blister Rim Locks Rim Locks (Model: 3066)
2025-05-20 12:33:10,309 - INFO - Completed 48/187 products
2025-05-20 12:33:10,784 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:33:10,786 - INFO - Searching Google for Freedom 7 Levers (2 keys) Rim Locks Rim Locks (Model: 3287)
2025-05-20 12:33:12,530 - INFO - Completed 49/187 products
2025-05-20 12:33:12,530 - INFO - Processing product: Godrej No.1 - 4cm (40mm) Rim Locks Rim Locks (Model: 2774)
2025-05-20 12:33:13,311 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:33:13,839 - INFO - Completed processing Freedom 6 Levers (2 keys) - Blister Rim Locks Rim Locks (Model: 3065). Found 0 images.
2025-05-20 12:33:14,855 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:33:16,279 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:33:16,279 - INFO - Searching Google for Freedom 7 Levers (2 keys) - Blister Rim Locks Rim Locks (Model: 3066)
2025-05-20 12:33:16,616 - INFO - Completed processing Freedom 7 Levers (2 keys) Rim Locks Rim Locks (Model: 3287). Found 0 images.
2025-05-20 12:33:18,784 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:33:18,786 - INFO - Searching Google for Godrej No.1 - 4cm (40mm) Rim Locks Rim Locks (Model: 2774)
2025-05-20 12:33:20,077 - INFO - Completed 50/187 products
2025-05-20 12:33:20,077 - INFO - Processing product: Freedom 7 Levers (3 keys) Rim Locks Rim Locks (Model: 7665)
2025-05-20 12:33:22,058 - INFO - Completed processing Freedom 7 Levers (2 keys) - Blister Rim Locks Rim Locks (Model: 3066). Found 0 images.
2025-05-20 12:33:22,343 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:33:22,860 - INFO - Completed 51/187 products
2025-05-20 12:33:22,861 - INFO - Processing product: Godrej No.1 - 5cm (50mm) Rim Locks Rim Locks (Model: 2775)
2025-05-20 12:33:24,666 - INFO - Completed processing Godrej No.1 - 4cm (40mm) Rim Locks Rim Locks (Model: 2774). Found 0 images.
2025-05-20 12:33:24,676 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:33:24,677 - INFO - Searching Google for Freedom 7 Levers (3 keys) Rim Locks Rim Locks (Model: 7665)
2025-05-20 12:33:25,012 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:33:28,265 - INFO - Processing product: Godrej No.1 - 6cm (60mm) Rim Locks Rim Locks (Model: 2776)
2025-05-20 12:33:28,266 - INFO - Completed 52/187 products
2025-05-20 12:33:28,811 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:33:28,814 - INFO - Searching Google for Godrej No.1 - 5cm (50mm) Rim Locks Rim Locks (Model: 2775)
2025-05-20 12:33:30,418 - INFO - Completed processing Freedom 7 Levers (3 keys) Rim Locks Rim Locks (Model: 7665). Found 0 images.
2025-05-20 12:33:31,011 - INFO - Completed 53/187 products
2025-05-20 12:33:31,012 - INFO - Processing product: Kaditala 20cm (200mm) Texture Brown (3 Keys) Rim Locks Rim Locks (Model: 5091)
2025-05-20 12:33:33,168 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:33:34,526 - INFO - Completed processing Godrej No.1 - 5cm (50mm) Rim Locks Rim Locks (Model: 2775). Found 0 images.
2025-05-20 12:33:34,533 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:33:35,075 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:33:35,076 - INFO - Searching Google for Godrej No.1 - 6cm (60mm) Rim Locks Rim Locks (Model: 2776)
2025-05-20 12:33:36,658 - INFO - Completed 54/187 products
2025-05-20 12:33:36,658 - INFO - Processing product: Godrej No.1 - 7cm (70mm) Rim Locks Rim Locks (Model: 2777)
2025-05-20 12:33:38,673 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:33:38,674 - INFO - Searching Google for Kaditala 20cm (200mm) Texture Brown (3 Keys) Rim Locks Rim Locks (Model: 5091)
2025-05-20 12:33:39,944 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:33:40,902 - INFO - Completed processing Godrej No.1 - 6cm (60mm) Rim Locks Rim Locks (Model: 2776). Found 0 images.
2025-05-20 12:33:41,124 - INFO - Completed 55/187 products
2025-05-20 12:33:41,125 - INFO - Processing product: Sherlock 6cm (60mm) Ultra - 3 Keys (Blister) Rim Locks Rim Locks (Model: 7676)
2025-05-20 12:33:43,792 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:33:43,793 - INFO - Searching Google for Godrej No.1 - 7cm (70mm) Rim Locks Rim Locks (Model: 2777)
2025-05-20 12:33:44,169 - INFO - Completed processing Kaditala 20cm (200mm) Texture Brown (3 Keys) Rim Locks Rim Locks (Model: 5091). Found 0 images.
2025-05-20 12:33:44,394 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:33:46,322 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:33:46,323 - INFO - Searching Google for Sherlock 6cm (60mm) Ultra - 3 Keys (Blister) Rim Locks Rim Locks (Model: 7676)
2025-05-20 12:33:47,129 - INFO - Completed 56/187 products
2025-05-20 12:33:47,129 - INFO - Processing product: Kaditala 20cm (200mm) Chrome (3 Keys) Rim Locks Rim Locks (Model: 5093)
2025-05-20 12:33:49,395 - INFO - Completed processing Godrej No.1 - 7cm (70mm) Rim Locks Rim Locks (Model: 2777). Found 0 images.
2025-05-20 12:33:50,234 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:33:52,011 - INFO - Completed processing Sherlock 6cm (60mm) Ultra - 3 Keys (Blister) Rim Locks Rim Locks (Model: 7676). Found 0 images.
2025-05-20 12:33:52,544 - INFO - Completed 57/187 products
2025-05-20 12:33:52,545 - INFO - Processing product: Kaditala 27.5 cm (275mm) Texture Brown (3 Keys) Rim Locks Rim Locks (Model: 5092)
2025-05-20 12:33:53,912 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:33:53,912 - INFO - Searching Google for Kaditala 20cm (200mm) Chrome (3 Keys) Rim Locks Rim Locks (Model: 5093)
2025-05-20 12:33:55,162 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:33:55,608 - INFO - Completed 58/187 products
2025-05-20 12:33:55,611 - INFO - Processing product: Kaditala 27.5 cm (275mm) Chrome (3 Keys) Rim Locks Rim Locks (Model: 5094)
2025-05-20 12:33:57,005 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:33:57,006 - INFO - Searching Google for Kaditala 27.5 cm (275mm) Texture Brown (3 Keys) Rim Locks Rim Locks (Model: 5092)
2025-05-20 12:33:58,287 - INFO - Completed 59/187 products
2025-05-20 12:33:58,288 - INFO - Processing product: MyLock Candy (2 Keys) Rim Locks Rim Locks (Model: 6666)
2025-05-20 12:33:59,433 - INFO - Completed processing Kaditala 20cm (200mm) Chrome (3 Keys) Rim Locks Rim Locks (Model: 5093). Found 0 images.
2025-05-20 12:34:00,616 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:34:04,430 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:34:04,430 - INFO - Searching Google for MyLock Candy (2 Keys) Rim Locks Rim Locks (Model: 6666)
2025-05-20 12:34:04,520 - INFO - Completed processing Kaditala 27.5 cm (275mm) Texture Brown (3 Keys) Rim Locks Rim Locks (Model: 5092). Found 0 images.
2025-05-20 12:34:05,728 - INFO - Completed 60/187 products
2025-05-20 12:34:05,729 - INFO - Processing product: Ace Pro 5 Levers (3 Keys) Rim Locks Rim Locks (Model: 3486)
2025-05-20 12:34:05,804 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:34:07,855 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:34:07,857 - INFO - Searching Google for Kaditala 27.5 cm (275mm) Chrome (3 Keys) Rim Locks Rim Locks (Model: 5094)
2025-05-20 12:34:08,593 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:34:10,779 - INFO - Completed 61/187 products
2025-05-20 12:34:10,780 - INFO - Processing product: Round Padlock 5 Levers (3 Keys) Rim Locks Rim Locks (Model: 8147)
2025-05-20 12:34:12,122 - INFO - Completed processing MyLock Candy (2 Keys) Rim Locks Rim Locks (Model: 6666). Found 0 images.
2025-05-20 12:34:12,493 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:34:12,494 - INFO - Searching Google for Ace Pro 5 Levers (3 Keys) Rim Locks Rim Locks (Model: 3486)
2025-05-20 12:34:13,286 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:34:13,515 - INFO - Completed processing Kaditala 27.5 cm (275mm) Chrome (3 Keys) Rim Locks Rim Locks (Model: 5094). Found 0 images.
2025-05-20 12:34:16,135 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:34:16,135 - INFO - Searching Google for Round Padlock 5 Levers (3 Keys) Rim Locks Rim Locks (Model: 8147)
2025-05-20 12:34:18,709 - INFO - Completed processing Ace Pro 5 Levers (3 Keys) Rim Locks Rim Locks (Model: 3486). Found 0 images.
2025-05-20 12:34:18,789 - INFO - Completed 62/187 products
2025-05-20 12:34:18,790 - INFO - Processing product: Round Padlock 6 Levers (3 Keys) Rim Locks Rim Locks (Model: 8148)
2025-05-20 12:34:19,821 - INFO - Completed 63/187 products
2025-05-20 12:34:19,821 - INFO - Processing product: Ace Pro 6 Levers (3 Keys) Rim Locks Rim Locks (Model: 3487)
2025-05-20 12:34:21,194 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:34:22,115 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:34:23,787 - INFO - Completed processing Round Padlock 5 Levers (3 Keys) Rim Locks Rim Locks (Model: 8147). Found 0 images.
2025-05-20 12:34:25,089 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:34:25,090 - INFO - Searching Google for Round Padlock 6 Levers (3 Keys) Rim Locks Rim Locks (Model: 8148)
2025-05-20 12:34:25,906 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:34:25,906 - INFO - Searching Google for Ace Pro 6 Levers (3 Keys) Rim Locks Rim Locks (Model: 3487)
2025-05-20 12:34:26,679 - INFO - Completed 64/187 products
2025-05-20 12:34:26,680 - INFO - Processing product: Ace Pro 7 Levers (3 Keys) Rim Locks Rim Locks (Model: 3488)
2025-05-20 12:34:29,218 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:34:30,048 - INFO - Completed 65/187 products
2025-05-20 12:34:30,048 - INFO - Processing product: Square Padlock 6 Levers (4 Keys) Rim Locks Rim Locks (Model: 8151)
2025-05-20 12:34:31,269 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:34:31,270 - INFO - Searching Google for Ace Pro 7 Levers (3 Keys) Rim Locks Rim Locks (Model: 3488)
2025-05-20 12:34:32,380 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:34:32,565 - INFO - Completed processing Round Padlock 6 Levers (3 Keys) Rim Locks Rim Locks (Model: 8148). Found 0 images.
2025-05-20 12:34:33,490 - INFO - Completed processing Ace Pro 6 Levers (3 Keys) Rim Locks Rim Locks (Model: 3487). Found 0 images.
2025-05-20 12:34:36,320 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:34:36,321 - INFO - Searching Google for Square Padlock 6 Levers (4 Keys) Rim Locks Rim Locks (Model: 8151)
2025-05-20 12:34:37,600 - INFO - Completed processing Ace Pro 7 Levers (3 Keys) Rim Locks Rim Locks (Model: 3488). Found 0 images.
2025-05-20 12:34:38,860 - INFO - Completed 66/187 products
2025-05-20 12:34:38,861 - INFO - Processing product: Round Padlock 7 Levers (3 Keys) Rim Locks Rim Locks (Model: 8149)
2025-05-20 12:34:39,802 - INFO - Completed 67/187 products
2025-05-20 12:34:39,803 - INFO - Processing product: Round Padlock 10 Levers (3 Keys) Rim Locks Rim Locks (Model: 8150)
2025-05-20 12:34:41,328 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:34:42,135 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:34:43,385 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:34:43,386 - INFO - Searching Google for Round Padlock 7 Levers (3 Keys) Rim Locks Rim Locks (Model: 8149)
2025-05-20 12:34:43,418 - INFO - Completed processing Square Padlock 6 Levers (4 Keys) Rim Locks Rim Locks (Model: 8151). Found 0 images.
2025-05-20 12:34:43,813 - INFO - Completed 68/187 products
2025-05-20 12:34:43,813 - INFO - Processing product: Square Padlock 7 Levers (4 Keys) Rim Locks Rim Locks (Model: 8153)
2025-05-20 12:34:46,327 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:34:46,329 - INFO - Searching Google for Round Padlock 10 Levers (3 Keys) Rim Locks Rim Locks (Model: 8150)
2025-05-20 12:34:46,361 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:34:49,698 - INFO - Completed processing Round Padlock 7 Levers (3 Keys) Rim Locks Rim Locks (Model: 8149). Found 0 images.
2025-05-20 12:34:50,488 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:34:50,489 - INFO - Searching Google for Square Padlock 7 Levers (4 Keys) Rim Locks Rim Locks (Model: 8153)
2025-05-20 12:34:52,320 - INFO - Completed processing Round Padlock 10 Levers (3 Keys) Rim Locks Rim Locks (Model: 8150). Found 0 images.
2025-05-20 12:34:53,319 - INFO - Completed 69/187 products
2025-05-20 12:34:53,319 - INFO - Processing product: Square Padlock 8 Levers (4 Keys) Rim Locks Rim Locks (Model: 8154)
2025-05-20 12:34:55,822 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:34:55,914 - INFO - Completed processing Square Padlock 7 Levers (4 Keys) Rim Locks Rim Locks (Model: 8153). Found 0 images.
2025-05-20 12:34:57,716 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:34:57,717 - INFO - Searching Google for Square Padlock 8 Levers (4 Keys) Rim Locks Rim Locks (Model: 8154)
2025-05-20 12:34:58,434 - INFO - Completed 70/187 products
2025-05-20 12:34:58,435 - INFO - Processing product: Nav-Tal Ultra XL+ (4 Keys) Rim Locks Rim Locks (Model: 5100)
2025-05-20 12:34:58,589 - INFO - Completed 71/187 products
2025-05-20 12:34:58,590 - INFO - Processing product: Nav-Tal Ultra XL+ (4 Keys) - Blister Rim Locks Rim Locks (Model: 4081)
2025-05-20 12:35:00,867 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:35:00,984 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:35:02,249 - INFO - Completed 72/187 products
2025-05-20 12:35:02,249 - INFO - Processing product: Nav-Tal Ultra XL+ (4 Keys) Pack of 2 Rim Locks Rim Locks (Model: 4063)
2025-05-20 12:35:03,235 - INFO - Completed processing Square Padlock 8 Levers (4 Keys) Rim Locks Rim Locks (Model: 8154). Found 0 images.
2025-05-20 12:35:04,812 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:35:05,022 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:35:05,022 - INFO - Searching Google for Nav-Tal Ultra XL+ (4 Keys) Rim Locks Rim Locks (Model: 5100)
2025-05-20 12:35:05,182 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:35:05,182 - INFO - Searching Google for Nav-Tal Ultra XL+ (4 Keys) - Blister Rim Locks Rim Locks (Model: 4081)
2025-05-20 12:35:06,935 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:35:06,936 - INFO - Searching Google for Nav-Tal Ultra XL+ (4 Keys) Pack of 2 Rim Locks Rim Locks (Model: 4063)
2025-05-20 12:35:09,471 - INFO - Completed 73/187 products
2025-05-20 12:35:09,472 - INFO - Processing product: Scorpio (3 Keys) Rim Locks Rim Locks (Model: 7394)
2025-05-20 12:35:10,584 - INFO - Completed processing Nav-Tal Ultra XL+ (4 Keys) Rim Locks Rim Locks (Model: 5100). Found 0 images.
2025-05-20 12:35:11,677 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:35:12,447 - INFO - Completed processing Nav-Tal Ultra XL+ (4 Keys) - Blister Rim Locks Rim Locks (Model: 4081). Found 0 images.
2025-05-20 12:35:13,099 - INFO - Completed processing Nav-Tal Ultra XL+ (4 Keys) Pack of 2 Rim Locks Rim Locks (Model: 4063). Found 0 images.
2025-05-20 12:35:15,514 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:35:15,514 - INFO - Searching Google for Scorpio (3 Keys) Rim Locks Rim Locks (Model: 7394)
2025-05-20 12:35:18,581 - INFO - Completed 74/187 products
2025-05-20 12:35:18,584 - INFO - Processing product: Dual Access Padlock (2 MK 2 CK) Rim Locks Rim Locks (Model: 7395)
2025-05-20 12:35:18,665 - INFO - Completed 75/187 products
2025-05-20 12:35:18,665 - INFO - Processing product: Imara 8cm (80mm) (3 Keys) Rim Locks Rim Locks (Model: 6248)
2025-05-20 12:35:20,788 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:35:20,803 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:35:20,985 - INFO - Completed 76/187 products
2025-05-20 12:35:20,985 - INFO - Processing product: Imara 7cm (70mm) (3 Keys) Rim Locks Rim Locks (Model: 6247)
2025-05-20 12:35:22,634 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:35:22,634 - INFO - Searching Google for Imara 8cm (80mm) (3 Keys) Rim Locks Rim Locks (Model: 6248)
2025-05-20 12:35:23,072 - INFO - Completed processing Scorpio (3 Keys) Rim Locks Rim Locks (Model: 7394). Found 0 images.
2025-05-20 12:35:23,494 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:35:24,748 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:35:24,749 - INFO - Searching Google for Dual Access Padlock (2 MK 2 CK) Rim Locks Rim Locks (Model: 7395)
2025-05-20 12:35:27,519 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:35:27,519 - INFO - Searching Google for Imara 7cm (70mm) (3 Keys) Rim Locks Rim Locks (Model: 6247)
2025-05-20 12:35:29,337 - INFO - Completed 77/187 products
2025-05-20 12:35:29,338 - INFO - Processing product: 7cm (70mm) Duralock Ultra XL+ (3 Keys) Rim Locks Rim Locks (Model: 4066)
2025-05-20 12:35:30,523 - INFO - Completed processing Imara 8cm (80mm) (3 Keys) Rim Locks Rim Locks (Model: 6248). Found 0 images.
2025-05-20 12:35:31,803 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:35:32,056 - INFO - Completed processing Dual Access Padlock (2 MK 2 CK) Rim Locks Rim Locks (Model: 7395). Found 0 images.
2025-05-20 12:35:33,607 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:35:33,607 - INFO - Searching Google for 7cm (70mm) Duralock Ultra XL+ (3 Keys) Rim Locks Rim Locks (Model: 4066)
2025-05-20 12:35:34,978 - INFO - Completed processing Imara 7cm (70mm) (3 Keys) Rim Locks Rim Locks (Model: 6247). Found 0 images.
2025-05-20 12:35:36,736 - INFO - Completed 78/187 products
2025-05-20 12:35:36,737 - INFO - Processing product: Herculoc Plus (4 Keys) Rim Locks Rim Locks (Model: 5485)
2025-05-20 12:35:38,305 - INFO - Completed 79/187 products
2025-05-20 12:35:38,306 - INFO - Processing product: 7cm (70mm) Duralock Ultra XL+ with Common Key Rim Locks Rim Locks (Model: 4072)
2025-05-20 12:35:39,395 - INFO - Completed processing 7cm (70mm) Duralock Ultra XL+ (3 Keys) Rim Locks Rim Locks (Model: 4066). Found 0 images.
2025-05-20 12:35:39,491 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:35:41,064 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:35:41,229 - INFO - Completed 80/187 products
2025-05-20 12:35:41,229 - INFO - Processing product: 9cm (90mm) Duralock Ultra XL+ (3 Keys) Rim Locks Rim Locks (Model: 4067)
2025-05-20 12:35:41,909 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:35:41,909 - INFO - Searching Google for Herculoc Plus (4 Keys) Rim Locks Rim Locks (Model: 5485)
2025-05-20 12:35:44,057 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:35:45,491 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:35:45,491 - INFO - Searching Google for 7cm (70mm) Duralock Ultra XL+ with Common Key Rim Locks Rim Locks (Model: 4072)
2025-05-20 12:35:45,673 - INFO - Completed 81/187 products
2025-05-20 12:35:45,674 - INFO - Processing product: Centre Shutter Lock Ultra 2C Rim Locks Rim Locks (Model: 3091)
2025-05-20 12:35:47,956 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:35:47,956 - INFO - Searching Google for 9cm (90mm) Duralock Ultra XL+ (3 Keys) Rim Locks Rim Locks (Model: 4067)
2025-05-20 12:35:48,020 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:35:49,323 - INFO - Completed processing Herculoc Plus (4 Keys) Rim Locks Rim Locks (Model: 5485). Found 0 images.
2025-05-20 12:35:51,773 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:35:51,773 - INFO - Searching Google for Centre Shutter Lock Ultra 2C Rim Locks Rim Locks (Model: 3091)
2025-05-20 12:35:52,741 - INFO - Completed processing 7cm (70mm) Duralock Ultra XL+ with Common Key Rim Locks Rim Locks (Model: 4072). Found 0 images.
2025-05-20 12:35:55,451 - INFO - Completed processing 9cm (90mm) Duralock Ultra XL+ (3 Keys) Rim Locks Rim Locks (Model: 4067). Found 0 images.
2025-05-20 12:35:55,572 - INFO - Completed 82/187 products
2025-05-20 12:35:55,572 - INFO - Processing product: 9cm (90mm) Duralock Ultra XL+ with Common Key Rim Locks Rim Locks (Model: 4073)
2025-05-20 12:35:57,641 - INFO - Completed processing Centre Shutter Lock Ultra 2C Rim Locks Rim Locks (Model: 3091). Found 0 images.
2025-05-20 12:35:58,407 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:35:58,992 - INFO - Completed 83/187 products
2025-05-20 12:35:58,993 - INFO - Processing product: Side Shutter Lock Ultra (New) Rim Locks Rim Locks (Model: 2285)
2025-05-20 12:36:00,254 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:36:00,254 - INFO - Searching Google for 9cm (90mm) Duralock Ultra XL+ with Common Key Rim Locks Rim Locks (Model: 4073)
2025-05-20 12:36:01,410 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:36:01,729 - INFO - Completed 84/187 products
2025-05-20 12:36:01,730 - INFO - Processing product: Centre Shutter Lock Ultra 1C Rim Locks Rim Locks (Model: 3090)
2025-05-20 12:36:03,926 - INFO - Completed 85/187 products
2025-05-20 12:36:03,927 - INFO - Processing product: Universal Furniture Lock Rim Locks Rim Locks (Model: 2333)
2025-05-20 12:36:04,299 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:36:05,359 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:36:05,360 - INFO - Searching Google for Side Shutter Lock Ultra (New) Rim Locks Rim Locks (Model: 2285)
2025-05-20 12:36:06,012 - INFO - Completed processing 9cm (90mm) Duralock Ultra XL+ with Common Key Rim Locks Rim Locks (Model: 4073). Found 0 images.
2025-05-20 12:36:06,185 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:36:08,871 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:36:08,872 - INFO - Searching Google for Centre Shutter Lock Ultra 1C Rim Locks Rim Locks (Model: 3090)
2025-05-20 12:36:09,026 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:36:09,026 - INFO - Searching Google for Universal Furniture Lock Rim Locks Rim Locks (Model: 2333)
2025-05-20 12:36:11,848 - INFO - Completed processing Side Shutter Lock Ultra (New) Rim Locks Rim Locks (Model: 2285). Found 0 images.
2025-05-20 12:36:12,254 - INFO - Completed 86/187 products
2025-05-20 12:36:12,255 - INFO - Processing product: Side Shutter Lock Ultra Pack of 2 in Common Key (New) Rim Locks Rim Locks (Model: 2286)
2025-05-20 12:36:14,370 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:36:16,494 - INFO - Completed processing Universal Furniture Lock Rim Locks Rim Locks (Model: 2333). Found 0 images.
2025-05-20 12:36:16,805 - INFO - Completed processing Centre Shutter Lock Ultra 1C Rim Locks Rim Locks (Model: 3090). Found 0 images.
2025-05-20 12:36:18,052 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:36:18,052 - INFO - Searching Google for Side Shutter Lock Ultra Pack of 2 in Common Key (New) Rim Locks Rim Locks (Model: 2286)
2025-05-20 12:36:18,107 - INFO - Completed 87/187 products
2025-05-20 12:36:18,107 - INFO - Processing product: 2.5cm (25mm)Pin Cylinder Wardrobe Lock Texture Brown Mortise Locks Mortise Locks (Model: 2576)
2025-05-20 12:36:20,501 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:36:22,320 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:36:22,321 - INFO - Searching Google for 2.5cm (25mm)Pin Cylinder Wardrobe Lock Texture Brown Mortise Locks Mortise Locks (Model: 2576)
2025-05-20 12:36:22,704 - INFO - Completed 88/187 products
2025-05-20 12:36:22,704 - INFO - Processing product: Almirah Lock Rim Locks Rim Locks (Model: 3936)
2025-05-20 12:36:23,072 - INFO - Completed 89/187 products
2025-05-20 12:36:23,073 - INFO - Processing product: 3.2cm (32mm) Pin Cylinder Wardrobe Lock Texture Brown Mortise Locks Mortise Locks (Model: 2577)
2025-05-20 12:36:23,617 - INFO - Completed processing Side Shutter Lock Ultra Pack of 2 in Common Key (New) Rim Locks Rim Locks (Model: 2286). Found 0 images.
2025-05-20 12:36:25,282 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:36:25,540 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:36:27,816 - INFO - Completed processing 2.5cm (25mm)Pin Cylinder Wardrobe Lock Texture Brown Mortise Locks Mortise Locks (Model: 2576). Found 0 images.
2025-05-20 12:36:29,331 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:36:29,332 - INFO - Searching Google for Almirah Lock Rim Locks Rim Locks (Model: 3936)
2025-05-20 12:36:29,701 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:36:29,702 - INFO - Searching Google for 3.2cm (32mm) Pin Cylinder Wardrobe Lock Texture Brown Mortise Locks Mortise Locks (Model: 2577)
2025-05-20 12:36:29,881 - INFO - Completed 90/187 products
2025-05-20 12:36:29,882 - INFO - Processing product: Rim Lock 1CK (Inside Opening) - Beige Brown Mortise Locks Mortise Locks (Model: 9104)
2025-05-20 12:36:32,119 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:36:33,848 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:36:33,848 - INFO - Searching Google for Rim Lock 1CK (Inside Opening) - Beige Brown Mortise Locks Mortise Locks (Model: 9104)
2025-05-20 12:36:34,127 - INFO - Completed 91/187 products
2025-05-20 12:36:34,128 - INFO - Processing product: Rim Lock 1CK (Outside Opening) - Beige Brown Mortise Locks Mortise Locks (Model: 9105)
2025-05-20 12:36:34,861 - INFO - Completed processing 3.2cm (32mm) Pin Cylinder Wardrobe Lock Texture Brown Mortise Locks Mortise Locks (Model: 2577). Found 0 images.
2025-05-20 12:36:35,319 - INFO - Completed processing Almirah Lock Rim Locks Rim Locks (Model: 3936). Found 0 images.
2025-05-20 12:36:36,368 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:36:39,789 - INFO - Completed processing Rim Lock 1CK (Inside Opening) - Beige Brown Mortise Locks Mortise Locks (Model: 9104). Found 0 images.
2025-05-20 12:36:40,271 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:36:40,272 - INFO - Searching Google for Rim Lock 1CK (Outside Opening) - Beige Brown Mortise Locks Mortise Locks (Model: 9105)
2025-05-20 12:36:41,599 - INFO - Completed 92/187 products
2025-05-20 12:36:41,600 - INFO - Processing product: Rim Lock 1CK (Inside Opening) - Metallic Satin Mortise Locks Mortise Locks (Model: 8351)
2025-05-20 12:36:42,873 - INFO - Completed 93/187 products
2025-05-20 12:36:42,873 - INFO - Processing product: Grill Door Lock Mortise Locks Mortise Locks (Model: 3572)
2025-05-20 12:36:43,915 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:36:45,006 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:36:45,702 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:36:45,702 - INFO - Searching Google for Rim Lock 1CK (Inside Opening) - Metallic Satin Mortise Locks Mortise Locks (Model: 8351)
2025-05-20 12:36:46,492 - INFO - Completed 94/187 products
2025-05-20 12:36:46,492 - INFO - Processing product: Rim Lock 1CK (Outside Opening) - Metallic Satin Mortise Locks Mortise Locks (Model: 8352)
2025-05-20 12:36:46,658 - INFO - Completed processing Rim Lock 1CK (Outside Opening) - Beige Brown Mortise Locks Mortise Locks (Model: 9105). Found 0 images.
2025-05-20 12:36:48,719 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:36:49,187 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:36:49,188 - INFO - Searching Google for Grill Door Lock Mortise Locks Mortise Locks (Model: 3572)
2025-05-20 12:36:51,639 - INFO - Completed processing Rim Lock 1CK (Inside Opening) - Metallic Satin Mortise Locks Mortise Locks (Model: 8351). Found 0 images.
2025-05-20 12:36:52,837 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:36:52,837 - INFO - Searching Google for Rim Lock 1CK (Outside Opening) - Metallic Satin Mortise Locks Mortise Locks (Model: 8352)
2025-05-20 12:36:52,901 - INFO - Completed 95/187 products
2025-05-20 12:36:52,902 - INFO - Processing product: Khatakhat Lock Cherry Brown (New) Mortise Locks Mortise Locks (Model: 2292)
2025-05-20 12:36:55,194 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:36:56,847 - INFO - Completed processing Grill Door Lock Mortise Locks Mortise Locks (Model: 3572). Found 0 images.
2025-05-20 12:36:57,418 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:36:57,418 - INFO - Searching Google for Khatakhat Lock Cherry Brown (New) Mortise Locks Mortise Locks (Model: 2292)
2025-05-20 12:36:57,919 - INFO - Completed 96/187 products
2025-05-20 12:36:57,919 - INFO - Processing product: Khatakhat Lock Silver Grey (New) Mortise Locks Mortise Locks (Model: 2293)
2025-05-20 12:36:58,718 - INFO - Completed processing Rim Lock 1CK (Outside Opening) - Metallic Satin Mortise Locks Mortise Locks (Model: 8352). Found 0 images.
2025-05-20 12:37:00,110 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:37:03,548 - INFO - Completed 97/187 products
2025-05-20 12:37:03,548 - INFO - Processing product: Super Night Latch (Inside Opening) Mortise Locks Mortise Locks (Model: 8812)
2025-05-20 12:37:03,866 - INFO - Completed processing Khatakhat Lock Cherry Brown (New) Mortise Locks Mortise Locks (Model: 2292). Found 0 images.
2025-05-20 12:37:04,035 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:37:04,036 - INFO - Searching Google for Khatakhat Lock Silver Grey (New) Mortise Locks Mortise Locks (Model: 2293)
2025-05-20 12:37:04,928 - INFO - Completed 98/187 products
2025-05-20 12:37:04,931 - INFO - Processing product: Premium Night Latch - Brushed Steel (Outside Opening) Mortise Locks Mortise Locks (Model: 7667)
2025-05-20 12:37:06,232 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:37:07,223 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:37:08,152 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:37:08,153 - INFO - Searching Google for Super Night Latch (Inside Opening) Mortise Locks Mortise Locks (Model: 8812)
2025-05-20 12:37:09,731 - INFO - Completed processing Khatakhat Lock Silver Grey (New) Mortise Locks Mortise Locks (Model: 2293). Found 0 images.
2025-05-20 12:37:10,417 - INFO - Processing product: Premium Night Latch - Brushed Steel (Inside Opening) Mortise Locks Mortise Locks (Model: 7666)
2025-05-20 12:37:10,422 - INFO - Completed 99/187 products
2025-05-20 12:37:11,608 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:37:11,609 - INFO - Searching Google for Premium Night Latch - Brushed Steel (Outside Opening) Mortise Locks Mortise Locks (Model: 7667)
2025-05-20 12:37:13,309 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:37:14,266 - INFO - Completed processing Super Night Latch (Inside Opening) Mortise Locks Mortise Locks (Model: 8812). Found 0 images.
2025-05-20 12:37:17,081 - INFO - Processing product: Super Night Latch (Outside Opening) Mortise Locks Mortise Locks (Model: 8813)
2025-05-20 12:37:17,082 - INFO - Completed 100/187 products
2025-05-20 12:37:17,523 - INFO - Completed processing Premium Night Latch - Brushed Steel (Outside Opening) Mortise Locks Mortise Locks (Model: 7667). Found 0 images.
2025-05-20 12:37:17,553 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:37:17,554 - INFO - Searching Google for Premium Night Latch - Brushed Steel (Inside Opening) Mortise Locks Mortise Locks (Model: 7666)
2025-05-20 12:37:20,140 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:37:20,584 - INFO - Completed 101/187 products
2025-05-20 12:37:20,584 - INFO - Processing product: 1CK I/O Satin Nickel (Carton) Mortise Locks Mortise Locks (Model: 8127)
2025-05-20 12:37:23,065 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:37:23,867 - INFO - Completed processing Premium Night Latch - Brushed Steel (Inside Opening) Mortise Locks Mortise Locks (Model: 7666). Found 0 images.
2025-05-20 12:37:23,895 - INFO - Processing product: Night Latch (Outside Opening) Mortise Locks Mortise Locks (Model: 8814)
2025-05-20 12:37:23,897 - INFO - Completed 102/187 products
2025-05-20 12:37:24,284 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:37:24,285 - INFO - Searching Google for Super Night Latch (Outside Opening) Mortise Locks Mortise Locks (Model: 8813)
2025-05-20 12:37:26,441 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:37:27,416 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:37:27,417 - INFO - Searching Google for 1CK I/O Satin Nickel (Carton) Mortise Locks Mortise Locks (Model: 8127)
2025-05-20 12:37:30,392 - INFO - Completed processing Super Night Latch (Outside Opening) Mortise Locks Mortise Locks (Model: 8813). Found 0 images.
2025-05-20 12:37:30,475 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:37:30,478 - INFO - Searching Google for Night Latch (Outside Opening) Mortise Locks Mortise Locks (Model: 8814)
2025-05-20 12:37:30,663 - INFO - Completed 103/187 products
2025-05-20 12:37:30,663 - INFO - Processing product: Night Latch (Inside Opening) Mortise Locks Mortise Locks (Model: 8261)
2025-05-20 12:37:32,856 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:37:33,488 - INFO - Completed processing 1CK I/O Satin Nickel (Carton) Mortise Locks Mortise Locks (Model: 8127). Found 0 images.
2025-05-20 12:37:35,805 - INFO - Completed processing Night Latch (Outside Opening) Mortise Locks Mortise Locks (Model: 8814). Found 0 images.
2025-05-20 12:37:36,507 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:37:36,508 - INFO - Searching Google for Night Latch (Inside Opening) Mortise Locks Mortise Locks (Model: 8261)
2025-05-20 12:37:36,889 - INFO - Completed 104/187 products
2025-05-20 12:37:36,890 - INFO - Processing product: 1CK Satin Nickel O/O (Carton) Mortise Locks Mortise Locks (Model: 8129)
2025-05-20 12:37:39,092 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:37:40,895 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:37:40,896 - INFO - Searching Google for 1CK Satin Nickel O/O (Carton) Mortise Locks Mortise Locks (Model: 8129)
2025-05-20 12:37:41,567 - INFO - Completed 105/187 products
2025-05-20 12:37:41,567 - INFO - Processing product: 2C Satin Nickel I/O (Carton) Mortise Locks Mortise Locks (Model: 8130)
2025-05-20 12:37:42,418 - INFO - Completed processing Night Latch (Inside Opening) Mortise Locks Mortise Locks (Model: 8261). Found 0 images.
2025-05-20 12:37:43,265 - INFO - Completed 106/187 products
2025-05-20 12:37:43,266 - INFO - Processing product: 1CK Deadbolt Satin Nickel (Carton) Mortise Locks Mortise Locks (Model: 8128)
2025-05-20 12:37:43,967 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:37:45,417 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:37:46,244 - INFO - Completed processing 1CK Satin Nickel O/O (Carton) Mortise Locks Mortise Locks (Model: 8129). Found 0 images.
2025-05-20 12:37:47,664 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:37:47,665 - INFO - Searching Google for 2C Satin Nickel I/O (Carton) Mortise Locks Mortise Locks (Model: 8130)
2025-05-20 12:37:48,635 - INFO - Completed 107/187 products
2025-05-20 12:37:48,635 - INFO - Processing product: 2C Satin Nickel Deadbolt (Carton) Mortise Locks Mortise Locks (Model: 8131)
2025-05-20 12:37:49,197 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:37:49,197 - INFO - Searching Google for 1CK Deadbolt Satin Nickel (Carton) Mortise Locks Mortise Locks (Model: 8128)
2025-05-20 12:37:51,271 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:37:52,493 - INFO - Completed 108/187 products
2025-05-20 12:37:52,494 - INFO - Processing product: 1CK Beige Brown I/O (Carton) Mortise Locks Mortise Locks (Model: 8133)
2025-05-20 12:37:54,964 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:37:55,265 - INFO - Completed processing 2C Satin Nickel I/O (Carton) Mortise Locks Mortise Locks (Model: 8130). Found 0 images.
2025-05-20 12:37:55,361 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:37:55,361 - INFO - Searching Google for 2C Satin Nickel Deadbolt (Carton) Mortise Locks Mortise Locks (Model: 8131)
2025-05-20 12:37:56,724 - INFO - Completed processing 1CK Deadbolt Satin Nickel (Carton) Mortise Locks Mortise Locks (Model: 8128). Found 0 images.
2025-05-20 12:37:58,984 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:37:58,985 - INFO - Searching Google for 1CK Beige Brown I/O (Carton) Mortise Locks Mortise Locks (Model: 8133)
2025-05-20 12:38:00,790 - INFO - Completed processing 2C Satin Nickel Deadbolt (Carton) Mortise Locks Mortise Locks (Model: 8131). Found 0 images.
2025-05-20 12:38:01,692 - INFO - Completed 109/187 products
2025-05-20 12:38:01,693 - INFO - Processing product: 1CK Beige Brown O/O (Carton) Mortise Locks Mortise Locks (Model: 8135)
2025-05-20 12:38:02,993 - INFO - Completed 110/187 products
2025-05-20 12:38:02,994 - INFO - Processing product: 1CK Beige Brown Deadbolt (Carton) Mortise Locks Mortise Locks (Model: 8134)
2025-05-20 12:38:03,984 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:38:05,257 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:38:05,438 - INFO - Completed processing 1CK Beige Brown I/O (Carton) Mortise Locks Mortise Locks (Model: 8133). Found 0 images.
2025-05-20 12:38:05,813 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:38:05,814 - INFO - Searching Google for 1CK Beige Brown O/O (Carton) Mortise Locks Mortise Locks (Model: 8135)
2025-05-20 12:38:07,078 - INFO - Completed 111/187 products
2025-05-20 12:38:07,078 - INFO - Processing product: 2C Beige Brown Deadbolt (Carton) Mortise Locks Mortise Locks (Model: 8137)
2025-05-20 12:38:09,406 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:38:09,466 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:38:09,468 - INFO - Searching Google for 1CK Beige Brown Deadbolt (Carton) Mortise Locks Mortise Locks (Model: 8134)
2025-05-20 12:38:11,555 - INFO - Completed processing 1CK Beige Brown O/O (Carton) Mortise Locks Mortise Locks (Model: 8135). Found 0 images.
2025-05-20 12:38:12,116 - INFO - Completed 112/187 products
2025-05-20 12:38:12,117 - INFO - Processing product: 1CK Antique Brass I/O (Blister) Mortise Locks Mortise Locks (Model: 8111)
2025-05-20 12:38:13,696 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:38:13,697 - INFO - Searching Google for 2C Beige Brown Deadbolt (Carton) Mortise Locks Mortise Locks (Model: 8137)
2025-05-20 12:38:14,534 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:38:15,357 - INFO - Completed processing 1CK Beige Brown Deadbolt (Carton) Mortise Locks Mortise Locks (Model: 8134). Found 0 images.
2025-05-20 12:38:16,291 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:38:16,291 - INFO - Searching Google for 1CK Antique Brass I/O (Blister) Mortise Locks Mortise Locks (Model: 8111)
2025-05-20 12:38:17,810 - INFO - Completed 113/187 products
2025-05-20 12:38:17,812 - INFO - Processing product: 1CK Antique Brass Deadbolt (Carton) Mortise Locks Mortise Locks (Model: 8112)
2025-05-20 12:38:20,238 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:38:21,056 - INFO - Completed processing 2C Beige Brown Deadbolt (Carton) Mortise Locks Mortise Locks (Model: 8137). Found 0 images.
2025-05-20 12:38:21,590 - INFO - Completed 114/187 products
2025-05-20 12:38:21,591 - INFO - Processing product: 2C Antique Brass Deadbolt (Carton) Mortise Locks Mortise Locks (Model: 8115)
2025-05-20 12:38:22,692 - INFO - Completed processing 1CK Antique Brass I/O (Blister) Mortise Locks Mortise Locks (Model: 8111). Found 0 images.
2025-05-20 12:38:23,836 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:38:24,244 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:38:24,247 - INFO - Searching Google for 1CK Antique Brass Deadbolt (Carton) Mortise Locks Mortise Locks (Model: 8112)
2025-05-20 12:38:26,048 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:38:26,049 - INFO - Searching Google for 2C Antique Brass Deadbolt (Carton) Mortise Locks Mortise Locks (Model: 8115)
2025-05-20 12:38:27,478 - INFO - Completed 115/187 products
2025-05-20 12:38:27,479 - INFO - Processing product: 1CK Brushed Steel I/O (Blister) Mortise Locks Mortise Locks (Model: 8116)
2025-05-20 12:38:28,959 - INFO - Completed 116/187 products
2025-05-20 12:38:28,960 - INFO - Processing product: Ultra XL+ Vertibolt 1CK Antique Brass (Blister) Mortise Locks Mortise Locks (Model: 4290)
2025-05-20 12:38:29,609 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:38:30,217 - INFO - Completed processing 1CK Antique Brass Deadbolt (Carton) Mortise Locks Mortise Locks (Model: 8112). Found 0 images.
2025-05-20 12:38:31,147 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:38:32,013 - INFO - Completed processing 2C Antique Brass Deadbolt (Carton) Mortise Locks Mortise Locks (Model: 8115). Found 0 images.
2025-05-20 12:38:33,555 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:38:33,555 - INFO - Searching Google for 1CK Brushed Steel I/O (Blister) Mortise Locks Mortise Locks (Model: 8116)
2025-05-20 12:38:34,861 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:38:34,861 - INFO - Searching Google for Ultra XL+ Vertibolt 1CK Antique Brass (Blister) Mortise Locks Mortise Locks (Model: 4290)
2025-05-20 12:38:36,529 - INFO - Completed 117/187 products
2025-05-20 12:38:36,529 - INFO - Processing product: Ultra XL+ Vertibolt 1CK Satin Nickel (Blister) Mortise Locks Mortise Locks (Model: 4291)
2025-05-20 12:38:38,240 - INFO - Completed 118/187 products
2025-05-20 12:38:38,241 - INFO - Processing product: Ultra XL+ Vertibolt 1CK Texture Brown (Blister) Mortise Locks Mortise Locks (Model: 3092)
2025-05-20 12:38:38,780 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:38:40,619 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:38:41,011 - INFO - Completed processing 1CK Brushed Steel I/O (Blister) Mortise Locks Mortise Locks (Model: 8116). Found 0 images.
2025-05-20 12:38:41,127 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:38:41,129 - INFO - Searching Google for Ultra XL+ Vertibolt 1CK Satin Nickel (Blister) Mortise Locks Mortise Locks (Model: 4291)
2025-05-20 12:38:42,531 - INFO - Completed processing Ultra XL+ Vertibolt 1CK Antique Brass (Blister) Mortise Locks Mortise Locks (Model: 4290). Found 0 images.
2025-05-20 12:38:44,765 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:38:44,766 - INFO - Searching Google for Ultra XL+ Vertibolt 1CK Texture Brown (Blister) Mortise Locks Mortise Locks (Model: 3092)
2025-05-20 12:38:46,947 - INFO - Completed processing Ultra XL+ Vertibolt 1CK Satin Nickel (Blister) Mortise Locks Mortise Locks (Model: 4291). Found 0 images.
2025-05-20 12:38:47,326 - INFO - Completed 119/187 products
2025-05-20 12:38:47,326 - INFO - Processing product: Ultra XL+ Vertibolt 2C Antique Brass (Blister) Mortise Locks Mortise Locks (Model: 4292)
2025-05-20 12:38:48,825 - INFO - Completed 120/187 products
2025-05-20 12:38:48,825 - INFO - Processing product: Altrix 2C Royal Titanium Mortise Locks Mortise Locks (Model: 3228)
2025-05-20 12:38:49,546 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:38:50,098 - INFO - Completed processing Ultra XL+ Vertibolt 1CK Texture Brown (Blister) Mortise Locks Mortise Locks (Model: 3092). Found 0 images.
2025-05-20 12:38:51,268 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:38:51,509 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:38:51,509 - INFO - Searching Google for Ultra XL+ Vertibolt 2C Antique Brass (Blister) Mortise Locks Mortise Locks (Model: 4292)
2025-05-20 12:38:53,187 - INFO - Completed 121/187 products
2025-05-20 12:38:53,188 - INFO - Processing product: Altrix 2C Deadbolt Royal Titanium Mortise Locks Mortise Locks (Model: 3229)
2025-05-20 12:38:55,430 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:38:55,431 - INFO - Searching Google for Altrix 2C Royal Titanium Mortise Locks Mortise Locks (Model: 3228)
2025-05-20 12:38:55,892 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:38:56,455 - INFO - Completed 122/187 products
2025-05-20 12:38:56,456 - INFO - Processing product: Altrix 1CK Milano Bronze Mortise Locks Mortise Locks (Model: 3231)
2025-05-20 12:38:57,676 - INFO - Completed processing Ultra XL+ Vertibolt 2C Antique Brass (Blister) Mortise Locks Mortise Locks (Model: 4292). Found 0 images.
2025-05-20 12:38:59,072 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:38:59,960 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:38:59,960 - INFO - Searching Google for Altrix 2C Deadbolt Royal Titanium Mortise Locks Mortise Locks (Model: 3229)
2025-05-20 12:39:01,665 - INFO - Completed processing Altrix 2C Royal Titanium Mortise Locks Mortise Locks (Model: 3228). Found 0 images.
2025-05-20 12:39:02,337 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:39:02,337 - INFO - Searching Google for Altrix 1CK Milano Bronze Mortise Locks Mortise Locks (Model: 3231)
2025-05-20 12:39:05,712 - INFO - Completed processing Altrix 2C Deadbolt Royal Titanium Mortise Locks Mortise Locks (Model: 3229). Found 0 images.
2025-05-20 12:39:06,174 - INFO - Completed 123/187 products
2025-05-20 12:39:06,175 - INFO - Processing product: Altrix 1CK Deadbolt Milano Bronze Mortise Locks Mortise Locks (Model: 3232)
2025-05-20 12:39:08,720 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:39:09,211 - INFO - Completed 124/187 products
2025-05-20 12:39:09,212 - INFO - Processing product: Altrix 1CK Outside Opening Milano Bronze Mortise Locks Mortise Locks (Model: 3236)
2025-05-20 12:39:09,685 - INFO - Completed processing Altrix 1CK Milano Bronze Mortise Locks Mortise Locks (Model: 3231). Found 0 images.
2025-05-20 12:39:11,886 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:39:12,089 - INFO - Completed 125/187 products
2025-05-20 12:39:12,089 - INFO - Processing product: Altrix 2C Milano Bronze Mortise Locks Mortise Locks (Model: 3233)
2025-05-20 12:39:12,679 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:39:12,681 - INFO - Searching Google for Altrix 1CK Deadbolt Milano Bronze Mortise Locks Mortise Locks (Model: 3232)
2025-05-20 12:39:14,369 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:39:14,369 - INFO - Searching Google for Altrix 1CK Outside Opening Milano Bronze Mortise Locks Mortise Locks (Model: 3236)
2025-05-20 12:39:15,207 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:39:15,910 - INFO - Completed 126/187 products
2025-05-20 12:39:15,910 - INFO - Processing product: Altrix 2C Deadbolt Milano Bronze Mortise Locks Mortise Locks (Model: 3234)
2025-05-20 12:39:18,191 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:39:18,523 - INFO - Completed processing Altrix 1CK Deadbolt Milano Bronze Mortise Locks Mortise Locks (Model: 3232). Found 0 images.
2025-05-20 12:39:19,162 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:39:19,163 - INFO - Searching Google for Altrix 2C Milano Bronze Mortise Locks Mortise Locks (Model: 3233)
2025-05-20 12:39:20,364 - INFO - Completed processing Altrix 1CK Outside Opening Milano Bronze Mortise Locks Mortise Locks (Model: 3236). Found 0 images.
2025-05-20 12:39:22,509 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:39:22,509 - INFO - Searching Google for Altrix 2C Deadbolt Milano Bronze Mortise Locks Mortise Locks (Model: 3234)
2025-05-20 12:39:24,549 - INFO - Completed processing Altrix 2C Milano Bronze Mortise Locks Mortise Locks (Model: 3233). Found 0 images.
2025-05-20 12:39:25,293 - INFO - Completed 127/187 products
2025-05-20 12:39:25,294 - INFO - Processing product: Pentabolt 1CK I/O Satin Nickel Mortise Locks Mortise Locks (Model: 3345)
2025-05-20 12:39:26,610 - INFO - Completed 128/187 products
2025-05-20 12:39:26,612 - INFO - Processing product: Pentabolt 1CK DB Satin Nickel Mortise Locks Mortise Locks (Model: 3346)
2025-05-20 12:39:27,822 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:39:28,095 - INFO - Completed processing Altrix 2C Deadbolt Milano Bronze Mortise Locks Mortise Locks (Model: 3234). Found 0 images.
2025-05-20 12:39:29,126 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:39:29,596 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:39:29,596 - INFO - Searching Google for Pentabolt 1CK I/O Satin Nickel Mortise Locks Mortise Locks (Model: 3345)
2025-05-20 12:39:30,823 - INFO - Completed 129/187 products
2025-05-20 12:39:30,824 - INFO - Processing product: Pentabolt 1CK O/O Satin Nickel Mortise Locks Mortise Locks (Model: 3347)
2025-05-20 12:39:33,111 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:39:33,112 - INFO - Searching Google for Pentabolt 1CK DB Satin Nickel Mortise Locks Mortise Locks (Model: 3346)
2025-05-20 12:39:34,341 - INFO - Completed 130/187 products
2025-05-20 12:39:34,342 - INFO - Processing product: Pentabolt 1CK DB Antique Brass Mortise Locks Mortise Locks (Model: 3348)
2025-05-20 12:39:34,494 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:39:35,794 - INFO - Completed processing Pentabolt 1CK I/O Satin Nickel Mortise Locks Mortise Locks (Model: 3345). Found 0 images.
2025-05-20 12:39:36,816 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:39:38,105 - INFO - Completed processing Pentabolt 1CK DB Satin Nickel Mortise Locks Mortise Locks (Model: 3346). Found 0 images.
2025-05-20 12:39:38,610 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:39:38,610 - INFO - Searching Google for Pentabolt 1CK O/O Satin Nickel Mortise Locks Mortise Locks (Model: 3347)
2025-05-20 12:39:38,950 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:39:38,950 - INFO - Searching Google for Pentabolt 1CK DB Antique Brass Mortise Locks Mortise Locks (Model: 3348)
2025-05-20 12:39:42,071 - INFO - Completed 131/187 products
2025-05-20 12:39:42,071 - INFO - Processing product: Pentabolt 1CK I/O Antique Brass Mortise Locks Mortise Locks (Model: 3349)
2025-05-20 12:39:44,191 - INFO - Completed processing Pentabolt 1CK O/O Satin Nickel Mortise Locks Mortise Locks (Model: 3347). Found 0 images.
2025-05-20 12:39:44,247 - INFO - Completed processing Pentabolt 1CK DB Antique Brass Mortise Locks Mortise Locks (Model: 3348). Found 0 images.
2025-05-20 12:39:44,426 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:39:45,180 - INFO - Completed 132/187 products
2025-05-20 12:39:45,181 - INFO - Processing product: Pentabolt 2C DB Satin Nickel Mortise Locks Mortise Locks (Model: 3380)
2025-05-20 12:39:46,613 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:39:46,614 - INFO - Searching Google for Pentabolt 1CK I/O Antique Brass Mortise Locks Mortise Locks (Model: 3349)
2025-05-20 12:39:47,779 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:39:50,524 - INFO - Processing product: Pentabolt 2C DB Antique Brass Mortise Locks Mortise Locks (Model: 3381)
2025-05-20 12:39:50,524 - INFO - Completed 133/187 products
2025-05-20 12:39:50,555 - INFO - Completed 134/187 products
2025-05-20 12:39:50,555 - INFO - Processing product: Pentabolt Aries 1CK I/O Roman Silver Mortise Locks Mortise Locks (Model: 6359)
2025-05-20 12:39:51,681 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:39:51,682 - INFO - Searching Google for Pentabolt 2C DB Satin Nickel Mortise Locks Mortise Locks (Model: 3380)
2025-05-20 12:39:51,981 - INFO - Completed processing Pentabolt 1CK I/O Antique Brass Mortise Locks Mortise Locks (Model: 3349). Found 0 images.
2025-05-20 12:39:53,425 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:39:53,474 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:39:55,322 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:39:55,323 - INFO - Searching Google for Pentabolt 2C DB Antique Brass Mortise Locks Mortise Locks (Model: 3381)
2025-05-20 12:39:57,562 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:39:57,563 - INFO - Searching Google for Pentabolt Aries 1CK I/O Roman Silver Mortise Locks Mortise Locks (Model: 6359)
2025-05-20 12:39:57,685 - INFO - Completed processing Pentabolt 2C DB Satin Nickel Mortise Locks Mortise Locks (Model: 3380). Found 0 images.
2025-05-20 12:39:58,269 - INFO - Completed 135/187 products
2025-05-20 12:39:58,270 - INFO - Processing product: Pentabolt Aries 1CK DB Roman Silver Mortise Locks Mortise Locks (Model: 6360)
2025-05-20 12:40:00,724 - INFO - Completed processing Pentabolt 2C DB Antique Brass Mortise Locks Mortise Locks (Model: 3381). Found 0 images.
2025-05-20 12:40:00,727 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:40:03,059 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:40:03,059 - INFO - Searching Google for Pentabolt Aries 1CK DB Roman Silver Mortise Locks Mortise Locks (Model: 6360)
2025-05-20 12:40:03,972 - INFO - Completed 136/187 products
2025-05-20 12:40:03,973 - INFO - Processing product: Pentabolt Aries 2C DB Roman Silver Mortise Locks Mortise Locks (Model: 6361)
2025-05-20 12:40:04,877 - INFO - Completed processing Pentabolt Aries 1CK I/O Roman Silver Mortise Locks Mortise Locks (Model: 6359). Found 0 images.
2025-05-20 12:40:06,499 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:40:07,036 - INFO - Completed 137/187 products
2025-05-20 12:40:07,036 - INFO - Processing product: Pentabolt Aries 1CK DB Vegas Gold Mortise Locks Mortise Locks (Model: 6363)
2025-05-20 12:40:09,458 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:40:10,421 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:40:10,422 - INFO - Searching Google for Pentabolt Aries 2C DB Roman Silver Mortise Locks Mortise Locks (Model: 6361)
2025-05-20 12:40:10,443 - INFO - Completed processing Pentabolt Aries 1CK DB Roman Silver Mortise Locks Mortise Locks (Model: 6360). Found 0 images.
2025-05-20 12:40:11,163 - INFO - Completed 138/187 products
2025-05-20 12:40:11,163 - INFO - Processing product: Pentabolt Aries 1CK I/O Vegas Gold Mortise Locks Mortise Locks (Model: 6362)
2025-05-20 12:40:12,861 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:40:12,861 - INFO - Searching Google for Pentabolt Aries 1CK DB Vegas Gold Mortise Locks Mortise Locks (Model: 6363)
2025-05-20 12:40:15,103 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:40:16,837 - INFO - Completed 139/187 products
2025-05-20 12:40:16,838 - INFO - Processing product: Retrofit Adaptor - Type A Mortise Locks Mortise Locks (Model: 6245)
2025-05-20 12:40:17,958 - INFO - Completed processing Pentabolt Aries 2C DB Roman Silver Mortise Locks Mortise Locks (Model: 6361). Found 0 images.
2025-05-20 12:40:19,012 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:40:19,013 - INFO - Searching Google for Pentabolt Aries 1CK I/O Vegas Gold Mortise Locks Mortise Locks (Model: 6362)
2025-05-20 12:40:19,622 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:40:20,763 - INFO - Completed processing Pentabolt Aries 1CK DB Vegas Gold Mortise Locks Mortise Locks (Model: 6363). Found 0 images.
2025-05-20 12:40:23,516 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:40:23,516 - INFO - Searching Google for Retrofit Adaptor - Type A Mortise Locks Mortise Locks (Model: 6245)
2025-05-20 12:40:24,638 - INFO - Completed 140/187 products
2025-05-20 12:40:24,638 - INFO - Processing product: Pentabolt Aries 2C DB Vegas Gold Mortise Locks Mortise Locks (Model: 6364)
2025-05-20 12:40:26,710 - INFO - Completed processing Pentabolt Aries 1CK I/O Vegas Gold Mortise Locks Mortise Locks (Model: 6362). Found 0 images.
2025-05-20 12:40:27,069 - INFO - Completed 141/187 products
2025-05-20 12:40:27,069 - INFO - Processing product: Retrofit Adaptor - Type B Mortise Locks Mortise Locks (Model: 6246)
2025-05-20 12:40:27,229 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:40:29,298 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:40:29,299 - INFO - Searching Google for Pentabolt Aries 2C DB Vegas Gold Mortise Locks Mortise Locks (Model: 6364)
2025-05-20 12:40:29,584 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:40:30,887 - INFO - Completed processing Retrofit Adaptor - Type A Mortise Locks Mortise Locks (Model: 6245). Found 0 images.
2025-05-20 12:40:32,943 - INFO - Completed 142/187 products
2025-05-20 12:40:32,943 - INFO - Processing product: Retrofit Adaptor - Type C Mortise Locks Mortise Locks (Model: 2647)
2025-05-20 12:40:33,543 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:40:33,544 - INFO - Searching Google for Retrofit Adaptor - Type B Mortise Locks Mortise Locks (Model: 6246)
2025-05-20 12:40:36,060 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:40:36,870 - INFO - Completed processing Pentabolt Aries 2C DB Vegas Gold Mortise Locks Mortise Locks (Model: 6364). Found 0 images.
2025-05-20 12:40:37,110 - INFO - Completed 143/187 products
2025-05-20 12:40:37,110 - INFO - Processing product: Keyless Latch with handle* Mortise Locks Mortise Locks (Model: 9155)
2025-05-20 12:40:39,373 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:40:39,601 - INFO - Completed processing Retrofit Adaptor - Type B Mortise Locks Mortise Locks (Model: 6246). Found 0 images.
2025-05-20 12:40:39,863 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:40:39,864 - INFO - Searching Google for Retrofit Adaptor - Type C Mortise Locks Mortise Locks (Model: 2647)
2025-05-20 12:40:43,125 - INFO - Processing product: ELC 01 10cm (100mm) only handle SS* Mortise Locks Mortise Locks (Model: 4168)
2025-05-20 12:40:43,133 - INFO - Completed 144/187 products
2025-05-20 12:40:43,638 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:40:43,639 - INFO - Searching Google for Keyless Latch with handle* Mortise Locks Mortise Locks (Model: 9155)
2025-05-20 12:40:45,650 - INFO - Completed processing Retrofit Adaptor - Type C Mortise Locks Mortise Locks (Model: 2647). Found 0 images.
2025-05-20 12:40:45,980 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:40:46,213 - INFO - Completed 145/187 products
2025-05-20 12:40:46,213 - INFO - Processing product: ELC 02 10cm (100mm) only handle SS* Mortise Locks Mortise Locks (Model: 4169)
2025-05-20 12:40:48,372 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:40:48,373 - INFO - Searching Google for ELC 01 10cm (100mm) only handle SS* Mortise Locks Mortise Locks (Model: 4168)
2025-05-20 12:40:48,599 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:40:51,183 - INFO - Completed processing Keyless Latch with handle* Mortise Locks Mortise Locks (Model: 9155). Found 0 images.
2025-05-20 12:40:51,913 - INFO - Completed 146/187 products
2025-05-20 12:40:51,914 - INFO - Processing product: Baby Latch with Venus Mini Handle Satin* Mortise Locks Mortise Locks (Model: 9102)
2025-05-20 12:40:52,710 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:40:52,711 - INFO - Searching Google for ELC 02 10cm (100mm) only handle SS* Mortise Locks Mortise Locks (Model: 4169)
2025-05-20 12:40:55,101 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:40:55,644 - INFO - Completed processing ELC 01 10cm (100mm) only handle SS* Mortise Locks Mortise Locks (Model: 4168). Found 0 images.
2025-05-20 12:40:57,420 - INFO - Completed 147/187 products
2025-05-20 12:40:57,420 - INFO - Processing product: 2 Lever Lock with Regal Handle Set Mortise Locks Mortise Locks (Model: 9157)
2025-05-20 12:40:58,747 - INFO - Completed processing ELC 02 10cm (100mm) only handle SS* Mortise Locks Mortise Locks (Model: 4169). Found 0 images.
2025-05-20 12:40:59,215 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:40:59,216 - INFO - Searching Google for Baby Latch with Venus Mini Handle Satin* Mortise Locks Mortise Locks (Model: 9102)
2025-05-20 12:40:59,931 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:41:02,228 - INFO - Completed 148/187 products
2025-05-20 12:41:02,228 - INFO - Processing product: Keyless Latch for ELC handles Brown Mortise Locks Mortise Locks (Model: 6119)
2025-05-20 12:41:03,947 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:41:03,947 - INFO - Searching Google for 2 Lever Lock with Regal Handle Set Mortise Locks Mortise Locks (Model: 9157)
2025-05-20 12:41:05,540 - INFO - Completed 149/187 products
2025-05-20 12:41:05,541 - INFO - Processing product: 4 Lever Hookbolt Mortise Locks Mortise Locks (Model: 9159)
2025-05-20 12:41:06,421 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:41:07,949 - INFO - Completed processing Baby Latch with Venus Mini Handle Satin* Mortise Locks Mortise Locks (Model: 9102). Found 0 images.
2025-05-20 12:41:10,346 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:41:10,347 - INFO - Searching Google for Keyless Latch for ELC handles Brown Mortise Locks Mortise Locks (Model: 6119)
2025-05-20 12:41:11,861 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:41:13,783 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:41:13,784 - INFO - Searching Google for 4 Lever Hookbolt Mortise Locks Mortise Locks (Model: 9159)
2025-05-20 12:41:14,202 - INFO - Completed 150/187 products
2025-05-20 12:41:14,203 - INFO - Processing product: 4 Lever Narrow Stile Mortise Lock Mortise Locks Mortise Locks (Model: 9158)
2025-05-20 12:41:14,736 - INFO - Completed processing 2 Lever Lock with Regal Handle Set Mortise Locks Mortise Locks (Model: 9157). Found 0 images.
2025-05-20 12:41:17,323 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:41:18,484 - INFO - Completed processing Keyless Latch for ELC handles Brown Mortise Locks Mortise Locks (Model: 6119). Found 0 images.
2025-05-20 12:41:19,745 - INFO - Completed processing 4 Lever Hookbolt Mortise Locks Mortise Locks (Model: 9159). Found 0 images.
2025-05-20 12:41:21,044 - INFO - Completed 151/187 products
2025-05-20 12:41:21,045 - INFO - Processing product: 4 Lever Combipack with Legend Chrome Handle Set Mortise Locks Mortise Locks (Model: 9161)
2025-05-20 12:41:21,089 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:41:21,089 - INFO - Searching Google for 4 Lever Narrow Stile Mortise Lock Mortise Locks Mortise Locks (Model: 9158)
2025-05-20 12:41:23,998 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:41:24,737 - INFO - Completed 152/187 products
2025-05-20 12:41:24,737 - INFO - Processing product: 6 Lever Mortise 2 way Deadlock Mortise Locks Mortise Locks (Model: 6997)
2025-05-20 12:41:25,871 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:41:25,871 - INFO - Searching Google for 4 Lever Combipack with Legend Chrome Handle Set Mortise Locks Mortise Locks (Model: 9161)
2025-05-20 12:41:26,008 - INFO - Completed 153/187 products
2025-05-20 12:41:26,008 - INFO - Processing product: SEC 01 Combi 2C Antique Brass Mortise Locks Mortise Locks (Model: 7524)
2025-05-20 12:41:27,324 - INFO - Completed processing 4 Lever Narrow Stile Mortise Lock Mortise Locks Mortise Locks (Model: 9158). Found 0 images.
2025-05-20 12:41:27,614 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:41:28,462 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:41:31,294 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:41:31,295 - INFO - Searching Google for 6 Lever Mortise 2 way Deadlock Mortise Locks Mortise Locks (Model: 6997)
2025-05-20 12:41:31,604 - INFO - Completed processing 4 Lever Combipack with Legend Chrome Handle Set Mortise Locks Mortise Locks (Model: 9161). Found 0 images.
2025-05-20 12:41:32,152 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:41:32,153 - INFO - Searching Google for SEC 01 Combi 2C Antique Brass Mortise Locks Mortise Locks (Model: 7524)
2025-05-20 12:41:35,326 - INFO - Processing product: 7cm (70mm) EXS Cylinder 1CK Satin Mortise Locks Mortise Locks (Model: 7476)
2025-05-20 12:41:35,329 - INFO - Completed 154/187 products
2025-05-20 12:41:38,484 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:41:38,781 - INFO - Completed processing 6 Lever Mortise 2 way Deadlock Mortise Locks Mortise Locks (Model: 6997). Found 0 images.
2025-05-20 12:41:39,076 - INFO - Completed 155/187 products
2025-05-20 12:41:39,077 - INFO - Processing product: SEC 01 Combi 2C Satin & Gold Mortise Locks Mortise Locks (Model: 7525)
2025-05-20 12:41:39,398 - INFO - Completed processing SEC 01 Combi 2C Antique Brass Mortise Locks Mortise Locks (Model: 7524). Found 0 images.
2025-05-20 12:41:40,300 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:41:40,301 - INFO - Searching Google for 7cm (70mm) EXS Cylinder 1CK Satin Mortise Locks Mortise Locks (Model: 7476)
2025-05-20 12:41:41,642 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:41:44,979 - INFO - Processing product: 7 Lever Mortise 2 way Deadlock Mortise Locks Mortise Locks (Model: 8815)
2025-05-20 12:41:44,982 - INFO - Completed 156/187 products
2025-05-20 12:41:45,451 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:41:45,452 - INFO - Searching Google for SEC 01 Combi 2C Satin & Gold Mortise Locks Mortise Locks (Model: 7525)
2025-05-20 12:41:46,504 - INFO - Completed processing 7cm (70mm) EXS Cylinder 1CK Satin Mortise Locks Mortise Locks (Model: 7476). Found 0 images.
2025-05-20 12:41:47,650 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:41:47,687 - INFO - Completed 157/187 products
2025-05-20 12:41:47,688 - INFO - Processing product: SEC 02 Combi 2C Antique Brass Mortise Locks Mortise Locks (Model: 7526)
2025-05-20 12:41:49,447 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:41:49,447 - INFO - Searching Google for 7 Lever Mortise 2 way Deadlock Mortise Locks Mortise Locks (Model: 8815)
2025-05-20 12:41:50,098 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:41:51,228 - INFO - Completed processing SEC 01 Combi 2C Satin & Gold Mortise Locks Mortise Locks (Model: 7525). Found 0 images.
2025-05-20 12:41:52,750 - INFO - Completed 158/187 products
2025-05-20 12:41:52,751 - INFO - Processing product: SEC 02 Combi 2C Gold Mortise Locks Mortise Locks (Model: 7527)
2025-05-20 12:41:53,948 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:41:53,949 - INFO - Searching Google for SEC 02 Combi 2C Antique Brass Mortise Locks Mortise Locks (Model: 7526)
2025-05-20 12:41:55,036 - INFO - Completed processing 7 Lever Mortise 2 way Deadlock Mortise Locks Mortise Locks (Model: 8815). Found 0 images.
2025-05-20 12:41:55,388 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:41:57,241 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:41:57,242 - INFO - Searching Google for SEC 02 Combi 2C Gold Mortise Locks Mortise Locks (Model: 7527)
2025-05-20 12:41:57,488 - INFO - Completed 159/187 products
2025-05-20 12:41:57,489 - INFO - Processing product: Aura Combi with 2C Brass Mortise Locks Mortise Locks (Model: 7851)
2025-05-20 12:41:59,782 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:42:01,234 - INFO - Completed 160/187 products
2025-05-20 12:42:01,235 - INFO - Processing product: SEC 03 Combi 2C Antique Brass Mortise Locks Mortise Locks (Model: 7528)
2025-05-20 12:42:01,302 - INFO - Completed processing SEC 02 Combi 2C Antique Brass Mortise Locks Mortise Locks (Model: 7526). Found 0 images.
2025-05-20 12:42:03,494 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:42:03,556 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:42:03,557 - INFO - Searching Google for Aura Combi with 2C Brass Mortise Locks Mortise Locks (Model: 7851)
2025-05-20 12:42:04,586 - INFO - Completed processing SEC 02 Combi 2C Gold Mortise Locks Mortise Locks (Model: 7527). Found 0 images.
2025-05-20 12:42:07,353 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:42:07,354 - INFO - Searching Google for SEC 03 Combi 2C Antique Brass Mortise Locks Mortise Locks (Model: 7528)
2025-05-20 12:42:07,558 - INFO - Completed 161/187 products
2025-05-20 12:42:07,559 - INFO - Processing product: Oriental Combi with 2C Satin & Gold Mortise Locks Mortise Locks (Model: 7856)
2025-05-20 12:42:09,389 - INFO - Completed processing Aura Combi with 2C Brass Mortise Locks Mortise Locks (Model: 7851). Found 0 images.
2025-05-20 12:42:09,803 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:42:10,859 - INFO - Completed 162/187 products
2025-05-20 12:42:10,860 - INFO - Processing product: Imperial Handle Satin & Gold* Mortise Locks Mortise Locks (Model: 4564)
2025-05-20 12:42:11,743 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:42:11,743 - INFO - Searching Google for Oriental Combi with 2C Satin & Gold Mortise Locks Mortise Locks (Model: 7856)
2025-05-20 12:42:13,866 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:42:14,902 - INFO - Completed processing SEC 03 Combi 2C Antique Brass Mortise Locks Mortise Locks (Model: 7528). Found 0 images.
2025-05-20 12:42:15,685 - INFO - Processing product: Cara Handle Satin Chrome* Mortise Locks Mortise Locks (Model: 6900)
2025-05-20 12:42:15,685 - INFO - Completed 163/187 products
2025-05-20 12:42:17,552 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:42:17,553 - INFO - Searching Google for Imperial Handle Satin & Gold* Mortise Locks Mortise Locks (Model: 4564)
2025-05-20 12:42:18,182 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:42:19,078 - INFO - Completed processing Oriental Combi with 2C Satin & Gold Mortise Locks Mortise Locks (Model: 7856). Found 0 images.
2025-05-20 12:42:21,145 - INFO - Completed 164/187 products
2025-05-20 12:42:21,146 - INFO - Processing product: Aura Handle Brass* Mortise Locks Mortise Locks (Model: 1493)
2025-05-20 12:42:22,250 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:42:22,250 - INFO - Searching Google for Cara Handle Satin Chrome* Mortise Locks Mortise Locks (Model: 6900)
2025-05-20 12:42:23,314 - INFO - Completed processing Imperial Handle Satin & Gold* Mortise Locks Mortise Locks (Model: 4564). Found 0 images.
2025-05-20 12:42:23,963 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:42:25,323 - INFO - Processing product: Cara Handle Satin & Gold* Mortise Locks Mortise Locks (Model: 6898)
2025-05-20 12:42:25,326 - INFO - Completed 165/187 products
2025-05-20 12:42:25,880 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:42:25,881 - INFO - Searching Google for Aura Handle Brass* Mortise Locks Mortise Locks (Model: 1493)
2025-05-20 12:42:28,149 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:42:28,902 - INFO - Completed processing Cara Handle Satin Chrome* Mortise Locks Mortise Locks (Model: 6900). Found 0 images.
2025-05-20 12:42:29,601 - INFO - Completed 166/187 products
2025-05-20 12:42:29,602 - INFO - Processing product: Cara Handle Antique Brass* Mortise Locks Mortise Locks (Model: 6899)
2025-05-20 12:42:31,762 - INFO - Completed processing Aura Handle Brass* Mortise Locks Mortise Locks (Model: 1493). Found 0 images.
2025-05-20 12:42:31,974 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:42:32,579 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:42:32,580 - INFO - Searching Google for Cara Handle Satin & Gold* Mortise Locks Mortise Locks (Model: 6898)
2025-05-20 12:42:34,105 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:42:34,105 - INFO - Searching Google for Cara Handle Antique Brass* Mortise Locks Mortise Locks (Model: 6899)
2025-05-20 12:42:37,064 - INFO - Completed 167/187 products
2025-05-20 12:42:37,064 - INFO - Processing product: Door Handle set Lock Body 1CK Mortise Locks Mortise Locks (Model: 4492)
2025-05-20 12:42:38,029 - INFO - Completed 168/187 products
2025-05-20 12:42:38,030 - INFO - Processing product: Cara Handle Brass* Mortise Locks Mortise Locks (Model: 6897)
2025-05-20 12:42:38,173 - INFO - Completed processing Cara Handle Antique Brass* Mortise Locks Mortise Locks (Model: 6899). Found 0 images.
2025-05-20 12:42:38,769 - INFO - Completed processing Cara Handle Satin & Gold* Mortise Locks Mortise Locks (Model: 6898). Found 0 images.
2025-05-20 12:42:39,431 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:42:40,384 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:42:41,269 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:42:41,270 - INFO - Searching Google for Door Handle set Lock Body 1CK Mortise Locks Mortise Locks (Model: 4492)
2025-05-20 12:42:44,399 - INFO - Processing product: Door Handle set Lock Body 2C Antique Brass Mortise Locks Mortise Locks (Model: 2856)
2025-05-20 12:42:44,405 - INFO - Completed 169/187 products
2025-05-20 12:42:44,555 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:42:44,556 - INFO - Searching Google for Cara Handle Brass* Mortise Locks Mortise Locks (Model: 6897)
2025-05-20 12:42:45,026 - INFO - Completed 170/187 products
2025-05-20 12:42:45,026 - INFO - Processing product: Door Handle set Lock Body 1CK Antique Brass Mortise Locks Mortise Locks (Model: 2852)
2025-05-20 12:42:46,820 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:42:47,421 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:42:47,746 - INFO - Completed processing Door Handle set Lock Body 1CK Mortise Locks Mortise Locks (Model: 4492). Found 0 images.
2025-05-20 12:42:50,594 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:42:50,594 - INFO - Searching Google for Door Handle set Lock Body 2C Antique Brass Mortise Locks Mortise Locks (Model: 2856)
2025-05-20 12:42:50,770 - INFO - Completed processing Cara Handle Brass* Mortise Locks Mortise Locks (Model: 6897). Found 0 images.
2025-05-20 12:42:51,342 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:42:51,342 - INFO - Searching Google for Door Handle set Lock Body 1CK Antique Brass Mortise Locks Mortise Locks (Model: 2852)
2025-05-20 12:42:53,938 - INFO - Completed 171/187 products
2025-05-20 12:42:53,939 - INFO - Processing product: Door Handle set Lock Body 2C Mortise Locks Mortise Locks (Model: 4381)
2025-05-20 12:42:56,405 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:42:58,235 - INFO - Completed processing Door Handle set Lock Body 2C Antique Brass Mortise Locks Mortise Locks (Model: 2856). Found 0 images.
2025-05-20 12:42:58,774 - INFO - Completed processing Door Handle set Lock Body 1CK Antique Brass Mortise Locks Mortise Locks (Model: 2852). Found 0 images.
2025-05-20 12:42:59,052 - INFO - Completed 172/187 products
2025-05-20 12:42:59,053 - INFO - Processing product: Door Handle set Lock Body 1CK Mortise Locks Mortise Locks (Model: 4493)
2025-05-20 12:43:00,266 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:43:00,267 - INFO - Searching Google for Door Handle set Lock Body 2C Mortise Locks Mortise Locks (Model: 4381)
2025-05-20 12:43:01,420 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:43:03,342 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:43:03,344 - INFO - Searching Google for Door Handle set Lock Body 1CK Mortise Locks Mortise Locks (Model: 4493)
2025-05-20 12:43:04,493 - INFO - Processing product: Door Handle set Lock Body 2C Antique Brass Mortise Locks Mortise Locks (Model: 2857)
2025-05-20 12:43:04,493 - INFO - Completed 173/187 products
2025-05-20 12:43:05,002 - INFO - Completed 174/187 products
2025-05-20 12:43:05,002 - INFO - Processing product: Door Handle set Lock Body 1CK Antique Brass Mortise Locks Mortise Locks (Model: 2853)
2025-05-20 12:43:06,362 - INFO - Completed processing Door Handle set Lock Body 2C Mortise Locks Mortise Locks (Model: 4381). Found 0 images.
2025-05-20 12:43:06,792 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:43:07,411 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:43:10,662 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:43:10,663 - INFO - Searching Google for Door Handle set Lock Body 2C Antique Brass Mortise Locks Mortise Locks (Model: 2857)
2025-05-20 12:43:10,669 - INFO - Completed processing Door Handle set Lock Body 1CK Mortise Locks Mortise Locks (Model: 4493). Found 0 images.
2025-05-20 12:43:11,668 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:43:11,669 - INFO - Searching Google for Door Handle set Lock Body 1CK Antique Brass Mortise Locks Mortise Locks (Model: 2853)
2025-05-20 12:43:12,626 - INFO - Completed 175/187 products
2025-05-20 12:43:12,627 - INFO - Processing product: Door Handle set Lock Body 2C Mortise Locks Mortise Locks (Model: 4382)
2025-05-20 12:43:15,014 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:43:16,849 - INFO - Completed processing Door Handle set Lock Body 2C Antique Brass Mortise Locks Mortise Locks (Model: 2857). Found 0 images.
2025-05-20 12:43:16,922 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:43:16,923 - INFO - Searching Google for Door Handle set Lock Body 2C Mortise Locks Mortise Locks (Model: 4382)
2025-05-20 12:43:18,264 - INFO - Completed 176/187 products
2025-05-20 12:43:18,265 - INFO - Processing product: Door Handle set Lock Body 1CK Mortise Locks Mortise Locks (Model: 4494)
2025-05-20 12:43:19,096 - INFO - Completed processing Door Handle set Lock Body 1CK Antique Brass Mortise Locks Mortise Locks (Model: 2853). Found 0 images.
2025-05-20 12:43:21,539 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:43:23,273 - INFO - Completed 177/187 products
2025-05-20 12:43:23,274 - INFO - Processing product: Door Handle set Lock Body 1CK Antique Brass Mortise Locks Mortise Locks (Model: 2854)
2025-05-20 12:43:23,351 - INFO - Completed processing Door Handle set Lock Body 2C Mortise Locks Mortise Locks (Model: 4382). Found 0 images.
2025-05-20 12:43:25,221 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:43:25,221 - INFO - Searching Google for Door Handle set Lock Body 1CK Mortise Locks Mortise Locks (Model: 4494)
2025-05-20 12:43:25,469 - INFO - Completed 178/187 products
2025-05-20 12:43:25,469 - INFO - Processing product: Door Handle set Lock Body 2C Mortise Locks Mortise Locks (Model: 4383)
2025-05-20 12:43:26,215 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:43:27,684 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:43:27,940 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:43:27,941 - INFO - Searching Google for Door Handle set Lock Body 1CK Antique Brass Mortise Locks Mortise Locks (Model: 2854)
2025-05-20 12:43:29,750 - INFO - Completed 179/187 products
2025-05-20 12:43:29,750 - INFO - Processing product: Door Handle set Lock Body 1CK Mortise Locks Mortise Locks (Model: 4495)
2025-05-20 12:43:31,701 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:43:31,701 - INFO - Searching Google for Door Handle set Lock Body 2C Mortise Locks Mortise Locks (Model: 4383)
2025-05-20 12:43:32,079 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:43:32,685 - INFO - Completed processing Door Handle set Lock Body 1CK Mortise Locks Mortise Locks (Model: 4494). Found 0 images.
2025-05-20 12:43:33,258 - INFO - Completed processing Door Handle set Lock Body 1CK Antique Brass Mortise Locks Mortise Locks (Model: 2854). Found 0 images.
2025-05-20 12:43:35,988 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:43:35,989 - INFO - Searching Google for Door Handle set Lock Body 1CK Mortise Locks Mortise Locks (Model: 4495)
2025-05-20 12:43:37,683 - INFO - Completed processing Door Handle set Lock Body 2C Mortise Locks Mortise Locks (Model: 4383). Found 0 images.
2025-05-20 12:43:38,958 - INFO - Completed 180/187 products
2025-05-20 12:43:38,958 - INFO - Processing product: Door Handle set Lock Body 2C Antique Brass Mortise Locks Mortise Locks (Model: 2858)
2025-05-20 12:43:39,552 - INFO - Completed 181/187 products
2025-05-20 12:43:39,553 - INFO - Processing product: Door Handle set Lock Body 2C Mortise Locks Mortise Locks (Model: 4384)
2025-05-20 12:43:41,400 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:43:41,729 - INFO - Completed processing Door Handle set Lock Body 1CK Mortise Locks Mortise Locks (Model: 4495). Found 0 images.
2025-05-20 12:43:42,122 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:43:43,148 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:43:43,148 - INFO - Searching Google for Door Handle set Lock Body 2C Antique Brass Mortise Locks Mortise Locks (Model: 2858)
2025-05-20 12:43:43,969 - INFO - Completed 182/187 products
2025-05-20 12:43:43,969 - INFO - Processing product: Door Handle set Lock Body 1CK Antique Brass Mortise Locks Mortise Locks (Model: 2855)
2025-05-20 12:43:45,961 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:43:45,962 - INFO - Searching Google for Door Handle set Lock Body 2C Mortise Locks Mortise Locks (Model: 4384)
2025-05-20 12:43:46,423 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:43:48,028 - INFO - Completed 183/187 products
2025-05-20 12:43:48,030 - INFO - Processing product: Door Handle set Lock Body 2C Antique Brass Mortise Locks Mortise Locks (Model: 2859)
2025-05-20 12:43:49,331 - INFO - Completed processing Door Handle set Lock Body 2C Antique Brass Mortise Locks Mortise Locks (Model: 2858). Found 0 images.
2025-05-20 12:43:50,371 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:43:50,371 - INFO - Searching Google for Door Handle set Lock Body 1CK Antique Brass Mortise Locks Mortise Locks (Model: 2855)
2025-05-20 12:43:50,596 - ERROR - Failed to initialize Chrome WebDriver: Message: Unable to obtain chromedriver using Selenium Manager; Message: Unsuccessful command executed: D:\Triumph\data new qubo jsons\env\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --output json.
The chromedriver version cannot be discovered
; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

2025-05-20 12:43:51,880 - INFO - Completed processing Door Handle set Lock Body 2C Mortise Locks Mortise Locks (Model: 4384). Found 0 images.
2025-05-20 12:43:52,481 - INFO - Successfully initialized Edge WebDriver as fallback
2025-05-20 12:43:52,481 - INFO - Searching Google for Door Handle set Lock Body 2C Antique Brass Mortise Locks Mortise Locks (Model: 2859)
2025-05-20 12:43:55,603 - INFO - Completed 184/187 products
2025-05-20 12:43:57,613 - INFO - Completed processing Door Handle set Lock Body 1CK Antique Brass Mortise Locks Mortise Locks (Model: 2855). Found 0 images.
2025-05-20 12:43:58,331 - INFO - Completed processing Door Handle set Lock Body 2C Antique Brass Mortise Locks Mortise Locks (Model: 2859). Found 0 images.
2025-05-20 12:43:58,437 - INFO - Completed 185/187 products
2025-05-20 12:44:04,638 - INFO - Completed 186/187 products
2025-05-20 12:44:05,543 - INFO - Completed 187/187 products
2025-05-20 12:44:05,551 - INFO - Updated output/json/product_data.json with image paths
2025-05-20 12:44:05,554 - INFO - Image scraping process completed
