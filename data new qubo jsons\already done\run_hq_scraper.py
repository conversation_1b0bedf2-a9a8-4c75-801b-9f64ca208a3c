#!/usr/bin/env python3
"""
Haier High-Quality Image Scraper Runner
This script provides options to run either Selenium or Playwright-based scrapers
"""

import sys
import os
import subprocess

def check_requirements():
    """Check if required packages are installed"""
    required_packages = ['selenium', 'requests', 'Pillow']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.lower())
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"Missing packages: {', '.join(missing_packages)}")
        install = input("Install missing packages? (y/n): ").lower().strip()
        if install == 'y':
            for package in missing_packages:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        else:
            print("Please install required packages before running the scraper.")
            return False
    
    return True

def run_selenium_scraper():
    """Run the Selenium-based scraper"""
    print("Running Selenium-based scraper...")
    try:
        from haier_hq_image_scraper import scrape_haier_hq_images
        scrape_haier_hq_images()
    except ImportError as e:
        print(f"Error importing Selenium scraper: {e}")
        print("Make sure haier_hq_image_scraper.py is in the current directory")
    except Exception as e:
        print(f"Error running Selenium scraper: {e}")

def run_playwright_scraper():
    """Run the Playwright-based scraper"""
    print("Running Playwright-based scraper...")
    try:
        import asyncio
        from haier_hq_image_scraper_playwright import scrape_haier_hq_images_playwright
        asyncio.run(scrape_haier_hq_images_playwright())
    except ImportError as e:
        print(f"Error importing Playwright scraper: {e}")
        print("Make sure haier_hq_image_scraper_playwright.py is in the current directory")
        print("Also ensure Playwright is installed: pip install playwright && playwright install")
    except Exception as e:
        print(f"Error running Playwright scraper: {e}")

def main():
    """Main function to choose and run scraper"""
    print("Haier High-Quality Image Scraper")
    print("=" * 40)
    
    # Check requirements
    if not check_requirements():
        return
    
    print("\nChoose scraper type:")
    print("1. Selenium (Chrome WebDriver)")
    print("2. Playwright (Chromium)")
    print("3. Exit")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == '1':
        run_selenium_scraper()
    elif choice == '2':
        run_playwright_scraper()
    elif choice == '3':
        print("Exiting...")
        return
    else:
        print("Invalid choice. Please run the script again.")

if __name__ == "__main__":
    main()
