# MinIO Integration for Frontend

This document explains how the frontend has been updated to work with Min<PERSON> for media storage.

## Overview

The frontend has been updated to use MinIO for media storage. This includes:

1. A utility function to handle image URLs
2. Updates to image components to use the utility function
3. Configuration for MinIO in the Next.js config

## How It Works

### Image URL Utility

The `imageUtils.ts` file contains utility functions for handling image URLs:

- `getImageUrl()`: Converts relative image paths to full URLs, using MinIO when appropriate
- `getProductImageUrl()`: Specialized function for product images

These functions handle different types of image sources:
- Full URLs (starting with http/https)
- MinIO URLs (media paths)
- Local assets
- Relative paths

### Environment Variables

The frontend uses the following environment variables for MinIO:

- `NEXT_PUBLIC_MINIO_URL`: The URL of the MinIO server, including the bucket name

Example:
```
NEXT_PUBLIC_MINIO_URL=https://minio-triumph.trio.net.in/media
```

## Setup Instructions

1. **Copy the environment variables template**:

   ```bash
   cp .env.local.example .env.local
   ```

2. **Update the environment variables**:

   Edit `.env.local` and set the correct values for your environment:

   ```
   NEXT_PUBLIC_MINIO_URL=https://minio-triumph.trio.net.in/media
   ```

3. **Restart the development server**:

   ```bash
   npm run dev
   ```

## Verifying the Integration

To verify that the MinIO integration is working correctly:

1. Open the website in your browser
2. Navigate to a product page
3. Check the network tab in your browser's developer tools
4. Verify that image requests are being made to the MinIO server

## Troubleshooting

### Images Not Loading

If images are not loading correctly, check the following:

1. **Environment Variables**: Make sure `NEXT_PUBLIC_MINIO_URL` is set correctly
2. **CORS Configuration**: Ensure that MinIO is configured to allow requests from your frontend domain
3. **Network Requests**: Check the network tab in your browser's developer tools to see if requests to MinIO are failing

### CORS Errors

If you see CORS errors in the console, you need to configure MinIO to allow requests from your frontend domain:

1. Open the MinIO Console
2. Go to Settings > CORS
3. Add a new rule:
   - Allow origin: `*` (or your specific domain)
   - Allow methods: `GET`
   - Allow headers: `*`

## Additional Resources

- [MinIO Documentation](https://min.io/docs/minio/container/index.html)
- [Next.js Image Component](https://nextjs.org/docs/api-reference/next/image)
- [CORS Configuration](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS)
