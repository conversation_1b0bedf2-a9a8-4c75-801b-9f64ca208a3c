"""
MinIO configuration for Django
This module provides settings and utilities for integrating MinIO for media storage
"""

import os
from pathlib import Path

# Toggle to enable/disable MinIO storage
USE_MINIO = os.getenv('USE_MINIO', 'True').lower() == 'true'

if USE_MINIO:
    # MinIO server information
    MINIO_STORAGE_ENDPOINT = os.getenv('MINIO_STORAGE_ENDPOINT', 'minio:9000')
    MINIO_STORAGE_ACCESS_KEY = os.getenv('MINIO_STORAGE_ACCESS_KEY', 'minioadmin')
    MINIO_STORAGE_SECRET_KEY = os.getenv('MINIO_STORAGE_SECRET_KEY', 'minioadmin')
    MINIO_STORAGE_USE_HTTPS = os.getenv('MINIO_STORAGE_USE_HTTPS', 'False').lower() == 'true'

    # Bucket configuration
    MINIO_STORAGE_MEDIA_BUCKET_NAME = os.getenv('MINIO_STORAGE_MEDIA_BUCKET_NAME', 'media')
    MINIO_STORAGE_AUTO_CREATE_MEDIA_BUCKET = os.getenv('MINIO_STORAGE_AUTO_CREATE_MEDIA_BUCKET', 'True').lower() == 'true'
    MINIO_STORAGE_AUTO_CREATE_MEDIA_POLICY = 'READ_WRITE'

    # URL configuration
    MINIO_STORAGE_MEDIA_URL = os.getenv('MINIO_STORAGE_MEDIA_URL', None)

    # Default metadata for objects
    MINIO_STORAGE_MEDIA_OBJECT_METADATA = {
        "Cache-Control": "max-age=86400"  # Cache for 24 hours
    }

    # Use MinIO for media storage
    DEFAULT_FILE_STORAGE = "minio_storage.storage.MinioMediaStorage"

    # Override Django's media URL if MinIO media URL is set
    if MINIO_STORAGE_MEDIA_URL:
        MEDIA_URL = MINIO_STORAGE_MEDIA_URL + '/'

# Function to get a MinIO URL for a media file
def get_minio_media_url(path):
    """
    Get the MinIO URL for a media file

    Args:
        path: The path to the file relative to the media bucket

    Returns:
        The full URL to the file
    """
    # Convert backslashes to forward slashes for consistent URL handling
    if '\\' in path:
        path = path.replace('\\', '/')

    # If MinIO is not enabled, return the standard media URL
    if not USE_MINIO:
        from django.conf import settings
        # Ensure path doesn't start with a slash for concatenation
        if path.startswith('/'):
            path = path[1:]
        return f'{settings.MEDIA_URL}{path}'

    # Ensure path doesn't start with a slash
    if path.startswith('/'):
        path = path[1:]

    # Remove duplicate 'media/' prefix if present
    if path.startswith('media/'):
        path = path[6:]  # Remove the 'media/' prefix

    # Also handle the case where there's a double 'media/media/' prefix
    if path.startswith('media/media/'):
        path = path[12:]  # Remove the 'media/media/' prefix

    if MINIO_STORAGE_MEDIA_URL:
        return f'{MINIO_STORAGE_MEDIA_URL}/{path}'
    else:
        protocol = 'https' if MINIO_STORAGE_USE_HTTPS else 'http'
        return f'{protocol}://{MINIO_STORAGE_ENDPOINT}/{MINIO_STORAGE_MEDIA_BUCKET_NAME}/{path}'
