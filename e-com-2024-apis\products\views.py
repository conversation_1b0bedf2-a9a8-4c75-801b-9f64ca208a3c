# from rest_framework import viewsets, permissions, status
# from rest_framework.decorators import action
# from rest_framework.response import Response
# from .models import Category, Product, ProductImage, ProductVariant, Review
# from .serializers import (
#     CategorySerializer, CategoryDetailSerializer,
#     ProductSerializer, ProductDetailSerializer,
#     ProductImageSerializer, ProductVariantSerializer,
#     ReviewSerializer, ReviewCreateSerializer
# )

# class CategoryViewSet(viewsets.ModelViewSet):
#     queryset = Category.objects.all()
#     serializer_class = CategorySerializer
#     permission_classes = [permissions.IsAuthenticatedOrReadOnly]
#     lookup_field = 'slug'

#     def get_serializer_class(self):
#         if self.action == 'retrieve':
#             return CategoryDetailSerializer
#         return CategorySerializer

#     @action(detail=False, methods=['get'])
#     def root_categories(self, request):
#         root_categories = Category.objects.filter(parent=None)
#         serializer = CategoryDetailSerializer(root_categories, many=True)
#         return Response(serializer.data)

# class ProductViewSet(viewsets.ModelViewSet):
#     queryset = Product.objects.filter(is_active=True)
#     serializer_class = ProductSerializer
#     permission_classes = [permissions.IsAuthenticatedOrReadOnly]
#     filterset_fields = ['category', 'price', 'stock']
#     search_fields = ['name', 'description']
#     ordering_fields = ['created_at', 'price', 'name']
#     lookup_field = 'slug'

#     def get_queryset(self):
#         queryset = super().get_queryset()
#         if self.request.user.is_staff:
#             return Product.objects.all()
#         return queryset

#     def get_serializer_class(self):
#         if self.action == 'retrieve':
#             return ProductDetailSerializer
#         return ProductSerializer

#     @action(detail=True, methods=['post'], permission_classes=[permissions.IsAdminUser])
#     def add_variant(self, request, slug=None):
#         product = self.get_object()
#         serializer = ProductVariantSerializer(data=request.data)

#         if serializer.is_valid():
#             serializer.save(product=product)
#             return Response(serializer.data, status=status.HTTP_201_CREATED)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

#     @action(detail=True, methods=['post'], permission_classes=[permissions.IsAdminUser])
#     def add_image(self, request, slug=None):
#         product = self.get_object()
#         serializer = ProductImageSerializer(data=request.data)

#         if serializer.is_valid():
#             serializer.save(product=product)
#             return Response(serializer.data, status=status.HTTP_201_CREATED)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

#     @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
#     def add_review(self, request, slug=None):
#         product = self.get_object()
#         serializer = ReviewCreateSerializer(
#             data={'product': product.id, **request.data},
#             context={'request': request}
#         )

#         if serializer.is_valid():
#             serializer.save()
#             return Response(serializer.data, status=status.HTTP_201_CREATED)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

#     @action(detail=False, methods=['get'])
#     def featured(self, request):
#         # You can customize this to return featured products based on your criteria
#         featured_products = self.get_queryset().order_by('-created_at')[:6]
#         serializer = self.get_serializer(featured_products, many=True)
#         return Response(serializer.data)

# class ProductVariantViewSet(viewsets.ModelViewSet):
#     serializer_class = ProductVariantSerializer
#     permission_classes = [permissions.IsAdminUser]

#     def get_queryset(self):
#         return ProductVariant.objects.filter(product__slug=self.kwargs['product_slug'])

# class ProductImageViewSet(viewsets.ModelViewSet):
#     serializer_class = ProductImageSerializer
#     permission_classes = [permissions.IsAdminUser]

#     def get_queryset(self):
#         return ProductImage.objects.filter(product__slug=self.kwargs['product_slug'])

# class ReviewViewSet(viewsets.ModelViewSet):
#     serializer_class = ReviewSerializer
#     permission_classes = [permissions.IsAuthenticatedOrReadOnly]

#     def get_queryset(self):
#         return Review.objects.filter(product__slug=self.kwargs['product_slug'])

#     def get_permissions(self):
#         if self.action in ['update', 'partial_update', 'destroy']:
#             return [permissions.IsAuthenticated()]
#         return super().get_permissions()

#     def perform_create(self, serializer):
#         product = Product.objects.get(slug=self.kwargs['product_slug'])
#         serializer.save(user=self.request.user, product=product)

from rest_framework import generics, permissions, status, views
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from .models import Category, Product, ProductImage, ProductVariant, Review, Brand, SubCategorie
from .serializers import (
    CategoryNameOnlySerializer,
    CategoryDetailSerializer,
    ProductSerializer,
    ProductListSerializer,
    ProductDetailSerializer,
    ProductImageSerializer,
    ProductVariantSerializer,
    ReviewSerializer,
    ReviewCreateSerializer,
    BrandNameOnlySerializer,
    BrandSerializer,
    AdminCategorySerializer,
    CategorySerializer
)
from products.pagination import ProductPagination,CategoryProductsPagination
from django_filters import rest_framework as filters
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page


class ProductFilter(filters.FilterSet):
    category = filters.CharFilter(
        method="filter_category"
    )  # Use a CharFilter to handle the custom list format
    brand = filters.CharFilter(method="filter_brand")
    price = filters.CharFilter(method="filter_price")

    class Meta:
        model = Product
        fields = [
            "category",
            "brand",
            "price",
            "stock",
        ]  # Other fields you want to filter

    def filter_category(self, queryset, name, value):
        """Custom filter to handle list input for category."""
        if value:
            # Convert the string representation of the list into actual list of integers
            try:
                category_ids = eval(
                    value
                )  # Use eval carefully! Consider using json.loads for safety.
                queryset = queryset.filter(category__id__in=category_ids)
            except (SyntaxError, NameError):
                pass  # Handle the case where the input is invalid
        return queryset

    def filter_brand(self, queryset, name, value):
        """Custom filter to handle list input for category."""
        if value:
            # Convert the string representation of the list into actual list of integers
            try:
                brand_ids = eval(
                    value
                )  # Use eval carefully! Consider using json.loads for safety.
                queryset = queryset.filter(brand__id__in=brand_ids)
            except (SyntaxError, NameError):
                pass  # Handle the case where the input is invalid
        return queryset

    def filter_price(self, queryset, name, value):
        """Custom filter to handle price range input."""
        if value:
            # Expecting value in the format 'min_price-max_price'
            try:
                min_price, max_price = map(float, value.split("-"))  # Convert to float
                queryset = queryset.filter(price__gte=min_price, price__lte=max_price)
            except ValueError:
                # Handle the case where the input is invalid (non-numeric)
                pass  # You could also raise an exception or log an error if needed
        return queryset



# Category Views
class CategoryListView(generics.ListAPIView):
    serializer_class = CategoryNameOnlySerializer
    permission_classes = [permissions.AllowAny]

    def get_queryset(self):
        return Category.objects.prefetch_related('subcategories').all()

    @method_decorator(cache_page(60 * 15))
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class BrandListView(generics.ListAPIView):
    serializer_class = BrandSerializer
    permission_classes = [permissions.AllowAny]

    def get_queryset(self):
        return Brand.objects.filter(is_active=True)


class CategoryDetailView(generics.RetrieveAPIView):
    serializer_class = CategoryDetailSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    lookup_field = "slug"

    def get_queryset(self):
        return Category.objects.all()


@api_view(["GET"])
def root_categories(request):
    try:
        root_categories = Category.objects.filter(parent=None)
        serializer = CategoryDetailSerializer(root_categories, many=True, context={'request': request})
        return Response(serializer.data)
    except:
        return Response({"error": "Could not retrieve root categories"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Product Views
class ProductListView(generics.ListAPIView):
    serializer_class = ProductListSerializer
    permission_classes = [permissions.AllowAny]
    # filterset_fields = ['category', 'price', 'stock']
    filterset_class = ProductFilter
    search_fields = ["name"]
    ordering_fields = ["created_at", "price", "name"]
    pagination_class = ProductPagination

    def get_queryset(self):
        if self.request.user.is_staff:
            queryset = Product.objects.all()
        else:
            queryset = Product.objects.filter(is_active=True)

        # Optimize query with select_related and prefetch_related
        return queryset.select_related('category', 'brand').prefetch_related('images')

    def get_serializer(self, *args, **kwargs):
        is_admin = self.request.query_params.get("is_admin", False)
        if not is_admin:
            kwargs["fields"] = [
                "id",
                "name",
                "price",
                "mrp",
                "base_price",
                "gst_amount",
                "average_rating",
                "images",
                'image',
                "category",
                "slug",
                "brand",
            ]
        return super().get_serializer(*args, **kwargs)


class ProductDetailView(generics.RetrieveAPIView):
    serializer_class = ProductDetailSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    lookup_field = "slug"

    def get_queryset(self):
        if self.request.user.is_staff:
            queryset = Product.objects.all()
        else:
            queryset = Product.objects.filter(is_active=True)

        # Optimize query with select_related and prefetch_related
        return queryset.select_related(
            'category',
            'brand',
            'subcategory'
        ).prefetch_related(
            'images',
            'variants',
            'reviews__user'
        )


@api_view(["POST"])
def add_product_variant(request, slug):
    product = generics.get_object_or_404(Product, slug=slug)
    if not request.user.is_staff:
        return Response(status=status.HTTP_403_FORBIDDEN)
    serializer = ProductVariantSerializer(data=request.data)
    if serializer.is_valid():
        serializer.save(product=product)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(["POST"])
def add_product_image(request, slug):
    product = generics.get_object_or_404(Product, slug=slug)
    if not request.user.is_staff:
        return Response(status=status.HTTP_403_FORBIDDEN)
    serializer = ProductImageSerializer(data=request.data)
    if serializer.is_valid():
        serializer.save(product=product)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(["POST"])
def add_review(request, slug):
    product = generics.get_object_or_404(Product, slug=slug)
    serializer = ReviewCreateSerializer(
        data={"product": product.id, **request.data}, context={"request": request}
    )
    if serializer.is_valid():
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(["GET"])
@permission_classes([permissions.AllowAny])
def featured_products(request):
    from .utils import get_featured_products

    category = request.query_params.get("category", None)
    random = request.query_params.get("random", "false").lower() == "true"
    limit = int(request.query_params.get("limit", 8 if random else 5))

    # If no specific filters, use cached featured products
    if not category and not random:
        featured_products = get_featured_products(limit)
        serializer = ProductListSerializer(featured_products, many=True, context={'request': request})
        return Response(serializer.data)

    # Otherwise, apply filters
    queryset = Product.objects.filter(is_active=True).select_related(
        'category', 'brand'
    ).prefetch_related('images')

    if category:
        queryset = queryset.filter(category__slug=category)

    # If random parameter is true, order randomly
    if random:
        # Use Django's random ordering
        featured_products = queryset.order_by("?")[:limit]
    else:
        # Otherwise use the default ordering by creation date
        featured_products = queryset.order_by("-created_at")[:limit]

    serializer = ProductListSerializer(featured_products, many=True, context={'request': request})
    return Response(serializer.data)


# Product Variant Views
class ProductVariantListView(generics.ListCreateAPIView):
    serializer_class = ProductVariantSerializer
    permission_classes = [permissions.IsAdminUser]

    def get_queryset(self):
        return ProductVariant.objects.filter(product__slug=self.kwargs["product_slug"])


# Product Image Views
class ProductImageListView(generics.ListCreateAPIView):
    serializer_class = ProductImageSerializer
    permission_classes = [permissions.IsAdminUser]

    def get_queryset(self):
        return ProductImage.objects.filter(product__slug=self.kwargs["product_slug"])


# Review Views
class ReviewListView(generics.ListCreateAPIView):
    serializer_class = ReviewSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]

    def get_queryset(self):
        return Review.objects.filter(product__slug=self.kwargs["product_slug"])

    def perform_create(self, serializer):
        product = Product.objects.get(slug=self.kwargs["product_slug"])
        serializer.save(user=self.request.user, product=product)


class ReviewDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = ReviewSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]

    def get_permissions(self):
        if self.request.method in ["PUT", "PATCH", "DELETE"]:
            return [permissions.IsAuthenticated()]
        return super().get_permissions()

    def get_queryset(self):
        return Review.objects.filter(product__slug=self.kwargs["product_slug"])

class AdminCategoryListView(views.APIView):
    permission_classes = [permissions.IsAdminUser]
    def get(self, request, *args, **kwargs):
        try:
            categories = Category.objects.all().order_by('created_at')  # Order categories as needed
            serializer = AdminCategorySerializer(categories, many=True, context={'request': request})
            return Response(serializer.data)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class CategoryCreateView(generics.CreateAPIView):
    serializer_class = CategorySerializer

    def get_queryset(self):
        return Category.objects.all()

class CategoryUpdateView(generics.RetrieveUpdateAPIView):
    serializer_class = CategorySerializer

    def get_queryset(self):
        return Category.objects.all()




class CategoryProductsAPIView(views.APIView, CategoryProductsPagination):
    permission_classes = [permissions.AllowAny]

    def get(self, request, slug, *args, **kwargs):
        try:
            # First try to find as subcategory
            try:
                subcategory = SubCategorie.objects.get(slug=slug, is_active=True)
                products = Product.objects.filter(
                    subcategory=subcategory,
                    is_active=True
                )

                # Paginate and serialize the products
                paginated_products = self.paginate_queryset(products, request, view=self)
                serialized_products = ProductListSerializer(paginated_products, many=True, context={'request': request}).data

                response_data = {
                    "category": {
                        "id": subcategory.category.id,
                        "name": subcategory.category.name,
                        "slug": subcategory.category.slug,
                    },
                    "subcategory": {
                        "id": subcategory.id,
                        "name": subcategory.name,
                        "slug": subcategory.slug
                    },
                    "products": serialized_products
                }
                return self.get_paginated_response(response_data)

            except SubCategorie.DoesNotExist:
                # If not found as subcategory, try as category
                category = Category.objects.get(slug=slug, is_active=True)
                subcategory_slug = request.query_params.get('subcategory')

                products = Product.objects.filter(category=category, is_active=True)

                if subcategory_slug:
                    subcategory = SubCategorie.objects.get(
                        slug=subcategory_slug,
                        category=category,
                        is_active=True
                    )
                    products = products.filter(subcategory=subcategory)

                # Paginate and serialize the products
                paginated_products = self.paginate_queryset(products, request, view=self)
                serialized_products = ProductListSerializer(paginated_products, many=True, context={'request': request}).data

                response_data = {
                    "category": {
                        "id": category.id,
                        "name": category.name,
                        "slug": category.slug,
                    },
                    "products": serialized_products
                }

                return self.get_paginated_response(response_data)

        except (Category.DoesNotExist, SubCategorie.DoesNotExist) as e:
            return Response(
                {"error": f"Category not found: {str(e)}"},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )