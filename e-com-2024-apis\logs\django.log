[2025-05-14 12:37:52,370] - INFO - django.utils.autoreload - Watching for file changes with StatReloader
[2025-05-14 12:38:26,318] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:38:26,319] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:38:26,410] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:38:26,493] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 8533
[2025-05-14 12:38:26,665] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62404)
[2025-05-14 12:38:26,860] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=1 HTTP/1.1" 200 1056
[2025-05-14 12:38:27,143] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=1 HTTP/1.1" 200 953
[2025-05-14 12:38:27,528] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=1 HTTP/1.1" 200 1036
[2025-05-14 12:38:28,704] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62395)
[2025-05-14 12:38:28,863] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62396)
[2025-05-14 12:38:28,924] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62397)
[2025-05-14 12:38:29,074] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62405)
[2025-05-14 12:38:29,268] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62417)
[2025-05-14 12:38:29,644] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62423)
[2025-05-14 12:38:32,045] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:38:32,046] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:38:32,148] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:38:32,219] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 10201
[2025-05-14 12:38:32,354] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62489)
[2025-05-14 12:38:32,704] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=1 HTTP/1.1" 200 1056
[2025-05-14 12:38:33,011] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=1 HTTP/1.1" 200 953
[2025-05-14 12:38:33,435] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=1 HTTP/1.1" 200 1036
[2025-05-14 12:38:34,229] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:38:34,388] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:38:34,471] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:38:34,803] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:38:35,114] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:38:35,544] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:38:36,329] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:38:36,502] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:38:36,687] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:38:37,020] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:38:37,219] - INFO - django.server - "GET /api/v1/products/categories/screws/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:38:37,663] - INFO - django.server - "GET /api/v1/products/categories/hinges/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:38:38,453] - INFO - django.server - "GET /api/v1/products/categories/tower-bolt/products/?page_size=1 HTTP/1.1" 200 185
[2025-05-14 12:38:38,617] - INFO - django.server - "GET /api/v1/products/categories/door-kits/products/?page_size=1 HTTP/1.1" 200 183
[2025-05-14 12:38:38,822] - INFO - django.server - "GET /api/v1/products/categories/handles/products/?page_size=1 HTTP/1.1" 200 179
[2025-05-14 12:38:39,131] - INFO - django.server - "GET /api/v1/products/categories/t-channel/products/?page_size=1 HTTP/1.1" 200 203
[2025-05-14 12:38:39,326] - INFO - django.server - "GET /api/v1/products/categories/tendom/products/?page_size=1 HTTP/1.1" 200 198
[2025-05-14 12:38:39,760] - INFO - django.server - "GET /api/v1/products/categories/tendom-box/products/?page_size=1 HTTP/1.1" 200 208
[2025-05-14 12:38:40,560] - INFO - django.server - "GET /api/v1/products/categories/bottle-pullout/products/?page_size=1 HTTP/1.1" 200 216
[2025-05-14 12:38:40,723] - INFO - django.server - "GET /api/v1/products/categories/majic-carnes/products/?page_size=1 HTTP/1.1" 200 212
[2025-05-14 12:38:41,060] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:38:41,360] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:38:41,428] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:38:41,895] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:38:42,695] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:38:42,848] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:38:43,186] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:38:43,588] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:38:43,689] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:38:44,010] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=1 HTTP/1.1" 200 142
[2025-05-14 12:38:44,802] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=1 HTTP/1.1" 200 132
[2025-05-14 12:38:44,952] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:38:45,382] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:38:45,698] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:38:45,787] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:38:46,131] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:38:46,883] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:38:47,053] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:38:47,650] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:38:47,884] - INFO - django.server - "GET /api/v1/products/categories/chimney/products/?page_size=1 HTTP/1.1" 200 200
[2025-05-14 12:38:47,922] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:38:48,214] - INFO - django.server - "GET /api/v1/products/categories/mov/products/?page_size=1 HTTP/1.1" 200 192
[2025-05-14 12:38:49,008] - INFO - django.server - "GET /api/v1/products/categories/fevicol-jivanjor/products/?page_size=1 HTTP/1.1" 200 208
[2025-05-14 12:38:49,159] - INFO - django.server - "GET /api/v1/products/categories/polymyts/products/?page_size=1 HTTP/1.1" 200 192
[2025-05-14 12:38:49,751] - INFO - django.server - "GET /api/v1/products/categories/silicon/products/?page_size=1 HTTP/1.1" 200 190
[2025-05-14 12:38:49,983] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:38:50,013] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:38:50,312] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:38:51,127] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:38:51,264] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:38:51,991] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:38:52,122] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:38:52,237] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:38:52,407] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:38:53,260] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=1 HTTP/1.1" 200 2210
[2025-05-14 12:38:53,368] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:38:54,120] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:38:54,241] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:38:54,340] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:38:54,621] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:38:55,480] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:38:55,513] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=1 HTTP/1.1" 200 3198
[2025-05-14 12:38:56,265] - INFO - django.server - "GET /api/v1/products/categories/solid-surface/products/?page_size=1 HTTP/1.1" 200 1392
[2025-05-14 12:38:56,348] - INFO - django.server - "GET /api/v1/products/categories/laminates/products/?page_size=1 HTTP/1.1" 200 124
[2025-05-14 12:38:56,447] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:38:56,721] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:38:57,572] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:38:57,719] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:38:58,452] - INFO - django.server - "GET /api/v1/products/categories/brillient/products/?page_size=1 HTTP/1.1" 200 185
[2025-05-14 12:38:58,530] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:38:58,544] - INFO - django.server - "GET /api/v1/products/categories/crystal/products/?page_size=1 HTTP/1.1" 200 181
[2025-05-14 12:38:58,841] - INFO - django.server - "GET /api/v1/products/categories/ace/products/?page_size=1 HTTP/1.1" 200 173
[2025-05-14 12:38:59,684] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:38:59,820] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:39:00,658] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:39:00,732] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:39:00,971] - INFO - django.server - "GET /api/v1/products/categories/air-purifiers/products/?page_size=1 HTTP/1.1" 200 1384
[2025-05-14 12:39:00,983] - INFO - django.server - "GET /api/v1/products/categories/security-cameras/products/?page_size=1 HTTP/1.1" 200 1436
[2025-05-14 12:39:01,815] - INFO - django.server - "GET /api/v1/products/categories/car-security-cameras/products/?page_size=1 HTTP/1.1" 200 1301
[2025-05-14 12:39:01,943] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:39:02,888] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:39:02,927] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:39:03,072] - INFO - django.server - "GET /api/v1/products/categories/air-purifiers-accessories/products/?page_size=1 HTTP/1.1" 200 227
[2025-05-14 12:39:03,090] - INFO - django.server - "GET /api/v1/products/categories/door-locks/products/?page_size=1 HTTP/1.1" 200 1368
[2025-05-14 12:39:03,922] - INFO - django.server - "GET /api/v1/products/categories/security-cameras-accessories/products/?page_size=1 HTTP/1.1" 200 239
[2025-05-14 12:39:04,063] - INFO - django.server - "GET /api/v1/products/categories/video-doorbells/products/?page_size=1 HTTP/1.1" 200 1419
[2025-05-14 12:39:04,998] - INFO - django.server - "GET /api/v1/products/categories/smart-cameras/products/?page_size=1 HTTP/1.1" 200 1426
[2025-05-14 12:39:05,062] - INFO - django.server - "GET /api/v1/products/categories/bullet-cameras/products/?page_size=1 HTTP/1.1" 200 1685
[2025-05-14 12:39:05,202] - INFO - django.server - "GET /api/v1/products/categories/car-security-cameras-accessories/products/?page_size=1 HTTP/1.1" 200 1410
[2025-05-14 12:39:05,363] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:39:06,198] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:39:07,356] - INFO - django.server - "GET /api/v1/products/categories/smart-switches/products/?page_size=1 HTTP/1.1" 200 1069
[2025-05-14 12:39:07,357] - INFO - django.server - "GET /api/v1/products/categories/tyre-air-pumps/products/?page_size=1 HTTP/1.1" 200 134
[2025-05-14 12:39:07,392] - INFO - django.server - "GET /api/v1/products/categories/wireless-chargers/products/?page_size=1 HTTP/1.1" 200 1630
[2025-05-14 12:39:08,428] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:39:09,533] - INFO - django.server - "GET /api/v1/products/categories/tyre-air-pumps-accessories/products/?page_size=1 HTTP/1.1" 200 231
[2025-05-14 12:39:09,545] - INFO - django.server - "GET /api/v1/products/categories/smart-bulbs/products/?page_size=1 HTTP/1.1" 200 1260
[2025-05-14 12:39:09,554] - INFO - django.server - "GET /api/v1/products/categories/smart-plugs/products/?page_size=1 HTTP/1.1" 200 1346
[2025-05-14 12:39:09,562] - INFO - django.server - "GET /api/v1/products/categories/smart-switches-accessories/products/?page_size=1 HTTP/1.1" 200 1076
[2025-05-14 12:39:09,564] - INFO - django.server - "GET /api/v1/products/categories/wireless-chargers-accessories/products/?page_size=1 HTTP/1.1" 200 1745
[2025-05-14 12:39:11,672] - INFO - django.server - "GET /api/v1/products/categories/smart-lighting/products/?page_size=1 HTTP/1.1" 200 134
[2025-05-14 12:39:11,682] - INFO - django.server - "GET /api/v1/products/categories/video-door-phones/products/?page_size=1 HTTP/1.1" 200 942
[2025-05-14 12:39:11,701] - INFO - django.server - "GET /api/v1/products/categories/smart-door-locks/products/?page_size=1 HTTP/1.1" 200 1332
[2025-05-14 12:39:13,807] - INFO - django.server - "GET /api/v1/products/categories/smart-lighting-accessories/products/?page_size=1 HTTP/1.1" 200 231
[2025-05-14 12:39:13,826] - INFO - django.server - "GET /api/v1/products/categories/smart-door-locks-accessories/products/?page_size=1 HTTP/1.1" 200 239
[2025-05-14 12:39:13,844] - INFO - django.server - "GET /api/v1/products/categories/video-door-phones-accessories/products/?page_size=1 HTTP/1.1" 200 1045
[2025-05-14 12:39:16,004] - INFO - django.server - "GET /api/v1/products/categories/mobile-cables/products/?page_size=1 HTTP/1.1" 200 132
[2025-05-14 12:39:16,010] - INFO - django.server - "GET /api/v1/products/categories/gps-navigation-devices/products/?page_size=1 HTTP/1.1" 200 1626
[2025-05-14 12:39:16,023] - INFO - django.server - "GET /api/v1/products/categories/gimbals/products/?page_size=1 HTTP/1.1" 200 1268
[2025-05-14 12:39:18,109] - INFO - django.server - "GET /api/v1/products/categories/mobile-cables-accessories/products/?page_size=1 HTTP/1.1" 200 227
[2025-05-14 12:39:18,115] - INFO - django.server - "GET /api/v1/products/categories/gps-navigation-devices-accessories/products/?page_size=1 HTTP/1.1" 200 1751
[2025-05-14 12:39:18,139] - INFO - django.server - "GET /api/v1/products/categories/gimbals-accessories/products/?page_size=1 HTTP/1.1" 200 1351
[2025-05-14 12:39:20,267] - INFO - django.server - "GET /api/v1/products/categories/baby-monitor/products/?page_size=1 HTTP/1.1" 200 130
[2025-05-14 12:39:20,279] - INFO - django.server - "GET /api/v1/products/categories/power-banks/products/?page_size=1 HTTP/1.1" 200 128
[2025-05-14 12:39:20,290] - INFO - django.server - "GET /api/v1/products/categories/led-lights/products/?page_size=1 HTTP/1.1" 200 126
[2025-05-14 12:39:22,384] - INFO - django.server - "GET /api/v1/products/categories/baby-monitor-accessories/products/?page_size=1 HTTP/1.1" 200 223
[2025-05-14 12:39:22,394] - INFO - django.server - "GET /api/v1/products/categories/power-banks-accessories/products/?page_size=1 HTTP/1.1" 200 219
[2025-05-14 12:39:22,423] - INFO - django.server - "GET /api/v1/products/categories/led-lights-accessories/products/?page_size=1 HTTP/1.1" 200 215
[2025-05-14 12:39:24,519] - INFO - django.server - "GET /api/v1/products/categories/egg-cookers/products/?page_size=1 HTTP/1.1" 200 128
[2025-05-14 12:39:24,535] - INFO - django.server - "GET /api/v1/products/categories/wall-lamps/products/?page_size=1 HTTP/1.1" 200 704
[2025-05-14 12:39:24,547] - INFO - django.server - "GET /api/v1/products/categories/study-lamps/products/?page_size=1 HTTP/1.1" 200 656
[2025-05-14 12:39:26,692] - INFO - django.server - "GET /api/v1/products/categories/wall-lamps-accessories/products/?page_size=1 HTTP/1.1" 200 793
[2025-05-14 12:39:26,701] - INFO - django.server - "GET /api/v1/products/categories/study-lamps-accessories/products/?page_size=1 HTTP/1.1" 200 747
[2025-05-14 12:39:28,819] - INFO - django.server - "GET /api/v1/products/categories/smart-home-devices/products/?page_size=1 HTTP/1.1" 200 1049
[2025-05-14 12:39:30,954] - INFO - django.server - "GET /api/v1/products/categories/home-tabs/products/?page_size=1 HTTP/1.1" 200 1112
[2025-05-14 12:40:13,298] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:40:13,298] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:40:13,327] - INFO - django.server - "GET /api/v1/products/categories/ HTTP/1.1" 200 5001
[2025-05-14 12:40:13,384] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:40:13,457] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 8594
[2025-05-14 12:40:15,510] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62487)
[2025-05-14 12:40:16,311] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62528)
[2025-05-14 12:40:16,528] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62488)
[2025-05-14 12:40:16,544] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62490)
[2025-05-14 12:40:16,666] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62536)
[2025-05-14 12:40:16,764] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:40:17,113] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:40:17,115] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:40:17,139] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:17,155] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:40:17,175] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:40:18,875] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:19,268] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:19,307] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:19,333] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:40:19,432] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:40:19,466] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:40:21,086] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:40:21,449] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:40:21,470] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:21,500] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:40:21,578] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:21,578] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:23,319] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:40:23,580] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:40:23,625] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:23,677] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:40:23,690] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:40:23,800] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:40:25,445] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:25,745] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:25,805] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:40:25,805] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:25,914] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:40:25,919] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:40:27,545] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:40:27,870] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:27,937] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:28,024] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:40:28,040] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:28,140] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:40:29,647] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:40:29,950] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:30,152] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:30,218] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:40:30,430] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:40:31,739] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:32,046] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:32,433] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:40:32,635] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:40:33,848] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:34,152] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:34,687] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:40:34,934] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:40:36,247] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:37,007] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64069)
[2025-05-14 12:40:37,246] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64076)
[2025-05-14 12:40:40,054] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:40:40,056] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:40:40,203] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:40:40,312] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 9887
[2025-05-14 12:40:40,551] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64073)
[2025-05-14 12:40:41,604] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64481)
[2025-05-14 12:40:41,698] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64485)
[2025-05-14 12:40:42,195] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:40:42,198] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:40:42,270] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:40:42,290] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:40:42,303] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:40:42,438] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:44,315] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:44,315] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:44,535] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:40:44,554] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:40:44,570] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:40:44,574] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:40:46,444] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:40:46,610] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:40:46,640] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:46,677] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:46,700] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:46,875] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:40:48,727] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:40:48,731] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:40:48,790] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:40:48,830] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:48,914] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:40:48,990] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:50,829] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:50,942] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:40:50,953] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:40:51,035] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:40:51,044] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:51,103] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:40:52,916] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:53,066] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:53,142] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:53,221] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:40:53,222] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:40:53,272] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:40:55,041] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:55,178] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:55,320] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:40:55,453] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:40:55,529] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:40:57,153] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64071)
[2025-05-14 12:40:57,301] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64494)
[2025-05-14 12:40:57,717] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64078)
[2025-05-14 12:40:57,786] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64502)
[2025-05-14 12:40:58,899] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62486)
[2025-05-14 12:41:00,235] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:41:00,280] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:41:00,611] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:41:00,661] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 7713
[2025-05-14 12:41:00,852] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64829)
[2025-05-14 12:41:01,777] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64845)
[2025-05-14 12:41:02,048] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64851)
[2025-05-14 12:41:02,207] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:41:02,415] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:41:02,416] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:41:02,516] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:41:02,716] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:41:02,776] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:04,307] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:04,501] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:04,623] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:41:04,715] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:41:04,905] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:41:04,971] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:41:06,448] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:41:06,612] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:41:06,742] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:06,846] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:06,997] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:07,171] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:41:08,676] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:41:08,840] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:41:08,865] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:41:08,957] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:41:09,131] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:09,285] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:10,772] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:11,061] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:41:11,073] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:41:11,087] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:41:11,232] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:41:11,396] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:12,882] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:13,199] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:13,296] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:41:13,314] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:41:13,361] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:41:13,514] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:14,998] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:15,316] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:15,520] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:41:15,521] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:41:15,601] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:17,117] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:17,424] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:17,730] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:41:17,767] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:41:19,226] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:19,545] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:19,966] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:41:19,966] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:41:21,649] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:22,181] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:41:22,181] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:41:24,431] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:41:24,431] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:41:26,760] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64822)
[2025-05-14 12:41:28,242] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:41:28,248] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:41:28,413] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:41:28,564] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 9569
[2025-05-14 12:41:28,845] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64858)
[2025-05-14 12:41:29,618] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64862)
[2025-05-14 12:41:30,317] - INFO - django.server - - Broken pipe from ('127.0.0.1', 65317)
[2025-05-14 12:41:30,536] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:41:30,551] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:41:30,586] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:41:30,603] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:41:30,646] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:41:30,661] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:32,641] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:32,654] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:32,790] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:41:32,870] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:41:32,877] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:41:32,893] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:41:34,745] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:41:34,863] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:41:34,895] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:34,967] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:34,987] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:35,113] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:41:36,953] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:41:36,975] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:41:37,067] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:41:37,088] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:37,113] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:41:37,209] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:39,055] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:39,190] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:41:39,204] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:41:39,227] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:39,279] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:41:39,335] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:41:41,153] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:41,308] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:41,338] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:41,412] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:41:41,439] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:41:41,486] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:41:43,238] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:43,411] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:43,555] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:43,648] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:41:43,694] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:41:45,347] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:45,509] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:41:45,871] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:41:45,946] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:41:47,455] - INFO - django.server - - Broken pipe from ('127.0.0.1', 65335)
[2025-05-14 12:41:47,608] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64498)
[2025-05-14 12:41:48,247] - INFO - django.server - - Broken pipe from ('127.0.0.1', 65325)
[2025-05-14 12:41:48,309] - INFO - django.server - - Broken pipe from ('127.0.0.1', 65329)
[2025-05-14 12:41:53,055] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49328)
[2025-05-14 12:41:53,599] - INFO - django.server - "GET /api/v1/products/categories/ HTTP/1.1" 200 5001
[2025-05-14 12:41:54,575] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49340)
[2025-05-14 12:41:55,700] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:41:55,701] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:41:55,814] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:41:55,884] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 7672
[2025-05-14 12:41:55,891] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49390)
[2025-05-14 12:41:56,296] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=1 HTTP/1.1" 200 1056
[2025-05-14 12:41:56,635] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=1 HTTP/1.1" 200 953
[2025-05-14 12:41:57,120] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=1 HTTP/1.1" 200 1036
[2025-05-14 12:41:57,934] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:41:58,092] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:41:58,125] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:41:58,398] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:41:58,734] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:41:59,228] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:42:00,053] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:42:00,198] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:42:00,398] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:42:00,644] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:42:00,828] - INFO - django.server - "GET /api/v1/products/categories/screws/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:42:01,342] - INFO - django.server - "GET /api/v1/products/categories/hinges/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:42:02,160] - INFO - django.server - "GET /api/v1/products/categories/tower-bolt/products/?page_size=1 HTTP/1.1" 200 185
[2025-05-14 12:42:02,302] - INFO - django.server - "GET /api/v1/products/categories/door-kits/products/?page_size=1 HTTP/1.1" 200 183
[2025-05-14 12:42:02,516] - INFO - django.server - "GET /api/v1/products/categories/handles/products/?page_size=1 HTTP/1.1" 200 179
[2025-05-14 12:42:02,732] - INFO - django.server - "GET /api/v1/products/categories/t-channel/products/?page_size=1 HTTP/1.1" 200 203
[2025-05-14 12:42:02,953] - INFO - django.server - "GET /api/v1/products/categories/tendom/products/?page_size=1 HTTP/1.1" 200 198
[2025-05-14 12:42:03,460] - INFO - django.server - "GET /api/v1/products/categories/tendom-box/products/?page_size=1 HTTP/1.1" 200 208
[2025-05-14 12:42:04,271] - INFO - django.server - "GET /api/v1/products/categories/bottle-pullout/products/?page_size=1 HTTP/1.1" 200 216
[2025-05-14 12:42:04,424] - INFO - django.server - "GET /api/v1/products/categories/majic-carnes/products/?page_size=1 HTTP/1.1" 200 212
[2025-05-14 12:42:04,746] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:42:05,017] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:42:05,073] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:42:05,570] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:42:06,382] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:42:06,560] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:42:06,853] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:42:07,431] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49401)
[2025-05-14 12:42:07,493] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49673)
[2025-05-14 12:42:07,683] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49683)
[2025-05-14 12:42:08,504] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49389)
[2025-05-14 12:42:08,704] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49391)
[2025-05-14 12:42:09,356] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49392)
[2025-05-14 12:42:09,976] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:42:09,977] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:42:10,070] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:42:10,504] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 10041
[2025-05-14 12:42:10,615] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49937)
[2025-05-14 12:42:11,147] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=1 HTTP/1.1" 200 953
[2025-05-14 12:42:11,191] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=1 HTTP/1.1" 200 1056
[2025-05-14 12:42:11,705] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=1 HTTP/1.1" 200 1036
[2025-05-14 12:42:12,197] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49913)
[2025-05-14 12:42:12,303] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49914)
[2025-05-14 12:42:12,738] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49932)
[2025-05-14 12:42:13,269] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49951)
[2025-05-14 12:42:13,288] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49952)
[2025-05-14 12:42:13,829] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49964)
[2025-05-14 12:43:10,494] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:43:10,496] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:43:10,636] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:43:10,737] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 7659
[2025-05-14 12:43:10,787] - INFO - django.server - - Broken pipe from ('127.0.0.1', 50183)
[2025-05-14 12:43:11,485] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=1 HTTP/1.1" 200 1056
[2025-05-14 12:43:11,511] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=1 HTTP/1.1" 200 953
[2025-05-14 12:43:12,085] - INFO - django.server - - Broken pipe from ('127.0.0.1', 50993)
[2025-05-14 12:43:12,804] - INFO - django.server - - Broken pipe from ('127.0.0.1', 50180)
[2025-05-14 12:43:13,042] - INFO - django.server - - Broken pipe from ('127.0.0.1', 50181)
[2025-05-14 12:43:13,134] - INFO - django.server - - Broken pipe from ('127.0.0.1', 50182)
[2025-05-14 12:43:13,617] - INFO - django.server - - Broken pipe from ('127.0.0.1', 50984)
[2025-05-14 12:43:13,636] - INFO - django.server - - Broken pipe from ('127.0.0.1', 50985)
[2025-05-14 12:43:14,822] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:43:14,823] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:43:14,906] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:43:14,978] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 9107
[2025-05-14 12:43:15,142] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51031)
[2025-05-14 12:43:15,881] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=1 HTTP/1.1" 200 953
[2025-05-14 12:43:15,883] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=1 HTTP/1.1" 200 1056
[2025-05-14 12:43:16,428] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=1 HTTP/1.1" 200 1036
[2025-05-14 12:43:16,991] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:43:17,150] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:43:17,241] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:43:17,978] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:43:18,007] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:43:18,529] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:43:19,089] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:43:19,263] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:43:19,495] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:43:20,137] - INFO - django.server - "GET /api/v1/products/categories/t-channel/products/?page_size=1 HTTP/1.1" 200 203
[2025-05-14 12:43:20,260] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:43:20,658] - INFO - django.server - "GET /api/v1/products/categories/tendom/products/?page_size=1 HTTP/1.1" 200 198
[2025-05-14 12:43:21,197] - INFO - django.server - "GET /api/v1/products/categories/screws/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:43:21,409] - INFO - django.server - "GET /api/v1/products/categories/hinges/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:43:21,643] - INFO - django.server - "GET /api/v1/products/categories/tower-bolt/products/?page_size=1 HTTP/1.1" 200 185
[2025-05-14 12:43:22,249] - INFO - django.server - "GET /api/v1/products/categories/door-kits/products/?page_size=1 HTTP/1.1" 200 183
[2025-05-14 12:43:22,379] - INFO - django.server - "GET /api/v1/products/categories/handles/products/?page_size=1 HTTP/1.1" 200 179
[2025-05-14 12:43:22,771] - INFO - django.server - "GET /api/v1/products/categories/tendom-box/products/?page_size=1 HTTP/1.1" 200 208
[2025-05-14 12:43:23,310] - INFO - django.server - "GET /api/v1/products/categories/bottle-pullout/products/?page_size=1 HTTP/1.1" 200 216
[2025-05-14 12:43:23,538] - INFO - django.server - "GET /api/v1/products/categories/majic-carnes/products/?page_size=1 HTTP/1.1" 200 212
[2025-05-14 12:43:23,948] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:43:24,533] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:43:24,553] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:43:24,913] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:43:25,426] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:43:25,651] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:43:26,068] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:43:26,760] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:43:26,771] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:43:27,029] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=1 HTTP/1.1" 200 142
[2025-05-14 12:43:27,527] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=1 HTTP/1.1" 200 132
[2025-05-14 12:43:27,740] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:43:28,315] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:43:28,885] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:43:28,885] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:43:29,155] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:43:29,656] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:43:29,864] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:43:30,536] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:43:31,000] - INFO - django.server - "GET /api/v1/products/categories/chimney/products/?page_size=1 HTTP/1.1" 200 200
[2025-05-14 12:43:31,184] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:43:31,303] - INFO - django.server - "GET /api/v1/products/categories/mov/products/?page_size=1 HTTP/1.1" 200 192
[2025-05-14 12:43:31,745] - INFO - django.server - "GET /api/v1/products/categories/fevicol-jivanjor/products/?page_size=1 HTTP/1.1" 200 208
[2025-05-14 12:43:31,970] - INFO - django.server - "GET /api/v1/products/categories/polymyts/products/?page_size=1 HTTP/1.1" 200 192
[2025-05-14 12:43:32,645] - INFO - django.server - "GET /api/v1/products/categories/silicon/products/?page_size=1 HTTP/1.1" 200 190
[2025-05-14 12:43:33,114] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:43:33,318] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:43:33,402] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:43:33,860] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:43:34,098] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:43:34,830] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:43:35,327] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:43:35,422] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:43:35,495] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:43:35,965] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=1 HTTP/1.1" 200 2210
[2025-05-14 12:43:36,197] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:43:36,941] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:43:37,430] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:43:37,513] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:43:37,723] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:43:38,207] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51028)
[2025-05-14 12:43:38,320] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51029)
[2025-05-14 12:43:39,061] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51030)
[2025-05-14 12:43:39,559] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51066)
[2025-05-14 12:43:39,646] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51067)
[2025-05-14 12:43:39,980] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51079)
[2025-05-14 12:44:45,175] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:44:45,176] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:44:45,264] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:44:45,346] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 7679
[2025-05-14 12:44:45,524] - INFO - django.server - - Broken pipe from ('127.0.0.1', 52362)
[2025-05-14 12:44:45,733] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=1 HTTP/1.1" 200 1056
[2025-05-14 12:44:46,096] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=1 HTTP/1.1" 200 953
[2025-05-14 12:44:46,507] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=1 HTTP/1.1" 200 1036
[2025-05-14 12:44:47,417] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:44:47,528] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:44:47,604] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:44:47,620] - INFO - django.server - "POST /api/v1/users/token/refresh/ HTTP/1.1" 200 241
[2025-05-14 12:44:47,838] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:44:48,225] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:44:48,615] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:44:49,508] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:44:49,639] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:44:49,857] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:44:50,148] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:44:50,334] - INFO - django.server - "GET /api/v1/products/categories/screws/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:44:50,711] - INFO - django.server - "GET /api/v1/products/categories/hinges/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:44:51,626] - INFO - django.server - "GET /api/v1/products/categories/tower-bolt/products/?page_size=1 HTTP/1.1" 200 185
[2025-05-14 12:44:51,733] - INFO - django.server - "GET /api/v1/products/categories/door-kits/products/?page_size=1 HTTP/1.1" 200 183
[2025-05-14 12:44:51,958] - INFO - django.server - "GET /api/v1/products/categories/handles/products/?page_size=1 HTTP/1.1" 200 179
[2025-05-14 12:44:52,257] - INFO - django.server - "GET /api/v1/products/categories/t-channel/products/?page_size=1 HTTP/1.1" 200 203
[2025-05-14 12:44:52,461] - INFO - django.server - "GET /api/v1/products/categories/tendom/products/?page_size=1 HTTP/1.1" 200 198
[2025-05-14 12:44:52,809] - INFO - django.server - "GET /api/v1/products/categories/tendom-box/products/?page_size=1 HTTP/1.1" 200 208
[2025-05-14 12:44:53,723] - INFO - django.server - "GET /api/v1/products/categories/bottle-pullout/products/?page_size=1 HTTP/1.1" 200 216
[2025-05-14 12:44:53,858] - INFO - django.server - "GET /api/v1/products/categories/majic-carnes/products/?page_size=1 HTTP/1.1" 200 212
[2025-05-14 12:44:54,251] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:44:54,571] - INFO - django.server - - Broken pipe from ('127.0.0.1', 52375)
[2025-05-14 12:44:54,593] - INFO - django.server - - Broken pipe from ('127.0.0.1', 52364)
[2025-05-14 12:44:54,894] - INFO - django.server - - Broken pipe from ('127.0.0.1', 52384)
[2025-05-14 12:44:55,888] - INFO - django.server - - Broken pipe from ('127.0.0.1', 52352)
[2025-05-14 12:44:55,981] - INFO - django.server - - Broken pipe from ('127.0.0.1', 52353)
[2025-05-14 12:44:56,373] - INFO - django.server - - Broken pipe from ('127.0.0.1', 52354)
[2025-05-14 12:44:57,634] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:44:57,637] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:44:57,711] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:44:57,779] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 9122
[2025-05-14 12:44:58,496] - INFO - django.server - - Broken pipe from ('127.0.0.1', 52608)
[2025-05-14 12:44:59,038] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=1 HTTP/1.1" 200 953
[2025-05-14 12:44:59,038] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=1 HTTP/1.1" 200 1056
[2025-05-14 12:44:59,494] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=1 HTTP/1.1" 200 1036
[2025-05-14 12:44:59,802] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:44:59,942] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:44:59,994] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:45:01,149] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:45:01,168] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:45:01,582] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:01,938] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:02,053] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:02,204] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:45:03,264] - INFO - django.server - "GET /api/v1/products/categories/t-channel/products/?page_size=1 HTTP/1.1" 200 203
[2025-05-14 12:45:03,392] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:45:03,697] - INFO - django.server - "GET /api/v1/products/categories/tendom/products/?page_size=1 HTTP/1.1" 200 198
[2025-05-14 12:45:04,063] - INFO - django.server - "GET /api/v1/products/categories/screws/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:45:04,169] - INFO - django.server - "GET /api/v1/products/categories/hinges/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:45:04,325] - INFO - django.server - "GET /api/v1/products/categories/tower-bolt/products/?page_size=1 HTTP/1.1" 200 185
[2025-05-14 12:45:05,365] - INFO - django.server - "GET /api/v1/products/categories/door-kits/products/?page_size=1 HTTP/1.1" 200 183
[2025-05-14 12:45:05,509] - INFO - django.server - "GET /api/v1/products/categories/handles/products/?page_size=1 HTTP/1.1" 200 179
[2025-05-14 12:45:05,807] - INFO - django.server - "GET /api/v1/products/categories/tendom-box/products/?page_size=1 HTTP/1.1" 200 208
[2025-05-14 12:45:06,165] - INFO - django.server - "GET /api/v1/products/categories/bottle-pullout/products/?page_size=1 HTTP/1.1" 200 216
[2025-05-14 12:45:06,264] - INFO - django.server - "GET /api/v1/products/categories/majic-carnes/products/?page_size=1 HTTP/1.1" 200 212
[2025-05-14 12:45:06,579] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:45:07,588] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:45:07,601] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:45:07,908] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:45:08,284] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:08,366] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:08,675] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:09,809] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:45:09,809] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:45:10,029] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=1 HTTP/1.1" 200 142
[2025-05-14 12:45:10,397] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=1 HTTP/1.1" 200 132
[2025-05-14 12:45:10,469] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:45:10,855] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:45:11,920] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:45:11,935] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:45:12,155] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:12,512] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:12,595] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:13,076] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:45:14,044] - INFO - django.server - "GET /api/v1/products/categories/chimney/products/?page_size=1 HTTP/1.1" 200 200
[2025-05-14 12:45:14,132] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:45:14,298] - INFO - django.server - "GET /api/v1/products/categories/mov/products/?page_size=1 HTTP/1.1" 200 192
[2025-05-14 12:45:14,636] - INFO - django.server - "GET /api/v1/products/categories/fevicol-jivanjor/products/?page_size=1 HTTP/1.1" 200 208
[2025-05-14 12:45:14,697] - INFO - django.server - "GET /api/v1/products/categories/polymyts/products/?page_size=1 HTTP/1.1" 200 192
[2025-05-14 12:45:15,157] - INFO - django.server - "GET /api/v1/products/categories/silicon/products/?page_size=1 HTTP/1.1" 200 190
[2025-05-14 12:45:16,170] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:45:16,232] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:45:16,414] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:16,743] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:16,804] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:17,368] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:45:18,339] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:45:18,393] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:45:18,503] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:45:18,888] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=1 HTTP/1.1" 200 2210
[2025-05-14 12:45:18,911] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:45:19,453] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:20,455] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:20,517] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:20,707] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:45:21,033] - INFO - django.server - "GET /api/v1/products/categories/door-locks/products/?page_size=1 HTTP/1.1" 200 1368
[2025-05-14 12:45:21,078] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:45:21,580] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:22,573] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:22,635] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:22,921] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:45:23,172] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=1 HTTP/1.1" 200 3198
[2025-05-14 12:45:23,220] - INFO - django.server - "GET /api/v1/products/categories/solid-surface/products/?page_size=1 HTTP/1.1" 200 1392
[2025-05-14 12:45:23,658] - INFO - django.server - "GET /api/v1/products/categories/laminates/products/?page_size=1 HTTP/1.1" 200 124
[2025-05-14 12:45:24,743] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:24,809] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:45:25,069] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:25,367] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:45:25,750] - INFO - django.server - "GET /api/v1/products/categories/brillient/products/?page_size=1 HTTP/1.1" 200 185
[2025-05-14 12:45:25,752] - INFO - django.server - "GET /api/v1/products/categories/crystal/products/?page_size=1 HTTP/1.1" 200 181
[2025-05-14 12:45:26,822] - INFO - django.server - "GET /api/v1/products/categories/ace/products/?page_size=1 HTTP/1.1" 200 173
[2025-05-14 12:45:27,037] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:45:27,150] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:45:27,572] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:45:28,936] - INFO - django.server - "GET /api/v1/products/categories/car-security-cameras/products/?page_size=1 HTTP/1.1" 200 1301
[2025-05-14 12:45:28,942] - INFO - django.server - "GET /api/v1/products/categories/air-purifiers/products/?page_size=1 HTTP/1.1" 200 1384
[2025-05-14 12:45:28,957] - INFO - django.server - "GET /api/v1/products/categories/security-cameras/products/?page_size=1 HTTP/1.1" 200 1436
[2025-05-14 12:45:29,314] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:45:29,782] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:45:31,059] - INFO - django.server - "GET /api/v1/products/categories/air-purifiers-accessories/products/?page_size=1 HTTP/1.1" 200 227
[2025-05-14 12:45:31,077] - INFO - django.server - "GET /api/v1/products/categories/car-security-cameras-accessories/products/?page_size=1 HTTP/1.1" 200 1410
[2025-05-14 12:45:31,090] - INFO - django.server - "GET /api/v1/products/categories/security-cameras-accessories/products/?page_size=1 HTTP/1.1" 200 239
[2025-05-14 12:45:31,102] - INFO - django.server - "GET /api/v1/products/categories/video-doorbells/products/?page_size=1 HTTP/1.1" 200 1419
[2025-05-14 12:45:31,440] - INFO - django.server - "GET /api/v1/products/categories/smart-cameras/products/?page_size=1 HTTP/1.1" 200 1426
[2025-05-14 12:45:31,922] - INFO - django.server - "GET /api/v1/products/categories/bullet-cameras/products/?page_size=1 HTTP/1.1" 200 1685
[2025-05-14 12:45:33,253] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:45:34,036] - INFO - django.server - "GET /api/v1/products/categories/tyre-air-pumps/products/?page_size=1 HTTP/1.1" 200 134
[2025-05-14 12:45:34,045] - INFO - django.server - "GET /api/v1/products/categories/wireless-chargers/products/?page_size=1 HTTP/1.1" 200 1630
[2025-05-14 12:45:34,056] - INFO - django.server - "GET /api/v1/products/categories/smart-switches/products/?page_size=1 HTTP/1.1" 200 1069
[2025-05-14 12:45:35,477] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:45:36,126] - INFO - django.server - "GET /api/v1/products/categories/tyre-air-pumps-accessories/products/?page_size=1 HTTP/1.1" 200 231
[2025-05-14 12:45:36,162] - INFO - django.server - "GET /api/v1/products/categories/wireless-chargers-accessories/products/?page_size=1 HTTP/1.1" 200 1745
[2025-05-14 12:45:36,173] - INFO - django.server - "GET /api/v1/products/categories/smart-switches-accessories/products/?page_size=1 HTTP/1.1" 200 1076
[2025-05-14 12:45:36,185] - INFO - django.server - "GET /api/v1/products/categories/smart-plugs/products/?page_size=1 HTTP/1.1" 200 1346
[2025-05-14 12:45:36,193] - INFO - django.server - "GET /api/v1/products/categories/smart-bulbs/products/?page_size=1 HTTP/1.1" 200 1260
[2025-05-14 12:45:38,301] - INFO - django.server - "GET /api/v1/products/categories/video-door-phones/products/?page_size=1 HTTP/1.1" 200 942
[2025-05-14 12:45:38,313] - INFO - django.server - "GET /api/v1/products/categories/smart-lighting/products/?page_size=1 HTTP/1.1" 200 134
[2025-05-14 12:45:38,331] - INFO - django.server - "GET /api/v1/products/categories/smart-door-locks/products/?page_size=1 HTTP/1.1" 200 1332
[2025-05-14 12:45:40,412] - INFO - django.server - "GET /api/v1/products/categories/smart-lighting-accessories/products/?page_size=1 HTTP/1.1" 200 231
[2025-05-14 12:45:40,426] - INFO - django.server - "GET /api/v1/products/categories/video-door-phones-accessories/products/?page_size=1 HTTP/1.1" 200 1045
[2025-05-14 12:45:40,433] - INFO - django.server - "GET /api/v1/products/categories/smart-door-locks-accessories/products/?page_size=1 HTTP/1.1" 200 239
[2025-05-14 12:45:42,527] - INFO - django.server - "GET /api/v1/products/categories/mobile-cables/products/?page_size=1 HTTP/1.1" 200 132
[2025-05-14 12:45:42,550] - INFO - django.server - "GET /api/v1/products/categories/gimbals/products/?page_size=1 HTTP/1.1" 200 1268
[2025-05-14 12:45:42,558] - INFO - django.server - "GET /api/v1/products/categories/gps-navigation-devices/products/?page_size=1 HTTP/1.1" 200 1626
[2025-05-14 12:45:44,635] - INFO - django.server - "GET /api/v1/products/categories/mobile-cables-accessories/products/?page_size=1 HTTP/1.1" 200 227
[2025-05-14 12:45:44,671] - INFO - django.server - "GET /api/v1/products/categories/gps-navigation-devices-accessories/products/?page_size=1 HTTP/1.1" 200 1751
[2025-05-14 12:45:44,676] - INFO - django.server - "GET /api/v1/products/categories/gimbals-accessories/products/?page_size=1 HTTP/1.1" 200 1351
[2025-05-14 12:45:46,767] - INFO - django.server - "GET /api/v1/products/categories/power-banks/products/?page_size=1 HTTP/1.1" 200 128
[2025-05-14 12:45:46,767] - INFO - django.server - "GET /api/v1/products/categories/led-lights/products/?page_size=1 HTTP/1.1" 200 126
[2025-05-14 12:45:46,797] - INFO - django.server - "GET /api/v1/products/categories/baby-monitor/products/?page_size=1 HTTP/1.1" 200 130
[2025-05-14 12:45:48,882] - INFO - django.server - "GET /api/v1/products/categories/led-lights-accessories/products/?page_size=1 HTTP/1.1" 200 215
[2025-05-14 12:45:48,884] - INFO - django.server - "GET /api/v1/products/categories/power-banks-accessories/products/?page_size=1 HTTP/1.1" 200 219
[2025-05-14 12:45:48,912] - INFO - django.server - "GET /api/v1/products/categories/baby-monitor-accessories/products/?page_size=1 HTTP/1.1" 200 223
[2025-05-14 12:45:51,009] - INFO - django.server - "GET /api/v1/products/categories/egg-cookers/products/?page_size=1 HTTP/1.1" 200 128
[2025-05-14 12:45:51,039] - INFO - django.server - "GET /api/v1/products/categories/study-lamps/products/?page_size=1 HTTP/1.1" 200 656
[2025-05-14 12:45:51,039] - INFO - django.server - "GET /api/v1/products/categories/wall-lamps/products/?page_size=1 HTTP/1.1" 200 704
[2025-05-14 12:45:53,150] - INFO - django.server - "GET /api/v1/products/categories/wall-lamps-accessories/products/?page_size=1 HTTP/1.1" 200 793
[2025-05-14 12:45:53,174] - INFO - django.server - "GET /api/v1/products/categories/study-lamps-accessories/products/?page_size=1 HTTP/1.1" 200 747
[2025-05-14 12:45:55,309] - INFO - django.server - "GET /api/v1/products/categories/smart-home-devices/products/?page_size=1 HTTP/1.1" 200 1049
[2025-05-14 12:45:57,427] - INFO - django.server - "GET /api/v1/products/categories/home-tabs/products/?page_size=1 HTTP/1.1" 200 1112
[2025-05-14 12:48:09,743] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:48:09,743] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:48:09,815] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:48:09,946] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 10963
[2025-05-14 12:48:10,114] - INFO - django.server - - Broken pipe from ('127.0.0.1', 52577)
[2025-05-14 12:48:10,593] - INFO - django.server - - Broken pipe from ('127.0.0.1', 52619)
[2025-05-14 12:48:11,036] - INFO - django.server - - Broken pipe from ('127.0.0.1', 52620)
[2025-05-14 12:48:11,281] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:48:11,371] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:48:11,397] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:48:11,872] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:48:11,968] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:48:12,075] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:13,387] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:13,480] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:13,619] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:48:14,137] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:48:14,217] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:48:14,296] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:48:15,493] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:48:15,587] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:48:15,735] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:16,246] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:16,328] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:16,495] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:48:17,728] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:48:17,795] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:48:17,873] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:48:18,342] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:48:18,424] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:18,609] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:19,821] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:20,089] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:48:20,216] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:48:20,449] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:48:20,518] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:48:20,694] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:21,934] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:22,260] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:22,422] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:48:22,639] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:48:22,660] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:48:22,831] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:24,050] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:24,396] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:24,663] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:48:24,868] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:48:24,949] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:26,167] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:26,503] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:26,858] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:48:27,068] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:48:28,305] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:28,602] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:29,093] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:48:29,272] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:48:30,700] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:48:31,347] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:48:31,474] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:48:33,531] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:48:33,705] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:48:35,929] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:49:56,741] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:49:56,743] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:49:56,845] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:49:56,960] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 9244
[2025-05-14 12:49:57,531] - INFO - django.server - - Broken pipe from ('127.0.0.1', 55125)
[2025-05-14 12:49:58,299] - INFO - django.server - - Broken pipe from ('127.0.0.1', 55128)
[2025-05-14 12:49:58,481] - INFO - django.server - - Broken pipe from ('127.0.0.1', 52631)
[2025-05-14 12:49:59,028] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:49:59,069] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:49:59,097] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:49:59,114] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:49:59,146] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:49:59,180] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:50:01,133] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:01,219] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:01,272] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:50:01,293] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:01,341] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:50:01,356] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:50:03,360] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:50:03,389] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:50:03,431] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:03,453] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:03,454] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:03,478] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:50:05,555] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:50:05,571] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:05,576] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:05,668] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:50:05,709] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:50:05,744] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:50:07,690] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:07,690] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:50:07,690] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:07,770] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:07,894] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:50:07,938] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:50:09,811] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:50:09,835] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:09,835] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:50:09,862] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:10,106] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:50:10,163] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:50:11,949] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:11,949] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:50:11,969] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:12,306] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:50:12,389] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:50:14,070] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:14,070] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:14,684] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:50:14,765] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:50:16,200] - INFO - django.server - - Broken pipe from ('127.0.0.1', 55123)
[2025-05-14 12:50:16,210] - INFO - django.server - - Broken pipe from ('127.0.0.1', 52576)
[2025-05-14 12:50:17,028] - INFO - django.server - - Broken pipe from ('127.0.0.1', 56666)
[2025-05-14 12:50:17,080] - INFO - django.server - - Broken pipe from ('127.0.0.1', 56671)
[2025-05-14 12:50:18,524] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:50:18,527] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:50:18,603] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:50:19,001] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 10564
[2025-05-14 12:50:19,200] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57004)
[2025-05-14 12:50:20,330] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57022)
[2025-05-14 12:50:20,498] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57028)
[2025-05-14 12:50:20,789] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:50:20,812] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:50:20,850] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:50:20,970] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:50:20,970] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:50:21,081] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:22,902] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:23,041] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:50:23,069] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:23,082] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:50:23,089] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:50:23,283] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:50:25,016] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:50:25,186] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:25,210] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:25,282] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:50:25,323] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:50:25,386] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:27,321] - INFO - django.server - - Broken pipe from ('127.0.0.1', 52578)
[2025-05-14 12:50:27,344] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57042)
[2025-05-14 12:50:27,344] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57039)
[2025-05-14 12:50:27,437] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57041)
[2025-05-14 12:50:27,610] - INFO - django.server - - Broken pipe from ('127.0.0.1', 56669)
[2025-05-14 12:50:27,673] - INFO - django.server - - Broken pipe from ('127.0.0.1', 56997)
[2025-05-14 12:50:30,880] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:50:30,884] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:50:31,009] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:50:31,210] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 7755
[2025-05-14 12:50:31,319] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57217)
[2025-05-14 12:50:32,083] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57218)
[2025-05-14 12:50:32,211] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57219)
[2025-05-14 12:50:33,019] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:50:33,030] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:50:33,066] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:50:33,112] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:50:33,162] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:50:33,335] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:35,118] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:35,132] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:35,263] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:50:35,319] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:50:35,389] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:50:35,530] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:50:37,224] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:50:37,254] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:50:37,386] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:37,479] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:37,509] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:37,723] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:50:39,457] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:50:39,479] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:50:39,494] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:50:39,586] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:50:39,625] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:39,809] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:41,620] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:41,718] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:50:41,749] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:50:41,777] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:50:41,782] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:50:41,936] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:43,753] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:43,827] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:43,862] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:50:44,100] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:44,119] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:50:44,130] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:50:45,848] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:45,951] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:46,206] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:46,361] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:50:46,388] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:50:47,948] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:48,051] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:48,979] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57215)
[2025-05-14 12:50:49,107] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57216)
[2025-05-14 12:50:50,070] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57276)
[2025-05-14 12:50:50,163] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57279)
[2025-05-14 12:50:51,655] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:50:51,656] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:50:51,796] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:50:52,141] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 9647
[2025-05-14 12:50:52,308] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57629)
[2025-05-14 12:50:52,938] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57639)
[2025-05-14 12:50:53,067] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57646)
[2025-05-14 12:50:53,400] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:50:53,426] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:50:53,452] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:50:53,789] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:50:53,890] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:50:54,269] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:55,510] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:55,544] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:55,714] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:50:56,048] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:50:56,158] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:50:56,533] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:50:57,618] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:50:57,678] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:50:57,824] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:58,161] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:58,295] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:50:58,804] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:50:59,863] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:50:59,922] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:50:59,946] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:51:00,278] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:51:00,401] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:00,924] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:01,976] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:02,213] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:51:02,223] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:51:02,408] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:51:02,542] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:51:03,037] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:04,098] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:04,320] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:04,451] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:51:04,632] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:51:04,654] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:51:05,170] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:06,220] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:06,431] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:06,696] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:51:06,908] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:51:07,268] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:08,318] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:08,517] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:08,906] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:51:09,132] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:51:10,467] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:10,636] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:11,132] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:51:11,375] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:51:12,771] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:13,404] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:51:13,614] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:51:15,638] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:51:15,851] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:51:18,214] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:51:22,969] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:51:22,974] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:51:23,103] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:51:23,202] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 10441
[2025-05-14 12:51:23,359] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57655)
[2025-05-14 12:51:23,904] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=1 HTTP/1.1" 200 953
[2025-05-14 12:51:23,918] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=1 HTTP/1.1" 200 1056
[2025-05-14 12:51:24,974] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=1 HTTP/1.1" 200 1036
[2025-05-14 12:51:25,159] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:51:25,366] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:51:25,445] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:51:26,015] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:51:26,036] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:51:27,083] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:27,267] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:27,488] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57620)
[2025-05-14 12:51:27,600] - INFO - django.server - "OPTIONS /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,617] - INFO - django.server - "OPTIONS /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,617] - INFO - django.server - "OPTIONS /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,622] - INFO - django.server - "OPTIONS /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,625] - INFO - django.server - "OPTIONS /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,638] - INFO - django.server - "OPTIONS /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,647] - INFO - django.server - "OPTIONS /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,648] - INFO - django.server - "OPTIONS /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,648] - INFO - django.server - "OPTIONS /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,658] - INFO - django.server - "OPTIONS /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,659] - INFO - django.server - "OPTIONS /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,659] - INFO - django.server - "OPTIONS /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,666] - INFO - django.server - "OPTIONS /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,668] - INFO - django.server - "OPTIONS /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,672] - INFO - django.server - "OPTIONS /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,679] - INFO - django.server - "OPTIONS /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,688] - INFO - django.server - "OPTIONS /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,699] - INFO - django.server - "OPTIONS /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,699] - INFO - django.server - "OPTIONS /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,700] - INFO - django.server - "OPTIONS /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,713] - INFO - django.server - "OPTIONS /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,724] - INFO - django.server - "OPTIONS /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,733] - INFO - django.server - "OPTIONS /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,739] - INFO - django.server - "OPTIONS /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,740] - INFO - django.server - "OPTIONS /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,744] - INFO - django.server - "OPTIONS /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,755] - INFO - django.server - "OPTIONS /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,757] - INFO - django.server - "OPTIONS /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:27,884] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57652)
[2025-05-14 12:51:28,143] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57657)
[2025-05-14 12:51:28,327] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57212)
[2025-05-14 12:51:28,566] - INFO - django.server - "OPTIONS /api/v1/products/categories/ HTTP/1.1" 200 0
[2025-05-14 12:51:28,566] - INFO - django.server - "OPTIONS /api/v1/products/feature/products/ HTTP/1.1" 200 0
[2025-05-14 12:51:28,569] - INFO - django.server - "OPTIONS /api/v1/products/feature/products/?random=true HTTP/1.1" 200 0
[2025-05-14 12:51:28,582] - INFO - django.server - "GET /api/v1/products/categories/ HTTP/1.1" 200 5001
[2025-05-14 12:51:28,752] - INFO - django.server - "OPTIONS /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:28,758] - INFO - django.server - "OPTIONS /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:29,352] - INFO - django.server - - Broken pipe from ('127.0.0.1', 58158)
[2025-05-14 12:51:29,362] - INFO - django.server - - Broken pipe from ('127.0.0.1', 57284)
[2025-05-14 12:51:30,655] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:51:30,656] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:51:30,659] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,666] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,673] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,678] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,684] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,688] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,705] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,747] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,756] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,766] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,775] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,783] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,794] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,800] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,807] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,813] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,822] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,822] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:51:30,827] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,827] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,832] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,836] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,838] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,840] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,844] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,844] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,851] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,851] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,857] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,857] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,862] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,868] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,868] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,874] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,878] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,884] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,885] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,891] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,893] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,899] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,899] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,905] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,907] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,915] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,915] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,923] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,925] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,932] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,932] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,936] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,938] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 8895
[2025-05-14 12:51:30,942] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,945] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,951] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,953] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,953] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,959] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,961] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,962] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,966] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,966] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,967] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,972] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,972] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,977] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,977] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,978] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,979] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,984] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,985] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,987] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,990] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,991] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,992] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,995] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:30,997] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,000] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,003] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,006] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,007] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,010] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,011] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,013] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,015] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,019] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,019] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,019] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,024] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,024] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,024] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,025] - INFO - django.server - - Broken pipe from ('127.0.0.1', 58257)
[2025-05-14 12:51:31,028] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,028] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,028] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,031] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,031] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,032] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,035] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,035] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,035] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,038] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,038] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,039] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,043] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,043] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,043] - INFO - django.server - "OPTIONS /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 0
[2025-05-14 12:51:31,642] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=1 HTTP/1.1" 200 1056
[2025-05-14 12:51:31,694] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=1 HTTP/1.1" 200 953
[2025-05-14 12:51:32,344] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=1 HTTP/1.1" 200 1036
[2025-05-14 12:51:33,194] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:51:33,245] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:51:33,300] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:51:33,740] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:51:33,814] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:34,447] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:35,315] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:35,489] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:51:35,596] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:51:35,955] - INFO - django.server - "GET /api/v1/products/categories/screws/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:51:35,985] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:51:36,541] - INFO - django.server - "GET /api/v1/products/categories/hinges/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:51:37,434] - INFO - django.server - "GET /api/v1/products/categories/tower-bolt/products/?page_size=1 HTTP/1.1" 200 185
[2025-05-14 12:51:37,639] - INFO - django.server - "GET /api/v1/products/categories/door-kits/products/?page_size=1 HTTP/1.1" 200 183
[2025-05-14 12:51:37,722] - INFO - django.server - "GET /api/v1/products/categories/handles/products/?page_size=1 HTTP/1.1" 200 179
[2025-05-14 12:51:38,084] - INFO - django.server - "GET /api/v1/products/categories/t-channel/products/?page_size=1 HTTP/1.1" 200 203
[2025-05-14 12:51:38,091] - INFO - django.server - "GET /api/v1/products/categories/tendom/products/?page_size=1 HTTP/1.1" 200 198
[2025-05-14 12:51:38,658] - INFO - django.server - "GET /api/v1/products/categories/tendom-box/products/?page_size=1 HTTP/1.1" 200 208
[2025-05-14 12:51:39,560] - INFO - django.server - "GET /api/v1/products/categories/bottle-pullout/products/?page_size=1 HTTP/1.1" 200 216
[2025-05-14 12:51:39,788] - INFO - django.server - "GET /api/v1/products/categories/majic-carnes/products/?page_size=1 HTTP/1.1" 200 212
[2025-05-14 12:51:39,852] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:51:40,207] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:40,214] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:51:40,768] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:41,674] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:42,007] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:51:42,070] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:51:42,421] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:51:42,429] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:51:42,890] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=1 HTTP/1.1" 200 142
[2025-05-14 12:51:43,781] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=1 HTTP/1.1" 200 132
[2025-05-14 12:51:44,121] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:51:44,211] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:51:44,527] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:44,538] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:45,011] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:45,972] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:51:46,312] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:51:46,368] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:51:46,680] - INFO - django.server - "GET /api/v1/products/categories/chimney/products/?page_size=1 HTTP/1.1" 200 200
[2025-05-14 12:51:46,757] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:51:47,128] - INFO - django.server - "GET /api/v1/products/categories/mov/products/?page_size=1 HTTP/1.1" 200 192
[2025-05-14 12:51:48,091] - INFO - django.server - "GET /api/v1/products/categories/fevicol-jivanjor/products/?page_size=1 HTTP/1.1" 200 208
[2025-05-14 12:51:48,420] - INFO - django.server - "GET /api/v1/products/categories/polymyts/products/?page_size=1 HTTP/1.1" 200 192
[2025-05-14 12:51:48,460] - INFO - django.server - "GET /api/v1/products/categories/silicon/products/?page_size=1 HTTP/1.1" 200 190
[2025-05-14 12:51:48,768] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:48,837] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:49,210] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:50,318] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:51:50,527] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:51:50,645] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:51:50,867] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:51:50,947] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:51:51,305] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:51:52,437] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=1 HTTP/1.1" 200 2210
[2025-05-14 12:51:52,626] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:52,742] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:53,039] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:51:53,126] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:53,506] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:51:54,538] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:51:54,738] - INFO - django.server - "GET /api/v1/products/categories/door-locks/products/?page_size=1 HTTP/1.1" 200 1368
[2025-05-14 12:51:54,834] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:55,246] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:55,256] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:51:55,700] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:51:56,655] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:56,875] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=1 HTTP/1.1" 200 3198
[2025-05-14 12:51:56,974] - INFO - django.server - "GET /api/v1/products/categories/solid-surface/products/?page_size=1 HTTP/1.1" 200 1392
[2025-05-14 12:51:57,391] - INFO - django.server - "GET /api/v1/products/categories/laminates/products/?page_size=1 HTTP/1.1" 200 124
[2025-05-14 12:51:57,418] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:57,922] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:51:58,907] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:51:59,004] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:51:59,515] - INFO - django.server - "GET /api/v1/products/categories/brillient/products/?page_size=1 HTTP/1.1" 200 185
[2025-05-14 12:51:59,515] - INFO - django.server - "GET /api/v1/products/categories/crystal/products/?page_size=1 HTTP/1.1" 200 181
[2025-05-14 12:51:59,532] - INFO - django.server - "GET /api/v1/products/categories/ace/products/?page_size=1 HTTP/1.1" 200 173
[2025-05-14 12:52:00,179] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:52:01,109] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:52:01,137] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:52:01,662] - INFO - django.server - "GET /api/v1/products/categories/car-security-cameras/products/?page_size=1 HTTP/1.1" 200 1301
[2025-05-14 12:52:01,662] - INFO - django.server - "GET /api/v1/products/categories/security-cameras/products/?page_size=1 HTTP/1.1" 200 1436
[2025-05-14 12:52:01,680] - INFO - django.server - "GET /api/v1/products/categories/air-purifiers/products/?page_size=1 HTTP/1.1" 200 1384
[2025-05-14 12:52:03,383] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:52:03,392] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:52:03,794] - INFO - django.server - "GET /api/v1/products/categories/security-cameras-accessories/products/?page_size=1 HTTP/1.1" 200 239
[2025-05-14 12:52:03,795] - INFO - django.server - "GET /api/v1/products/categories/video-doorbells/products/?page_size=1 HTTP/1.1" 200 1419
[2025-05-14 12:52:03,795] - INFO - django.server - "GET /api/v1/products/categories/car-security-cameras-accessories/products/?page_size=1 HTTP/1.1" 200 1410
[2025-05-14 12:52:03,828] - INFO - django.server - "GET /api/v1/products/categories/smart-cameras/products/?page_size=1 HTTP/1.1" 200 1426
[2025-05-14 12:52:05,512] - INFO - django.server - "GET /api/v1/products/categories/air-purifiers-accessories/products/?page_size=1 HTTP/1.1" 200 227
[2025-05-14 12:52:05,514] - INFO - django.server - "GET /api/v1/products/categories/bullet-cameras/products/?page_size=1 HTTP/1.1" 200 1685
[2025-05-14 12:52:06,030] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:52:07,640] - INFO - django.server - "GET /api/v1/products/categories/tyre-air-pumps/products/?page_size=1 HTTP/1.1" 200 134
[2025-05-14 12:52:07,671] - INFO - django.server - "GET /api/v1/products/categories/smart-switches/products/?page_size=1 HTTP/1.1" 200 1069
[2025-05-14 12:52:07,677] - INFO - django.server - "GET /api/v1/products/categories/wireless-chargers/products/?page_size=1 HTTP/1.1" 200 1630
[2025-05-14 12:52:09,763] - INFO - django.server - "GET /api/v1/products/categories/tyre-air-pumps-accessories/products/?page_size=1 HTTP/1.1" 200 231
[2025-05-14 12:52:09,792] - INFO - django.server - "GET /api/v1/products/categories/smart-switches-accessories/products/?page_size=1 HTTP/1.1" 200 1076
[2025-05-14 12:52:09,828] - INFO - django.server - "GET /api/v1/products/categories/smart-plugs/products/?page_size=1 HTTP/1.1" 200 1346
[2025-05-14 12:52:09,835] - INFO - django.server - "GET /api/v1/products/categories/smart-bulbs/products/?page_size=1 HTTP/1.1" 200 1260
[2025-05-14 12:52:09,842] - INFO - django.server - "GET /api/v1/products/categories/wireless-chargers-accessories/products/?page_size=1 HTTP/1.1" 200 1745
[2025-05-14 12:52:11,971] - INFO - django.server - "GET /api/v1/products/categories/smart-lighting/products/?page_size=1 HTTP/1.1" 200 134
[2025-05-14 12:52:11,998] - INFO - django.server - "GET /api/v1/products/categories/video-door-phones/products/?page_size=1 HTTP/1.1" 200 942
[2025-05-14 12:52:12,000] - INFO - django.server - "GET /api/v1/products/categories/smart-door-locks/products/?page_size=1 HTTP/1.1" 200 1332
[2025-05-14 12:52:14,081] - INFO - django.server - "GET /api/v1/products/categories/smart-lighting-accessories/products/?page_size=1 HTTP/1.1" 200 231
[2025-05-14 12:52:14,145] - INFO - django.server - "GET /api/v1/products/categories/smart-door-locks-accessories/products/?page_size=1 HTTP/1.1" 200 239
[2025-05-14 12:52:14,161] - INFO - django.server - "GET /api/v1/products/categories/video-door-phones-accessories/products/?page_size=1 HTTP/1.1" 200 1045
[2025-05-14 12:52:16,289] - INFO - django.server - "GET /api/v1/products/categories/gps-navigation-devices/products/?page_size=1 HTTP/1.1" 200 1626
[2025-05-14 12:52:16,289] - INFO - django.server - "GET /api/v1/products/categories/gimbals/products/?page_size=1 HTTP/1.1" 200 1268
[2025-05-14 12:52:16,292] - INFO - django.server - "GET /api/v1/products/categories/mobile-cables/products/?page_size=1 HTTP/1.1" 200 132
[2025-05-14 12:52:18,408] - INFO - django.server - "GET /api/v1/products/categories/mobile-cables-accessories/products/?page_size=1 HTTP/1.1" 200 227
[2025-05-14 12:52:18,429] - INFO - django.server - "GET /api/v1/products/categories/gps-navigation-devices-accessories/products/?page_size=1 HTTP/1.1" 200 1751
[2025-05-14 12:52:18,432] - INFO - django.server - "GET /api/v1/products/categories/gimbals-accessories/products/?page_size=1 HTTP/1.1" 200 1351
[2025-05-14 12:52:20,548] - INFO - django.server - "GET /api/v1/products/categories/led-lights/products/?page_size=1 HTTP/1.1" 200 126
[2025-05-14 12:52:20,549] - INFO - django.server - "GET /api/v1/products/categories/power-banks/products/?page_size=1 HTTP/1.1" 200 128
[2025-05-14 12:52:20,560] - INFO - django.server - "GET /api/v1/products/categories/baby-monitor/products/?page_size=1 HTTP/1.1" 200 130
[2025-05-14 12:52:22,646] - INFO - django.server - "GET /api/v1/products/categories/power-banks-accessories/products/?page_size=1 HTTP/1.1" 200 219
[2025-05-14 12:52:22,648] - INFO - django.server - "GET /api/v1/products/categories/led-lights-accessories/products/?page_size=1 HTTP/1.1" 200 215
[2025-05-14 12:52:22,670] - INFO - django.server - "GET /api/v1/products/categories/baby-monitor-accessories/products/?page_size=1 HTTP/1.1" 200 223
[2025-05-14 12:52:24,799] - INFO - django.server - "GET /api/v1/products/categories/study-lamps/products/?page_size=1 HTTP/1.1" 200 656
[2025-05-14 12:52:24,799] - INFO - django.server - "GET /api/v1/products/categories/egg-cookers/products/?page_size=1 HTTP/1.1" 200 128
[2025-05-14 12:52:24,799] - INFO - django.server - "GET /api/v1/products/categories/wall-lamps/products/?page_size=1 HTTP/1.1" 200 704
[2025-05-14 12:52:26,949] - INFO - django.server - "GET /api/v1/products/categories/wall-lamps-accessories/products/?page_size=1 HTTP/1.1" 200 793
[2025-05-14 12:52:26,949] - INFO - django.server - "GET /api/v1/products/categories/study-lamps-accessories/products/?page_size=1 HTTP/1.1" 200 747
[2025-05-14 12:52:29,080] - INFO - django.server - "GET /api/v1/products/categories/smart-home-devices/products/?page_size=1 HTTP/1.1" 200 1049
[2025-05-14 12:52:31,208] - INFO - django.server - "GET /api/v1/products/categories/home-tabs/products/?page_size=1 HTTP/1.1" 200 1112
[2025-05-14 12:53:20,443] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:53:20,444] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:53:20,595] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:53:20,697] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 7526
[2025-05-14 12:53:20,981] - INFO - django.server - - Broken pipe from ('127.0.0.1', 58254)
[2025-05-14 12:53:21,561] - INFO - django.server - - Broken pipe from ('127.0.0.1', 58255)
[2025-05-14 12:53:21,680] - INFO - django.server - - Broken pipe from ('127.0.0.1', 58256)
[2025-05-14 12:53:22,166] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60128)
[2025-05-14 12:53:22,221] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60131)
[2025-05-14 12:53:22,257] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60133)
[2025-05-14 12:53:22,665] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:53:22,756] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:53:22,765] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:53:22,818] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:22,828] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:53:22,846] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:53:24,787] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:24,867] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:24,945] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:53:24,958] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:24,986] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:53:25,027] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:53:27,043] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:53:27,072] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:27,072] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:53:27,104] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:27,139] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:27,147] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:53:29,203] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60147)
[2025-05-14 12:53:29,245] - INFO - django.server - - Broken pipe from ('127.0.0.1', 58258)
[2025-05-14 12:53:29,257] - INFO - django.server - - Broken pipe from ('127.0.0.1', 58309)
[2025-05-14 12:53:29,428] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60152)
[2025-05-14 12:53:29,445] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60149)
[2025-05-14 12:53:29,519] - INFO - django.server - - Broken pipe from ('127.0.0.1', 58259)
[2025-05-14 12:53:32,695] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:53:32,696] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:53:32,810] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:53:32,939] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 9241
[2025-05-14 12:53:33,300] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60315)
[2025-05-14 12:53:34,085] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60317)
[2025-05-14 12:53:34,274] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60318)
[2025-05-14 12:53:34,921] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:53:34,925] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:53:34,961] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:53:35,064] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:35,104] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:53:35,124] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:53:37,031] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:37,053] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:37,231] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:37,234] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:53:37,275] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:53:37,294] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:53:39,246] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:53:39,246] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:53:39,341] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:39,362] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:53:39,384] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:39,389] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:41,454] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:53:41,454] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:53:41,491] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:41,521] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:53:41,535] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:53:41,591] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:53:43,582] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:43,653] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:43,674] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:43,714] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:53:43,715] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:53:43,750] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:53:45,692] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:53:45,782] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:45,794] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:45,849] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:46,032] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:53:46,045] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:53:47,811] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60313)
[2025-05-14 12:53:47,900] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60314)
[2025-05-14 12:53:47,965] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60387)
[2025-05-14 12:53:48,339] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60378)
[2025-05-14 12:53:48,350] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60383)
[2025-05-14 12:53:49,815] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:53:49,816] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:53:50,202] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:53:50,271] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 7160
[2025-05-14 12:53:50,536] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60674)
[2025-05-14 12:53:51,497] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60690)
[2025-05-14 12:53:51,696] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60694)
[2025-05-14 12:53:52,029] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:53:52,048] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:53:52,090] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:53:52,141] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:53:52,329] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:53:52,403] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:54,153] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:54,173] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:54,305] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:53:54,354] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:53:54,609] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:53:54,674] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:53:56,258] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:53:56,286] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:53:56,410] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:56,451] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:56,697] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:56,889] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:53:58,568] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:53:58,571] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:53:58,577] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:53:58,588] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:53:58,815] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:53:59,018] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:00,695] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:54:00,695] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:00,811] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:54:00,831] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:54:00,906] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:54:01,122] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:02,794] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:02,794] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:03,013] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:54:03,051] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:54:03,086] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:54:03,221] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:04,900] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:04,921] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:05,266] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:54:05,318] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:54:05,338] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:07,005] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:07,057] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:07,560] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:54:07,603] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:54:09,114] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:09,163] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:09,973] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60704)
[2025-05-14 12:54:10,046] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60706)
[2025-05-14 12:54:11,259] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60710)
[2025-05-14 12:54:12,264] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60664)
[2025-05-14 12:54:13,031] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:54:13,035] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60663)
[2025-05-14 12:54:13,208] - INFO - django.server - - Broken pipe from ('127.0.0.1', 60312)
[2025-05-14 12:54:13,639] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61071)
[2025-05-14 12:54:13,767] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61076)
[2025-05-14 12:54:14,459] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61088)
[2025-05-14 12:54:14,799] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61094)
[2025-05-14 12:54:15,064] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61101)
[2025-05-14 12:54:15,092] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61099)
[2025-05-14 12:54:15,739] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:54:15,740] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:54:15,832] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:54:15,903] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 9999
[2025-05-14 12:54:16,341] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61144)
[2025-05-14 12:54:17,137] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61158)
[2025-05-14 12:54:17,230] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61162)
[2025-05-14 12:54:17,443] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:54:17,533] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:54:17,633] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:54:17,855] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:54:17,934] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:54:18,029] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:19,582] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:19,669] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:19,929] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:54:20,140] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:54:20,205] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:54:20,291] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:54:21,680] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:54:21,800] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:54:22,027] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:22,257] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:22,332] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:22,502] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:54:23,896] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:54:24,008] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:54:24,130] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:54:24,376] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:54:24,477] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:24,622] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:25,982] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:26,224] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:54:26,373] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:54:26,488] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:54:26,567] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:54:26,718] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:28,082] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61170)
[2025-05-14 12:54:28,321] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61172)
[2025-05-14 12:54:28,676] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61116)
[2025-05-14 12:54:28,701] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61174)
[2025-05-14 12:54:28,801] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61115)
[2025-05-14 12:54:28,835] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61117)
[2025-05-14 12:54:31,247] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:54:31,248] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:54:31,617] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:54:31,687] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 11751
[2025-05-14 12:54:31,993] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61439)
[2025-05-14 12:54:32,843] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61452)
[2025-05-14 12:54:33,233] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61461)
[2025-05-14 12:54:33,325] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:54:33,485] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:54:33,499] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:54:33,576] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:54:33,717] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:54:33,791] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:35,438] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:35,583] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:35,701] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:54:35,787] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:54:35,927] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:54:36,012] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:54:37,545] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:54:37,667] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:54:37,815] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:37,916] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61473)
[2025-05-14 12:54:38,026] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61428)
[2025-05-14 12:54:38,343] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61429)
[2025-05-14 12:54:39,886] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61466)
[2025-05-14 12:54:39,939] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61398)
[2025-05-14 12:54:40,037] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61476)
[2025-05-14 12:54:41,390] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:54:41,391] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:54:41,529] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:54:41,654] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 10318
[2025-05-14 12:54:41,891] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61603)
[2025-05-14 12:54:42,799] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61604)
[2025-05-14 12:54:43,277] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61605)
[2025-05-14 12:54:43,592] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:54:43,650] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:54:43,699] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:54:43,761] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:54:43,761] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:54:43,777] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:45,713] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:45,751] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:45,918] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:54:46,097] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:54:46,160] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:54:46,172] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:54:47,876] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:54:48,034] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:48,050] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:54:48,221] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:48,272] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:48,545] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:54:49,670] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:54:49,671] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:54:49,742] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:54:49,818] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 7113
[2025-05-14 12:54:49,940] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61809)
[2025-05-14 12:54:50,046] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=1 HTTP/1.1" 200 1056
[2025-05-14 12:54:50,127] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:54:50,171] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:50,171] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:54:50,373] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=1 HTTP/1.1" 200 953
[2025-05-14 12:54:50,386] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:54:50,478] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:54:50,653] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:51,068] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=1 HTTP/1.1" 200 1036
[2025-05-14 12:54:52,027] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:54:52,182] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:54:52,209] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:54:52,283] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:52,318] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:54:52,580] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:54:52,582] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:52,624] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:54:52,650] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:54:52,673] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:54:52,806] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:54:53,204] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:54,179] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:54,389] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:54,446] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:54,766] - INFO - django.server - "GET /api/v1/products/categories/screws/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:54:54,792] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:54,808] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:54:54,898] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:54:55,167] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:54:55,192] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:54:55,328] - INFO - django.server - "GET /api/v1/products/categories/hinges/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:54:55,362] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:54:55,362] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:54:56,335] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61801)
[2025-05-14 12:54:56,536] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61802)
[2025-05-14 12:54:56,590] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61601)
[2025-05-14 12:54:56,926] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61828)
[2025-05-14 12:54:56,959] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61814)
[2025-05-14 12:54:56,973] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61665)
[2025-05-14 12:54:57,076] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61803)
[2025-05-14 12:54:57,337] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61600)
[2025-05-14 12:54:57,509] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61844)
[2025-05-14 12:54:57,847] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61602)
[2025-05-14 12:54:57,935] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61667)
[2025-05-14 12:54:58,008] - INFO - django.server - - Broken pipe from ('127.0.0.1', 61666)
[2025-05-14 12:55:00,497] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:55:00,504] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:55:00,709] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:55:01,085] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:55:01,141] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 9992
[2025-05-14 12:55:01,944] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=1 HTTP/1.1" 200 953
[2025-05-14 12:55:01,960] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=1 HTTP/1.1" 200 1056
[2025-05-14 12:55:02,683] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=1 HTTP/1.1" 200 1036
[2025-05-14 12:55:03,072] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62022)
[2025-05-14 12:55:03,291] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62023)
[2025-05-14 12:55:03,439] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62024)
[2025-05-14 12:55:04,162] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:55:04,207] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:55:04,287] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:55:04,381] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:55:04,427] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:55:04,789] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:05,218] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:55:05,223] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:55:05,315] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:55:05,407] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 8731
[2025-05-14 12:55:05,603] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62184)
[2025-05-14 12:55:06,278] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:06,319] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:06,578] - INFO - django.server - "GET /api/v1/products/categories/t-channel/products/?page_size=1 HTTP/1.1" 200 203
[2025-05-14 12:55:06,617] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:55:06,683] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:55:06,710] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62188)
[2025-05-14 12:55:06,923] - INFO - django.server - "GET /api/v1/products/categories/tendom/products/?page_size=1 HTTP/1.1" 200 198
[2025-05-14 12:55:07,095] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62223)
[2025-05-14 12:55:07,501] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:55:07,506] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:55:07,530] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:55:07,591] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:07,639] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:55:07,644] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:55:08,408] - INFO - django.server - "GET /api/v1/products/categories/screws/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:55:08,449] - INFO - django.server - "GET /api/v1/products/categories/tower-bolt/products/?page_size=1 HTTP/1.1" 200 185
[2025-05-14 12:55:08,696] - INFO - django.server - "GET /api/v1/products/categories/hinges/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:55:08,740] - INFO - django.server - "GET /api/v1/products/categories/door-kits/products/?page_size=1 HTTP/1.1" 200 183
[2025-05-14 12:55:08,821] - INFO - django.server - "GET /api/v1/products/categories/handles/products/?page_size=1 HTTP/1.1" 200 179
[2025-05-14 12:55:09,044] - INFO - django.server - "GET /api/v1/products/categories/tendom-box/products/?page_size=1 HTTP/1.1" 200 208
[2025-05-14 12:55:09,625] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:09,658] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:09,771] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:55:09,777] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:09,837] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:55:09,884] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:55:10,566] - INFO - django.server - "GET /api/v1/products/categories/bottle-pullout/products/?page_size=1 HTTP/1.1" 200 216
[2025-05-14 12:55:10,608] - INFO - django.server - "GET /api/v1/products/categories/majic-carnes/products/?page_size=1 HTTP/1.1" 200 212
[2025-05-14 12:55:10,801] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:55:10,983] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:55:11,101] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:55:11,160] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:55:11,906] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:11,916] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:55:11,931] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:55:11,940] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:55:11,984] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:12,019] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:12,674] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:12,725] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:12,916] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:13,288] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=1 HTTP/1.1" 200 142
[2025-05-14 12:55:13,304] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:55:13,453] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:55:14,093] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:55:14,099] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:14,166] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:55:14,337] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:55:14,368] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:55:14,410] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:55:14,792] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62141)
[2025-05-14 12:55:14,842] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62025)
[2025-05-14 12:55:15,289] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62026)
[2025-05-14 12:55:15,395] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62021)
[2025-05-14 12:55:15,425] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62137)
[2025-05-14 12:55:15,618] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62139)
[2025-05-14 12:55:16,218] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62236)
[2025-05-14 12:55:16,218] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62241)
[2025-05-14 12:55:16,310] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62175)
[2025-05-14 12:55:16,468] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62174)
[2025-05-14 12:55:16,756] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62172)
[2025-05-14 12:55:16,791] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62245)
[2025-05-14 12:55:21,399] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:55:21,400] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:55:21,520] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:55:21,644] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 8983
[2025-05-14 12:55:22,067] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:55:22,070] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:55:22,156] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:55:22,258] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:55:22,398] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 9184
[2025-05-14 12:55:22,480] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:55:23,837] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62594)
[2025-05-14 12:55:24,217] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62577)
[2025-05-14 12:55:24,240] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62616)
[2025-05-14 12:55:24,377] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=1 HTTP/1.1" 200 953
[2025-05-14 12:55:24,393] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=1 HTTP/1.1" 200 1056
[2025-05-14 12:55:24,395] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=1 HTTP/1.1" 200 1036
[2025-05-14 12:55:24,457] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62579)
[2025-05-14 12:55:24,462] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:55:24,501] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62580)
[2025-05-14 12:55:24,614] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62589)
[2025-05-14 12:55:24,806] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:55:25,221] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:55:25,229] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:55:25,290] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:25,295] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:55:25,384] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62653)
[2025-05-14 12:55:25,470] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:55:25,498] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:55:26,515] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62488)
[2025-05-14 12:55:26,542] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62489)
[2025-05-14 12:55:26,546] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62492)
[2025-05-14 12:55:26,593] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62490)
[2025-05-14 12:55:26,936] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62639)
[2025-05-14 12:55:27,354] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:27,385] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:27,662] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:55:27,662] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:55:27,702] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:55:27,745] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:27,805] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:55:27,827] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:27,983] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62728)
[2025-05-14 12:55:28,040] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:55:28,129] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62671)
[2025-05-14 12:55:28,135] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62655)
[2025-05-14 12:55:28,137] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:55:29,795] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62663)
[2025-05-14 12:55:29,809] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62669)
[2025-05-14 12:55:29,905] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62745)
[2025-05-14 12:55:29,918] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62743)
[2025-05-14 12:55:29,918] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62660)
[2025-05-14 12:55:29,961] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62658)
[2025-05-14 12:55:30,167] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62734)
[2025-05-14 12:55:30,288] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62755)
[2025-05-14 12:55:30,304] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62732)
[2025-05-14 12:55:30,487] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62739)
[2025-05-14 12:55:31,079] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62832)
[2025-05-14 12:55:31,160] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62835)
[2025-05-14 12:55:31,313] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62842)
[2025-05-14 12:55:31,581] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62834)
[2025-05-14 12:55:31,602] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62833)
[2025-05-14 12:55:31,833] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62837)
[2025-05-14 12:55:32,917] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:55:32,933] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:55:33,028] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:55:33,156] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 7915
[2025-05-14 12:55:33,256] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62895)
[2025-05-14 12:55:33,859] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62896)
[2025-05-14 12:55:34,279] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62922)
[2025-05-14 12:55:34,587] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:55:34,661] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:55:34,680] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:55:35,062] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:55:35,155] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:55:35,277] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:36,685] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:36,779] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:37,024] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:55:37,542] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62888)
[2025-05-14 12:55:37,656] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62887)
[2025-05-14 12:55:37,720] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62886)
[2025-05-14 12:55:37,766] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:55:37,769] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:55:37,908] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:55:38,043] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 8891
[2025-05-14 12:55:38,180] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:55:38,820] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62930)
[2025-05-14 12:55:38,934] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62931)
[2025-05-14 12:55:39,154] - INFO - django.server - - Broken pipe from ('127.0.0.1', 62937)
[2025-05-14 12:55:39,717] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63057)
[2025-05-14 12:55:39,739] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63056)
[2025-05-14 12:55:40,016] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63058)
[2025-05-14 12:55:40,489] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:55:41,253] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=1 HTTP/1.1" 200 1056
[2025-05-14 12:55:41,277] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=1 HTTP/1.1" 200 1036
[2025-05-14 12:55:41,300] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=1 HTTP/1.1" 200 953
[2025-05-14 12:55:41,480] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:55:42,069] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:55:42,608] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:55:43,365] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63016)
[2025-05-14 12:55:43,431] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63008)
[2025-05-14 12:55:43,543] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63007)
[2025-05-14 12:55:43,592] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63019)
[2025-05-14 12:55:44,390] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63107)
[2025-05-14 12:55:44,681] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:55:44,692] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:55:44,790] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63009)
[2025-05-14 12:55:44,838] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:55:45,040] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 10959
[2025-05-14 12:55:45,324] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63208)
[2025-05-14 12:55:45,423] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63181)
[2025-05-14 12:55:45,500] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63192)
[2025-05-14 12:55:45,549] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63210)
[2025-05-14 12:55:45,552] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63204)
[2025-05-14 12:55:45,596] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63186)
[2025-05-14 12:55:45,655] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63183)
[2025-05-14 12:55:45,771] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63199)
[2025-05-14 12:55:45,876] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63201)
[2025-05-14 12:55:47,099] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63165)
[2025-05-14 12:55:47,141] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63164)
[2025-05-14 12:55:47,218] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63203)
[2025-05-14 12:55:47,477] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63166)
[2025-05-14 12:55:48,005] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:55:48,192] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:55:48,228] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:55:48,249] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:55:48,318] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:55:48,343] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:55:49,731] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:55:49,732] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:55:49,883] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:55:49,984] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 10477
[2025-05-14 12:55:50,144] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:55:50,232] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:55:50,302] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:50,351] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:50,385] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:50,669] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:55:50,681] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:55:51,453] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=1 HTTP/1.1" 200 1056
[2025-05-14 12:55:51,461] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=1 HTTP/1.1" 200 953
[2025-05-14 12:55:51,933] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=1 HTTP/1.1" 200 1036
[2025-05-14 12:55:52,191] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63231)
[2025-05-14 12:55:52,287] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63232)
[2025-05-14 12:55:52,351] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63233)
[2025-05-14 12:55:52,396] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:55:52,418] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:55:52,479] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:52,490] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:55:52,814] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:52,814] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:53,396] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63409)
[2025-05-14 12:55:53,473] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63412)
[2025-05-14 12:55:53,476] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:55:53,590] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:55:53,633] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:55:53,741] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:55:53,769] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:55:54,033] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:54,603] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:55:54,617] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:55:54,662] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:55:54,694] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:55:54,918] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:54,958] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:55,582] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:55,702] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:55,903] - INFO - django.server - "GET /api/v1/products/categories/screws/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:55:56,006] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:55:56,096] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:55:56,138] - INFO - django.server - "GET /api/v1/products/categories/hinges/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:55:56,712] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:56,754] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:55:57,074] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:55:57,076] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:55:57,084] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:55:57,099] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:57,712] - INFO - django.server - "GET /api/v1/products/categories/tower-bolt/products/?page_size=1 HTTP/1.1" 200 185
[2025-05-14 12:55:57,817] - INFO - django.server - "GET /api/v1/products/categories/door-kits/products/?page_size=1 HTTP/1.1" 200 183
[2025-05-14 12:55:58,069] - INFO - django.server - "GET /api/v1/products/categories/handles/products/?page_size=1 HTTP/1.1" 200 179
[2025-05-14 12:55:58,168] - INFO - django.server - "GET /api/v1/products/categories/t-channel/products/?page_size=1 HTTP/1.1" 200 203
[2025-05-14 12:55:58,225] - INFO - django.server - "GET /api/v1/products/categories/tendom/products/?page_size=1 HTTP/1.1" 200 198
[2025-05-14 12:55:58,264] - INFO - django.server - "GET /api/v1/products/categories/tendom-box/products/?page_size=1 HTTP/1.1" 200 208
[2025-05-14 12:55:58,840] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:58,870] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:59,221] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:55:59,247] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:55:59,410] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:55:59,423] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:55:59,816] - INFO - django.server - "GET /api/v1/products/categories/bottle-pullout/products/?page_size=1 HTTP/1.1" 200 216
[2025-05-14 12:55:59,910] - INFO - django.server - "GET /api/v1/products/categories/majic-carnes/products/?page_size=1 HTTP/1.1" 200 212
[2025-05-14 12:56:00,188] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:56:00,294] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:00,548] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:56:00,549] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:56:00,947] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:01,001] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:01,360] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:01,643] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:56:01,643] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:56:01,948] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:56:02,041] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:02,321] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:02,482] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:56:02,652] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=1 HTTP/1.1" 200 142
[2025-05-14 12:56:02,756] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:56:03,050] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:03,112] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:03,844] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:56:03,844] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:56:04,071] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=1 HTTP/1.1" 200 132
[2025-05-14 12:56:04,157] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:56:04,468] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:04,693] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:56:04,772] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:56:04,866] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:56:05,165] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:05,246] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:06,069] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:56:06,079] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:56:06,198] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:06,297] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:06,686] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:56:06,826] - INFO - django.server - "GET /api/v1/products/categories/chimney/products/?page_size=1 HTTP/1.1" 200 200
[2025-05-14 12:56:06,896] - INFO - django.server - "GET /api/v1/products/categories/mov/products/?page_size=1 HTTP/1.1" 200 192
[2025-05-14 12:56:07,081] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:56:07,369] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:08,332] - INFO - django.server - "GET /api/v1/products/categories/fevicol-jivanjor/products/?page_size=1 HTTP/1.1" 200 208
[2025-05-14 12:56:08,336] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:56:08,354] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:56:08,416] - INFO - django.server - "GET /api/v1/products/categories/polymyts/products/?page_size=1 HTTP/1.1" 200 192
[2025-05-14 12:56:08,813] - INFO - django.server - "GET /api/v1/products/categories/silicon/products/?page_size=1 HTTP/1.1" 200 190
[2025-05-14 12:56:08,924] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:56:08,999] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:56:09,194] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:10,433] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:10,549] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:10,558] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:56:10,558] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:56:11,030] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:56:11,107] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:56:11,143] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:56:11,300] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:56:12,535] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=1 HTTP/1.1" 200 2210
[2025-05-14 12:56:12,644] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:56:12,753] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:56:13,124] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:13,194] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:13,254] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:13,498] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:56:14,741] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:56:14,758] - INFO - django.server - "GET /api/v1/products/categories/door-locks/products/?page_size=1 HTTP/1.1" 200 1368
[2025-05-14 12:56:15,208] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:15,302] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:15,373] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:15,704] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:56:16,872] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=1 HTTP/1.1" 200 3198
[2025-05-14 12:56:16,965] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:56:17,342] - INFO - django.server - "GET /api/v1/products/categories/solid-surface/products/?page_size=1 HTTP/1.1" 200 1392
[2025-05-14 12:56:17,396] - INFO - django.server - "GET /api/v1/products/categories/laminates/products/?page_size=1 HTTP/1.1" 200 124
[2025-05-14 12:56:17,448] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:17,807] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:19,087] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:56:19,220] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:56:19,504] - INFO - django.server - "GET /api/v1/products/categories/brillient/products/?page_size=1 HTTP/1.1" 200 185
[2025-05-14 12:56:19,524] - INFO - django.server - "GET /api/v1/products/categories/crystal/products/?page_size=1 HTTP/1.1" 200 181
[2025-05-14 12:56:19,544] - INFO - django.server - "GET /api/v1/products/categories/ace/products/?page_size=1 HTTP/1.1" 200 173
[2025-05-14 12:56:19,935] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:56:21,309] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:56:21,456] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:56:21,684] - INFO - django.server - "GET /api/v1/products/categories/security-cameras/products/?page_size=1 HTTP/1.1" 200 1436
[2025-05-14 12:56:21,684] - INFO - django.server - "GET /api/v1/products/categories/car-security-cameras/products/?page_size=1 HTTP/1.1" 200 1301
[2025-05-14 12:56:21,684] - INFO - django.server - "GET /api/v1/products/categories/air-purifiers/products/?page_size=1 HTTP/1.1" 200 1384
[2025-05-14 12:56:23,517] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:56:23,689] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:56:23,813] - INFO - django.server - "GET /api/v1/products/categories/security-cameras-accessories/products/?page_size=1 HTTP/1.1" 200 239
[2025-05-14 12:56:23,833] - INFO - django.server - "GET /api/v1/products/categories/car-security-cameras-accessories/products/?page_size=1 HTTP/1.1" 200 1410
[2025-05-14 12:56:23,835] - INFO - django.server - "GET /api/v1/products/categories/video-doorbells/products/?page_size=1 HTTP/1.1" 200 1419
[2025-05-14 12:56:23,835] - INFO - django.server - "GET /api/v1/products/categories/smart-cameras/products/?page_size=1 HTTP/1.1" 200 1426
[2025-05-14 12:56:25,649] - INFO - django.server - "GET /api/v1/products/categories/bullet-cameras/products/?page_size=1 HTTP/1.1" 200 1685
[2025-05-14 12:56:25,787] - INFO - django.server - "GET /api/v1/products/categories/air-purifiers-accessories/products/?page_size=1 HTTP/1.1" 200 227
[2025-05-14 12:56:26,025] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:56:27,901] - INFO - django.server - "GET /api/v1/products/categories/tyre-air-pumps/products/?page_size=1 HTTP/1.1" 200 134
[2025-05-14 12:56:27,921] - INFO - django.server - "GET /api/v1/products/categories/smart-switches/products/?page_size=1 HTTP/1.1" 200 1069
[2025-05-14 12:56:27,941] - INFO - django.server - "GET /api/v1/products/categories/wireless-chargers/products/?page_size=1 HTTP/1.1" 200 1630
[2025-05-14 12:56:30,006] - INFO - django.server - "GET /api/v1/products/categories/tyre-air-pumps-accessories/products/?page_size=1 HTTP/1.1" 200 231
[2025-05-14 12:56:30,051] - INFO - django.server - "GET /api/v1/products/categories/smart-plugs/products/?page_size=1 HTTP/1.1" 200 1346
[2025-05-14 12:56:30,053] - INFO - django.server - "GET /api/v1/products/categories/smart-switches-accessories/products/?page_size=1 HTTP/1.1" 200 1076
[2025-05-14 12:56:30,065] - INFO - django.server - "GET /api/v1/products/categories/smart-bulbs/products/?page_size=1 HTTP/1.1" 200 1260
[2025-05-14 12:56:30,074] - INFO - django.server - "GET /api/v1/products/categories/wireless-chargers-accessories/products/?page_size=1 HTTP/1.1" 200 1745
[2025-05-14 12:56:32,183] - INFO - django.server - "GET /api/v1/products/categories/smart-lighting/products/?page_size=1 HTTP/1.1" 200 134
[2025-05-14 12:56:32,190] - INFO - django.server - "GET /api/v1/products/categories/video-door-phones/products/?page_size=1 HTTP/1.1" 200 942
[2025-05-14 12:56:32,217] - INFO - django.server - "GET /api/v1/products/categories/smart-door-locks/products/?page_size=1 HTTP/1.1" 200 1332
[2025-05-14 12:56:34,319] - INFO - django.server - "GET /api/v1/products/categories/smart-lighting-accessories/products/?page_size=1 HTTP/1.1" 200 231
[2025-05-14 12:56:34,329] - INFO - django.server - "GET /api/v1/products/categories/smart-door-locks-accessories/products/?page_size=1 HTTP/1.1" 200 239
[2025-05-14 12:56:34,340] - INFO - django.server - "GET /api/v1/products/categories/video-door-phones-accessories/products/?page_size=1 HTTP/1.1" 200 1045
[2025-05-14 12:56:36,437] - INFO - django.server - "GET /api/v1/products/categories/mobile-cables/products/?page_size=1 HTTP/1.1" 200 132
[2025-05-14 12:56:36,468] - INFO - django.server - "GET /api/v1/products/categories/gps-navigation-devices/products/?page_size=1 HTTP/1.1" 200 1626
[2025-05-14 12:56:36,480] - INFO - django.server - "GET /api/v1/products/categories/gimbals/products/?page_size=1 HTTP/1.1" 200 1268
[2025-05-14 12:56:38,529] - INFO - django.server - "GET /api/v1/products/categories/mobile-cables-accessories/products/?page_size=1 HTTP/1.1" 200 227
[2025-05-14 12:56:38,577] - INFO - django.server - "GET /api/v1/products/categories/gps-navigation-devices-accessories/products/?page_size=1 HTTP/1.1" 200 1751
[2025-05-14 12:56:38,610] - INFO - django.server - "GET /api/v1/products/categories/gimbals-accessories/products/?page_size=1 HTTP/1.1" 200 1351
[2025-05-14 12:56:40,742] - INFO - django.server - "GET /api/v1/products/categories/power-banks/products/?page_size=1 HTTP/1.1" 200 128
[2025-05-14 12:56:40,759] - INFO - django.server - "GET /api/v1/products/categories/led-lights/products/?page_size=1 HTTP/1.1" 200 126
[2025-05-14 12:56:40,760] - INFO - django.server - "GET /api/v1/products/categories/baby-monitor/products/?page_size=1 HTTP/1.1" 200 130
[2025-05-14 12:56:42,865] - INFO - django.server - "GET /api/v1/products/categories/power-banks-accessories/products/?page_size=1 HTTP/1.1" 200 219
[2025-05-14 12:56:42,875] - INFO - django.server - "GET /api/v1/products/categories/led-lights-accessories/products/?page_size=1 HTTP/1.1" 200 215
[2025-05-14 12:56:42,907] - INFO - django.server - "GET /api/v1/products/categories/baby-monitor-accessories/products/?page_size=1 HTTP/1.1" 200 223
[2025-05-14 12:56:45,028] - INFO - django.server - "GET /api/v1/products/categories/study-lamps/products/?page_size=1 HTTP/1.1" 200 656
[2025-05-14 12:56:45,058] - INFO - django.server - "GET /api/v1/products/categories/egg-cookers/products/?page_size=1 HTTP/1.1" 200 128
[2025-05-14 12:56:45,062] - INFO - django.server - "GET /api/v1/products/categories/wall-lamps/products/?page_size=1 HTTP/1.1" 200 704
[2025-05-14 12:56:47,169] - INFO - django.server - "GET /api/v1/products/categories/study-lamps-accessories/products/?page_size=1 HTTP/1.1" 200 747
[2025-05-14 12:56:47,199] - INFO - django.server - "GET /api/v1/products/categories/wall-lamps-accessories/products/?page_size=1 HTTP/1.1" 200 793
[2025-05-14 12:56:49,349] - INFO - django.server - "GET /api/v1/products/categories/smart-home-devices/products/?page_size=1 HTTP/1.1" 200 1049
[2025-05-14 12:56:51,491] - INFO - django.server - "GET /api/v1/products/categories/home-tabs/products/?page_size=1 HTTP/1.1" 200 1112
[2025-05-14 12:56:58,451] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:56:58,473] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:56:58,647] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:56:58,741] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 9419
[2025-05-14 12:56:59,688] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:56:59,689] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:56:59,752] - INFO - django.server - "GET /api/v1/products/categories/ HTTP/1.1" 200 5001
[2025-05-14 12:56:59,895] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:56:59,901] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:57:00,078] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 8143
[2025-05-14 12:57:02,208] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63283)
[2025-05-14 12:57:02,654] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:57:03,001] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63298)
[2025-05-14 12:57:03,195] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63291)
[2025-05-14 12:57:03,301] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63296)
[2025-05-14 12:57:03,319] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63293)
[2025-05-14 12:57:03,337] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63281)
[2025-05-14 12:57:03,776] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64669)
[2025-05-14 12:57:03,828] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64671)
[2025-05-14 12:57:04,169] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64673)
[2025-05-14 12:57:04,238] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64676)
[2025-05-14 12:57:04,923] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:57:05,947] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:57:05,949] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:57:06,109] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:57:06,496] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 10097
[2025-05-14 12:57:06,868] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64740)
[2025-05-14 12:57:07,206] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:57:07,750] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64755)
[2025-05-14 12:57:07,892] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64760)
[2025-05-14 12:57:08,126] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:57:08,175] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:57:08,238] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:08,322] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:57:08,350] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:57:08,620] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:09,280] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:57:09,328] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:57:09,338] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:09,451] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:57:09,656] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:09,850] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:57:10,262] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:10,296] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:10,478] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:10,676] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64694)
[2025-05-14 12:57:10,714] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64770)
[2025-05-14 12:57:11,037] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64731)
[2025-05-14 12:57:11,403] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:11,457] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:11,640] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:57:11,676] - INFO - django.server - - Broken pipe from ('127.0.0.1', 63433)
[2025-05-14 12:57:11,771] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:12,184] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64818)
[2025-05-14 12:57:12,452] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64768)
[2025-05-14 12:57:12,603] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64778)
[2025-05-14 12:57:12,672] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64693)
[2025-05-14 12:57:13,524] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64801)
[2025-05-14 12:57:13,809] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64812)
[2025-05-14 12:57:13,907] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64822)
[2025-05-14 12:57:13,967] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64806)
[2025-05-14 12:57:14,069] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64952)
[2025-05-14 12:57:14,388] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:57:14,406] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:57:14,441] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:14,462] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:14,578] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:57:14,590] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:57:16,514] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:16,555] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:16,735] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:16,739] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:16,866] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:57:16,883] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:57:17,169] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:57:17,173] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:57:17,292] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:57:17,375] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 9154
[2025-05-14 12:57:17,518] - INFO - django.server - - Broken pipe from ('127.0.0.1', 65051)
[2025-05-14 12:57:18,468] - INFO - django.server - - Broken pipe from ('127.0.0.1', 65055)
[2025-05-14 12:57:18,717] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:57:18,785] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:57:18,842] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:57:18,863] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:18,979] - INFO - django.server - - Broken pipe from ('127.0.0.1', 65084)
[2025-05-14 12:57:18,997] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:19,096] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:57:19,246] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:57:19,320] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:57:19,367] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:57:19,376] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:57:19,398] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:19,498] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:20,835] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:20,989] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:57:21,014] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:57:21,062] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:57:21,118] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:21,194] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:21,372] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:21,454] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:21,654] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:57:21,654] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:57:21,664] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:57:21,757] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:57:22,959] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:23,182] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:57:23,234] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:23,245] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:57:23,257] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:57:23,298] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:23,471] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:57:23,562] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:23,746] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:23,778] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:23,790] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:23,981] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:57:25,085] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:25,340] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:25,396] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:25,450] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:25,907] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:57:25,978] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:57:26,020] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:57:26,051] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:26,219] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:26,244] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:26,293] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:57:26,294] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:57:27,312] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:57:27,350] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:57:27,660] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64968)
[2025-05-14 12:57:27,746] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:27,843] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64973)
[2025-05-14 12:57:28,076] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:28,348] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:28,428] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:57:28,520] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:28,871] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:28,930] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:28,889] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 7270
[2025-05-14 12:57:28,872] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:57:28,967] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:57:29,128] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:29,644] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:57:30,150] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:57:30,229] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:57:30,235] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:57:30,565] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:30,672] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:30,779] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64970)
[2025-05-14 12:57:31,516] - INFO - django.server - - Broken pipe from ('127.0.0.1', 65314)
[2025-05-14 12:57:31,800] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64956)
[2025-05-14 12:57:32,316] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:57:32,368] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:57:32,397] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:32,504] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:57:32,548] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:57:32,586] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:57:32,623] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:32,632] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:32,719] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:32,796] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:32,909] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:57:32,912] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:57:34,462] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:34,637] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:34,701] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:34,706] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:57:34,734] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:57:34,793] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:34,923] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:57:34,932] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:34,975] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:57:35,034] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:57:35,356] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:57:35,398] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:57:36,850] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 8598
[2025-05-14 12:57:36,884] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:37,152] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:57:37,176] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:57:37,181] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:37,181] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:37,199] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:37,239] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:57:37,256] - INFO - django.server - "GET /media/products/video-doorbell-pro_1.jpg HTTP/1.1" 200 66613
[2025-05-14 12:57:37,477] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:57:37,763] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:57:37,797] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:57:39,287] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:57:39,314] - INFO - django.server - - Broken pipe from ('127.0.0.1', 65391)
[2025-05-14 12:57:39,383] - INFO - django.server - - Broken pipe from ('127.0.0.1', 65387)
[2025-05-14 12:57:39,392] - INFO - django.server - - Broken pipe from ('127.0.0.1', 65098)
[2025-05-14 12:57:39,404] - INFO - django.server - - Broken pipe from ('127.0.0.1', 65389)
[2025-05-14 12:57:39,626] - INFO - django.server - - Broken pipe from ('127.0.0.1', 65313)
[2025-05-14 12:57:39,627] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64965)
[2025-05-14 12:57:40,151] - INFO - django.server - - Broken pipe from ('127.0.0.1', 65094)
[2025-05-14 12:57:40,163] - INFO - django.server - - Broken pipe from ('127.0.0.1', 65042)
[2025-05-14 12:57:40,386] - INFO - django.server - - Broken pipe from ('127.0.0.1', 65101)
[2025-05-14 12:57:41,229] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49224)
[2025-05-14 12:57:41,426] - INFO - django.server - - Broken pipe from ('127.0.0.1', 64975)
[2025-05-14 12:57:41,485] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49241)
[2025-05-14 12:57:41,602] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49230)
[2025-05-14 12:57:41,615] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49227)
[2025-05-14 12:57:41,732] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49235)
[2025-05-14 12:57:41,775] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49251)
[2025-05-14 12:57:41,803] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49255)
[2025-05-14 12:57:41,840] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49261)
[2025-05-14 12:57:41,925] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49263)
[2025-05-14 12:57:42,055] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49253)
[2025-05-14 12:57:42,055] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49254)
[2025-05-14 12:57:43,250] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:57:43,251] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:57:43,366] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:57:43,800] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 10909
[2025-05-14 12:57:44,957] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49334)
[2025-05-14 12:57:45,621] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:57:45,674] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:57:45,790] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:57:45,945] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 10514
[2025-05-14 12:57:46,218] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:57:46,236] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=1 HTTP/1.1" 200 1056
[2025-05-14 12:57:46,249] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=1 HTTP/1.1" 200 953
[2025-05-14 12:57:46,254] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=1 HTTP/1.1" 200 1036
[2025-05-14 12:57:46,652] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49369)
[2025-05-14 12:57:46,716] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49372)
[2025-05-14 12:57:47,122] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49385)
[2025-05-14 12:57:47,383] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49389)
[2025-05-14 12:57:47,392] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49391)
[2025-05-14 12:57:47,600] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:57:47,740] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:57:47,837] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:57:48,371] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:57:48,397] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:48,398] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:48,533] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49282)
[2025-05-14 12:57:49,142] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49702)
[2025-05-14 12:57:49,654] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49722)
[2025-05-14 12:57:49,722] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:49,728] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49729)
[2025-05-14 12:57:49,862] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:49,900] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49736)
[2025-05-14 12:57:50,061] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:57:50,090] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49741)
[2025-05-14 12:57:50,537] - INFO - django.server - "GET /api/v1/products/categories/hinges/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:57:50,537] - INFO - django.server - "GET /api/v1/products/categories/screws/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:57:50,634] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:57:50,886] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:57:50,900] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:57:50,933] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:50,972] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:51,006] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:57:51,033] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:57:51,834] - INFO - django.server - "GET /api/v1/products/categories/tower-bolt/products/?page_size=1 HTTP/1.1" 200 185
[2025-05-14 12:57:51,978] - INFO - django.server - "GET /api/v1/products/categories/door-kits/products/?page_size=1 HTTP/1.1" 200 183
[2025-05-14 12:57:52,170] - INFO - django.server - "GET /api/v1/products/categories/handles/products/?page_size=1 HTTP/1.1" 200 179
[2025-05-14 12:57:52,661] - INFO - django.server - "GET /api/v1/products/categories/t-channel/products/?page_size=1 HTTP/1.1" 200 203
[2025-05-14 12:57:52,687] - INFO - django.server - "GET /api/v1/products/categories/tendom/products/?page_size=1 HTTP/1.1" 200 198
[2025-05-14 12:57:52,738] - INFO - django.server - "GET /api/v1/products/categories/tendom-box/products/?page_size=1 HTTP/1.1" 200 208
[2025-05-14 12:57:53,031] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:53,031] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:53,127] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:53,167] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:53,216] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:57:53,244] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:57:53,956] - INFO - django.server - "GET /api/v1/products/categories/bottle-pullout/products/?page_size=1 HTTP/1.1" 200 216
[2025-05-14 12:57:54,090] - INFO - django.server - "GET /api/v1/products/categories/majic-carnes/products/?page_size=1 HTTP/1.1" 200 212
[2025-05-14 12:57:54,440] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:57:54,799] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:57:54,869] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:54,914] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:57:55,265] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:57:55,296] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:57:55,297] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:57:55,309] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:55,372] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:55,463] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:57:56,085] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:56,197] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:56,570] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:57,051] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=1 HTTP/1.1" 200 142
[2025-05-14 12:57:57,093] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:57:57,188] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:57:57,396] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:57,440] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:57,523] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:57:57,597] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:57,613] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:57:57,634] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:57:58,202] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=1 HTTP/1.1" 200 132
[2025-05-14 12:57:58,294] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:57:58,652] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:59,197] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:57:59,279] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:59,299] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:57:59,505] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:59,651] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:59,665] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:57:59,720] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:57:59,738] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:57:59,864] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:58:00,299] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:00,402] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:00,780] - INFO - django.server - "GET /api/v1/products/categories/chimney/products/?page_size=1 HTTP/1.1" 200 200
[2025-05-14 12:58:01,312] - INFO - django.server - "GET /api/v1/products/categories/mov/products/?page_size=1 HTTP/1.1" 200 192
[2025-05-14 12:58:01,547] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:58:01,557] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:58:01,617] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:58:01,751] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:01,847] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:01,857] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:01,911] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:58:02,096] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:58:02,388] - INFO - django.server - "GET /api/v1/products/categories/fevicol-jivanjor/products/?page_size=1 HTTP/1.1" 200 208
[2025-05-14 12:58:02,496] - INFO - django.server - "GET /api/v1/products/categories/polymyts/products/?page_size=1 HTTP/1.1" 200 192
[2025-05-14 12:58:02,895] - INFO - django.server - "GET /api/v1/products/categories/silicon/products/?page_size=1 HTTP/1.1" 200 190
[2025-05-14 12:58:03,413] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:58:03,642] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:03,656] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:58:03,725] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:58:03,865] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:03,976] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:04,174] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:58:04,355] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:58:04,487] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:04,587] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:05,093] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:58:05,647] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:58:05,736] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:58:05,755] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:58:05,858] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:05,951] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:06,397] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:58:06,590] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:58:06,628] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=1 HTTP/1.1" 200 2210
[2025-05-14 12:58:06,720] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:07,227] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:58:07,745] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:07,837] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:07,997] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:58:08,012] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:08,088] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:08,754] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:58:08,880] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:58:08,884] - INFO - django.server - "GET /api/v1/products/categories/door-locks/products/?page_size=1 HTTP/1.1" 200 1368
[2025-05-14 12:58:08,920] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:58:09,323] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:09,850] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:09,944] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:10,216] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:10,272] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:58:11,054] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=1 HTTP/1.1" 200 3198
[2025-05-14 12:58:11,070] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:58:11,090] - INFO - django.server - "GET /api/v1/products/categories/solid-surface/products/?page_size=1 HTTP/1.1" 200 1392
[2025-05-14 12:58:11,170] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:58:11,462] - INFO - django.server - "GET /api/v1/products/categories/laminates/products/?page_size=1 HTTP/1.1" 200 124
[2025-05-14 12:58:12,066] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:12,078] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:58:12,386] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:13,334] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:58:13,334] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:58:13,453] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:58:13,587] - INFO - django.server - "GET /api/v1/products/categories/brillient/products/?page_size=1 HTTP/1.1" 200 185
[2025-05-14 12:58:13,612] - INFO - django.server - "GET /api/v1/products/categories/crystal/products/?page_size=1 HTTP/1.1" 200 181
[2025-05-14 12:58:14,170] - INFO - django.server - "GET /api/v1/products/categories/ace/products/?page_size=1 HTTP/1.1" 200 173
[2025-05-14 12:58:14,326] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:58:14,523] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:15,535] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:58:15,643] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:58:16,327] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49399)
[2025-05-14 12:58:16,331] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49310)
[2025-05-14 12:58:16,356] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49672)
[2025-05-14 12:58:16,638] - INFO - django.server - - Broken pipe from ('127.0.0.1', 65041)
[2025-05-14 12:58:17,814] - INFO - django.server - - Broken pipe from ('127.0.0.1', 65043)
[2025-05-14 12:58:18,637] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:58:18,638] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:58:18,988] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:58:19,079] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 10959
[2025-05-14 12:58:19,225] - INFO - django.server - - Broken pipe from ('127.0.0.1', 50652)
[2025-05-14 12:58:19,647] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=1 HTTP/1.1" 200 1056
[2025-05-14 12:58:19,647] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=1 HTTP/1.1" 200 953
[2025-05-14 12:58:19,984] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=1 HTTP/1.1" 200 1036
[2025-05-14 12:58:20,786] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:58:21,196] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:58:21,277] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:58:21,735] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:58:21,766] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:58:22,114] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:22,987] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:58:23,411] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:23,441] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:58:23,842] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:23,904] - INFO - django.server - "GET /api/v1/products/categories/screws/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:58:24,242] - INFO - django.server - "GET /api/v1/products/categories/hinges/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:58:25,086] - INFO - django.server - "GET /api/v1/products/categories/tower-bolt/products/?page_size=1 HTTP/1.1" 200 185
[2025-05-14 12:58:25,528] - INFO - django.server - "GET /api/v1/products/categories/handles/products/?page_size=1 HTTP/1.1" 200 179
[2025-05-14 12:58:25,528] - INFO - django.server - "GET /api/v1/products/categories/door-kits/products/?page_size=1 HTTP/1.1" 200 183
[2025-05-14 12:58:25,940] - INFO - django.server - "GET /api/v1/products/categories/t-channel/products/?page_size=1 HTTP/1.1" 200 203
[2025-05-14 12:58:25,990] - INFO - django.server - "GET /api/v1/products/categories/tendom/products/?page_size=1 HTTP/1.1" 200 198
[2025-05-14 12:58:26,358] - INFO - django.server - "GET /api/v1/products/categories/tendom-box/products/?page_size=1 HTTP/1.1" 200 208
[2025-05-14 12:58:27,178] - INFO - django.server - "GET /api/v1/products/categories/bottle-pullout/products/?page_size=1 HTTP/1.1" 200 216
[2025-05-14 12:58:27,680] - INFO - django.server - "GET /api/v1/products/categories/majic-carnes/products/?page_size=1 HTTP/1.1" 200 212
[2025-05-14 12:58:27,766] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:58:28,107] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:58:28,164] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:58:28,495] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:58:29,261] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:29,784] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:30,023] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:58:30,221] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:30,409] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:58:30,592] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=1 HTTP/1.1" 200 142
[2025-05-14 12:58:31,388] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=1 HTTP/1.1" 200 132
[2025-05-14 12:58:31,908] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:58:32,379] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:58:32,384] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:58:32,515] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:58:32,720] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:33,504] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:34,052] - INFO - django.server - - Broken pipe from ('127.0.0.1', 50646)
[2025-05-14 12:58:34,631] - INFO - django.server - - Broken pipe from ('127.0.0.1', 50661)
[2025-05-14 12:58:34,737] - INFO - django.server - - Broken pipe from ('127.0.0.1', 50660)
[2025-05-14 12:58:34,738] - INFO - django.server - - Broken pipe from ('127.0.0.1', 50645)
[2025-05-14 12:58:34,894] - INFO - django.server - - Broken pipe from ('127.0.0.1', 50671)
[2025-05-14 12:58:35,632] - INFO - django.server - - Broken pipe from ('127.0.0.1', 49407)
[2025-05-14 12:58:38,716] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:58:38,728] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:58:38,810] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:58:38,975] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 11059
[2025-05-14 12:58:39,773] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:58:40,115] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:58:40,122] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:58:40,228] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:58:40,416] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 10583
[2025-05-14 12:58:40,544] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:58:41,836] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=1 HTTP/1.1" 200 1056
[2025-05-14 12:58:41,846] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=1 HTTP/1.1" 200 953
[2025-05-14 12:58:41,879] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=1 HTTP/1.1" 200 1036
[2025-05-14 12:58:42,041] - INFO - django.server - - Broken pipe from ('127.0.0.1', 50997)
[2025-05-14 12:58:42,082] - INFO - django.server - - Broken pipe from ('127.0.0.1', 50996)
[2025-05-14 12:58:42,277] - INFO - django.server - - Broken pipe from ('127.0.0.1', 50995)
[2025-05-14 12:58:42,801] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:58:43,769] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51130)
[2025-05-14 12:58:43,825] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51133)
[2025-05-14 12:58:43,838] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51141)
[2025-05-14 12:58:43,979] - INFO - django.server - - Broken pipe from ('127.0.0.1', 50994)
[2025-05-14 12:58:44,241] - INFO - django.server - - Broken pipe from ('127.0.0.1', 50991)
[2025-05-14 12:58:44,359] - INFO - django.server - - Broken pipe from ('127.0.0.1', 50993)
[2025-05-14 12:58:44,425] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51165)
[2025-05-14 12:58:44,494] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51150)
[2025-05-14 12:58:44,541] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51155)
[2025-05-14 12:58:44,602] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51161)
[2025-05-14 12:58:45,091] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:58:45,269] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:58:45,642] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:58:45,755] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:58:46,060] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:58:46,845] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51226)
[2025-05-14 12:58:47,200] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51063)
[2025-05-14 12:58:47,537] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51058)
[2025-05-14 12:58:47,865] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51054)
[2025-05-14 12:58:47,955] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51055)
[2025-05-14 12:58:48,389] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51067)
[2025-05-14 12:58:48,952] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51266)
[2025-05-14 12:58:48,952] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51273)
[2025-05-14 12:58:49,035] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51289)
[2025-05-14 12:58:49,052] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:58:49,066] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51291)
[2025-05-14 12:58:49,068] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51295)
[2025-05-14 12:58:49,072] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:58:49,136] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51269)
[2025-05-14 12:58:49,292] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:58:49,405] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51284)
[2025-05-14 12:58:49,431] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51286)
[2025-05-14 12:58:49,468] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 9691
[2025-05-14 12:58:49,950] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51332)
[2025-05-14 12:58:51,245] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=1 HTTP/1.1" 200 1056
[2025-05-14 12:58:51,285] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=1 HTTP/1.1" 200 953
[2025-05-14 12:58:51,291] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=1 HTTP/1.1" 200 1036
[2025-05-14 12:58:51,576] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51179)
[2025-05-14 12:58:51,731] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51184)
[2025-05-14 12:58:52,136] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51388)
[2025-05-14 12:58:52,218] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51390)
[2025-05-14 12:58:52,373] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51394)
[2025-05-14 12:58:52,740] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:58:52,760] - WARNING - django.request - Not Found: /api/v1/promotions/get/single/promotion/
[2025-05-14 12:58:52,766] - WARNING - django.server - "GET /api/v1/promotions/get/single/promotion/ HTTP/1.1" 404 43
[2025-05-14 12:58:53,023] - INFO - django.server - "GET /api/v1/products/feature/products/ HTTP/1.1" 200 4302
[2025-05-14 12:58:53,027] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:58:53,051] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:58:53,129] - INFO - django.server - "GET /api/v1/products/feature/products/?random=true HTTP/1.1" 200 9590
[2025-05-14 12:58:53,271] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:58:53,360] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:58:53,390] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:58:53,404] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:54,859] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:54,945] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51470)
[2025-05-14 12:58:55,035] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51462)
[2025-05-14 12:58:55,073] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51321)
[2025-05-14 12:58:55,159] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:55,249] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51322)
[2025-05-14 12:58:55,280] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:58:55,393] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51325)
[2025-05-14 12:58:55,471] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51323)
[2025-05-14 12:58:55,513] - INFO - django.server - "GET /api/v1/products/categories/screws/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:58:55,525] - INFO - django.server - "GET /api/v1/products/categories/hinges/products/?page_size=1 HTTP/1.1" 200 177
[2025-05-14 12:58:55,575] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:58:56,481] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51507)
[2025-05-14 12:58:56,544] - INFO - django.server - - Broken pipe from ('127.0.0.1', 51511)
[2025-05-14 12:58:56,569] - INFO - django.server - "GET /api/v1/products/categories/hardware/products/?page_size=8 HTTP/1.1" 200 5457
[2025-05-14 12:58:56,598] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:58:56,663] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:58:56,696] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:58:56,763] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:56,763] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:58:56,976] - INFO - django.server - "GET /api/v1/products/categories/tower-bolt/products/?page_size=1 HTTP/1.1" 200 185
[2025-05-14 12:58:57,246] - INFO - django.server - "GET /api/v1/products/categories/door-kits/products/?page_size=1 HTTP/1.1" 200 183
[2025-05-14 12:58:57,398] - INFO - django.server - "GET /api/v1/products/categories/handles/products/?page_size=1 HTTP/1.1" 200 179
[2025-05-14 12:58:57,605] - INFO - django.server - "GET /api/v1/products/categories/t-channel/products/?page_size=1 HTTP/1.1" 200 203
[2025-05-14 12:58:57,627] - INFO - django.server - "GET /api/v1/products/categories/tendom/products/?page_size=1 HTTP/1.1" 200 198
[2025-05-14 12:58:57,699] - INFO - django.server - "GET /api/v1/products/categories/tendom-box/products/?page_size=1 HTTP/1.1" 200 208
[2025-05-14 12:58:58,685] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:58,713] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:58:59,022] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:58:59,067] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:58:59,109] - INFO - django.server - "GET /api/v1/products/categories/bottle-pullout/products/?page_size=1 HTTP/1.1" 200 216
[2025-05-14 12:58:59,123] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:58:59,130] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:58:59,352] - INFO - django.server - "GET /api/v1/products/categories/majic-carnes/products/?page_size=1 HTTP/1.1" 200 212
[2025-05-14 12:58:59,691] - INFO - django.server - "GET /api/v1/products/categories/furniture-fittings/products/?page_size=8 HTTP/1.1" 200 6401
[2025-05-14 12:58:59,731] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:58:59,795] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:58:59,843] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:59:00,811] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:59:00,824] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:59:01,124] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:01,155] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:01,196] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:01,228] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:01,342] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:59:01,440] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:01,778] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:01,931] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:59:01,952] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=1 HTTP/1.1" 200 142
[2025-05-14 12:59:02,041] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:59:03,134] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:59:03,134] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:59:03,235] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:59:03,250] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:59:03,291] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=1 HTTP/1.1" 200 132
[2025-05-14 12:59:03,334] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:03,478] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:03,537] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:59:03,916] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:59:04,072] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:59:04,156] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:04,158] - INFO - django.server - "GET /api/v1/products/categories/kitchen-accessories/products/?page_size=8 HTTP/1.1" 200 11215
[2025-05-14 12:59:05,235] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:05,364] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:59:05,387] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:59:05,420] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:05,440] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:59:05,486] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:59:05,574] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:05,655] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:06,101] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:59:06,167] - INFO - django.server - "GET /api/v1/products/categories/chimney/products/?page_size=1 HTTP/1.1" 200 200
[2025-05-14 12:59:06,269] - INFO - django.server - "GET /api/v1/products/categories/mov/products/?page_size=1 HTTP/1.1" 200 192
[2025-05-14 12:59:06,345] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:59:07,362] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:07,504] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:07,542] - INFO - django.server - "GET /api/v1/products/categories/fevicol-jivanjor/products/?page_size=1 HTTP/1.1" 200 208
[2025-05-14 12:59:07,547] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:59:07,636] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:59:07,696] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:59:07,709] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:07,785] - INFO - django.server - "GET /api/v1/products/categories/polymyts/products/?page_size=1 HTTP/1.1" 200 192
[2025-05-14 12:59:08,202] - INFO - django.server - "GET /api/v1/products/categories/silicon/products/?page_size=1 HTTP/1.1" 200 190
[2025-05-14 12:59:08,276] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:59:08,385] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:08,462] - INFO - django.server - "GET /api/v1/products/categories/kitchen-appliances/products/?page_size=8 HTTP/1.1" 200 142
[2025-05-14 12:59:09,482] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:09,591] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:09,621] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:09,823] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:59:09,840] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:09,893] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:09,926] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:59:10,518] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:59:10,524] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:59:10,566] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:59:10,591] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=1 HTTP/1.1" 200 116
[2025-05-14 12:59:11,621] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:11,720] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:11,774] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=1 HTTP/1.1" 200 2210
[2025-05-14 12:59:12,025] - INFO - django.server - "GET /api/v1/products/categories/tools/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:12,187] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:59:12,356] - INFO - django.server - "GET /api/v1/products/categories/smart-house-accessories/products/?page_size=8 HTTP/1.1" 200 14373
[2025-05-14 12:59:12,630] - INFO - django.server - "GET /api/v1/products/categories/adhesive-glue/products/?page_size=8 HTTP/1.1" 200 132
[2025-05-14 12:59:12,660] - INFO - django.server - "GET /api/v1/products/categories/tapes/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:12,695] - INFO - django.server - "GET /api/v1/products/categories/locks/products/?page_size=8 HTTP/1.1" 200 116
[2025-05-14 12:59:12,964] - INFO - django.server - "GET /api/v1/products/categories/digital-locks/products/?page_size=8 HTTP/1.1" 200 13740
[2025-05-14 12:59:13,336] - INFO - django.utils.autoreload - D:\Triumph\e-com-2024-apis\backend\wsgi.py changed, reloading.
