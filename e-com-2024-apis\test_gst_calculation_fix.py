#!/usr/bin/env python3
"""
Test GST calculation fix - verify correct base price and GST amounts
"""

import os
import sys
import django
from decimal import Decimal

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

django.setup()

def test_gst_calculation_fix():
    """Test that GST calculations show correct base price and GST amounts"""
    print("🧮 Testing GST Calculation Fix...")
    
    from django.contrib.auth import get_user_model
    from orders.models import Cart, CartItem
    from orders.serializers import CartSerializer
    from products.models import Product, GST, Category, Brand
    from decimal import Decimal
    
    User = get_user_model()
    
    try:
        # 1. Create test user
        user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'first_name': 'GST',
                'last_name': 'Test',
                'phone': '1234567890'
            }
        )
        print(f"✅ Test user: {user.email}")
        
        # 2. Create test GST rate (18%)
        test_gst, created = GST.objects.get_or_create(
            name="Test GST 18%",
            defaults={
                'rate': Decimal('18.00'),
                'cgst_rate': Decimal('9.00'),
                'sgst_rate': Decimal('9.00'),
                'igst_rate': Decimal('18.00'),
                'hsn_code': '8471',
                'is_active': True
            }
        )
        print(f"✅ Test GST: {test_gst.rate}%")
        
        # 3. Create test product with MRP ₹2270 (GST inclusive)
        category, created = Category.objects.get_or_create(
            name="Test Category GST",
            defaults={'slug': 'test-category-gst'}
        )
        
        brand, created = Brand.objects.get_or_create(
            name="Test Brand GST",
            defaults={'slug': 'test-brand-gst'}
        )
        
        product, created = Product.objects.get_or_create(
            slug="door-handle-gst-test",
            defaults={
                'name': "Door Handle set Lock Body 2C Mortise Locks Mortise Locks",
                'description': "Test product for GST calculation",
                'category': category,
                'brand': brand,
                'price': Decimal('2270.00'),  # MRP inclusive of 18% GST
                'gst': test_gst,
                'stock': 10,
                'is_active': True
            }
        )
        print(f"✅ Test product: {product.name}")
        print(f"   MRP (GST inclusive): ₹{product.price}")
        
        # 4. Calculate expected values
        # MRP = ₹2270 (inclusive of 18% GST)
        # Base Price = MRP / (1 + GST_rate/100) = 2270 / 1.18 = ₹1923.73
        # GST Amount = MRP - Base Price = 2270 - 1923.73 = ₹346.27
        
        expected_base_price = Decimal('2270.00') / Decimal('1.18')
        expected_gst_amount = Decimal('2270.00') - expected_base_price
        
        print(f"\n📊 Expected Calculations:")
        print(f"   Base Price: ₹{expected_base_price:.2f}")
        print(f"   GST Amount: ₹{expected_gst_amount:.2f}")
        print(f"   Total (MRP): ₹2270.00")
        
        # 5. Test product GST calculation
        gst_breakdown = product.calculate_gst_breakdown_from_mrp(quantity=1)
        
        print(f"\n🧪 Product GST Breakdown:")
        print(f"   Unit MRP: ₹{gst_breakdown['unit_mrp']}")
        print(f"   Unit Base Price: ₹{gst_breakdown['unit_base_price']}")
        print(f"   GST Rate: {gst_breakdown['gst_rate']}%")
        print(f"   Total GST: ₹{gst_breakdown['total_gst']}")
        print(f"   CGST: ₹{gst_breakdown['cgst_amount']}")
        print(f"   SGST: ₹{gst_breakdown['sgst_amount']}")
        
        # 6. Create cart and add item
        cart, created = Cart.objects.get_or_create(user=user)
        cart.items.all().delete()  # Clear existing items
        
        cart_item, created = CartItem.objects.get_or_create(
            cart=cart,
            product=product,
            defaults={'quantity': 1}
        )
        print(f"\n✅ Cart item added: {cart_item.quantity}x {product.name}")
        
        # 7. Test cart serialization
        cart_serializer = CartSerializer(cart)
        cart_data = cart_serializer.data
        
        print(f"\n🛒 Cart Serialization Results:")
        
        if 'gst_breakdown' in cart_data:
            gst_breakdown = cart_data['gst_breakdown']
            
            print(f"   Subtotal (base): ₹{gst_breakdown.get('subtotal', 0)}")
            print(f"   Total MRP: ₹{gst_breakdown.get('total_mrp', 0)}")
            print(f"   Total GST: ₹{gst_breakdown.get('total_gst_amount', 0)}")
            print(f"   CGST: ₹{gst_breakdown.get('total_cgst_amount', 0)}")
            print(f"   SGST: ₹{gst_breakdown.get('total_sgst_amount', 0)}")
            
            # Verify calculations
            subtotal = float(gst_breakdown.get('subtotal', 0))
            total_gst = float(gst_breakdown.get('total_gst_amount', 0))
            total_mrp = float(gst_breakdown.get('total_mrp', 0))
            
            print(f"\n✅ Verification:")
            print(f"   Base Price: ₹{subtotal:.2f} (Expected: ₹{expected_base_price:.2f})")
            print(f"   GST Amount: ₹{total_gst:.2f} (Expected: ₹{expected_gst_amount:.2f})")
            print(f"   Total MRP: ₹{total_mrp:.2f} (Expected: ₹2270.00)")
            
            # Check if calculations are correct (within 0.01 tolerance)
            base_correct = abs(subtotal - float(expected_base_price)) < 0.01
            gst_correct = abs(total_gst - float(expected_gst_amount)) < 0.01
            total_correct = abs(total_mrp - 2270.00) < 0.01
            
            if base_correct and gst_correct and total_correct:
                print(f"\n🎉 GST Calculation Fix Successful!")
                print(f"   ✅ Base price calculation correct")
                print(f"   ✅ GST amount calculation correct")
                print(f"   ✅ Total MRP calculation correct")
                return True
            else:
                print(f"\n❌ GST Calculation Issues:")
                if not base_correct:
                    print(f"   ❌ Base price incorrect: got ₹{subtotal:.2f}, expected ₹{expected_base_price:.2f}")
                if not gst_correct:
                    print(f"   ❌ GST amount incorrect: got ₹{total_gst:.2f}, expected ₹{expected_gst_amount:.2f}")
                if not total_correct:
                    print(f"   ❌ Total MRP incorrect: got ₹{total_mrp:.2f}, expected ₹2270.00")
                return False
        else:
            print("❌ No GST breakdown found in cart data")
            return False
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_gst_calculation_fix()
    
    if success:
        print("\n✅ SUMMARY: GST Calculation Fix Verified!")
        print("   🔹 Base price correctly calculated from GST-inclusive MRP")
        print("   🔹 GST amount correctly extracted from MRP")
        print("   🔹 Frontend will now show correct subtotal and GST breakdown")
        print("   🔹 Cart API returns proper base price and GST amounts")
    else:
        print("\n❌ GST calculation fix needs more work")
    
    sys.exit(0 if success else 1)
