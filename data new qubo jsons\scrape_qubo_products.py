import requests
from bs4 import BeautifulSoup
import json
import re
from urllib.parse import urljoin, urlparse
import time

def load_existing_products(json_file):
    """Load existing products from JSON file"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return []

def extract_images_from_html(html_content, base_url):
    """Extract all image URLs from HTML content"""
    soup = BeautifulSoup(html_content, 'html.parser')
    images = []
    
    # Find all img tags
    img_tags = soup.find_all('img')
    
    for img in img_tags:
        src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
        if src:
            # Convert relative URLs to absolute URLs
            full_url = urljoin(base_url, src)
            # Filter out small icons, logos, and irrelevant images
            if any(keyword in src.lower() for keyword in ['icon', 'logo', 'arrow', 'check', 'cross', 'dot', 'star']):
                continue
            if any(size in src.lower() for size in ['16x16', '32x32', '24x24']):
                continue
            images.append(full_url)
    
    return list(set(images))  # Remove duplicates

def scrape_product_page(url):
    """Scrape a specific product page for detailed information and images"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Extract product name
        name = ""
        name_selectors = ['h1', '.product-name', '.page-title', 'title']
        for selector in name_selectors:
            element = soup.select_one(selector)
            if element:
                name = element.get_text().strip()
                break
        
        # Extract images
        images = extract_images_from_html(response.text, url)
        
        return {
            'name': name,
            'images': images,
            'url': url
        }
    except Exception as e:
        print(f"Error scraping {url}: {e}")
        return None

def find_similar_products(existing_products, scraped_products):
    """Find similar products between existing JSON and scraped data"""
    matches = []
    
    for existing in existing_products:
        if existing.get('category') != 'Smart Home':
            continue
            
        existing_name = existing.get('name', '').lower()
        existing_words = set(re.findall(r'\w+', existing_name))
        
        for scraped in scraped_products:
            scraped_name = scraped.get('name', '').lower()
            scraped_words = set(re.findall(r'\w+', scraped_name))
            
            # Calculate similarity based on common words
            common_words = existing_words.intersection(scraped_words)
            if len(common_words) >= 2:  # At least 2 common words
                similarity = len(common_words) / max(len(existing_words), len(scraped_words))
                if similarity > 0.3:  # 30% similarity threshold
                    matches.append({
                        'existing_product': existing,
                        'scraped_product': scraped,
                        'similarity': similarity
                    })
    
    return matches

def scrape_qubo_smart_home():
    """Main function to scrape Qubo smart home products"""
    
    # URLs to scrape
    urls_to_scrape = [
        'https://www.quboworld.com/smart-cameras',
        'https://www.quboworld.com/smart-locks',
        'https://www.quboworld.com/lighting-control',
        'https://www.quboworld.com/video-door-bell',
        'https://www.quboworld.com/home-air-purifier'
    ]
    
    scraped_products = []
    
    for url in urls_to_scrape:
        print(f"Scraping {url}...")
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            
            # Extract images from the category page
            images = extract_images_from_html(response.text, url)
            
            # Filter product images (larger images, likely product photos)
            product_images = [img for img in images if any(keyword in img.lower() for keyword in 
                            ['product', 'camera', 'lock', 'bulb', 'plug', 'doorbell', 'purifier'])]
            
            # Create a product entry for this category
            category_name = url.split('/')[-1].replace('-', ' ').title()
            scraped_products.append({
                'name': f"Qubo {category_name}",
                'category': 'Smart Home',
                'images': product_images[:10],  # Limit to 10 images per category
                'source_url': url
            })
            
            time.sleep(1)  # Be respectful to the server
            
        except Exception as e:
            print(f"Error scraping {url}: {e}")
    
    return scraped_products

if __name__ == "__main__":
    # Load existing products
    existing_products = load_existing_products('products_qubo.json')
    print(f"Loaded {len(existing_products)} existing products")
    
    # Scrape new products
    scraped_products = scrape_qubo_smart_home()
    print(f"Scraped {len(scraped_products)} product categories")
    
    # Find matches
    matches = find_similar_products(existing_products, scraped_products)
    print(f"Found {len(matches)} potential matches")
    
    # Create final output
    output_products = []
    
    for match in matches:
        existing = match['existing_product']
        scraped = match['scraped_product']
        
        output_products.append({
            'name': existing.get('name'),
            'category': existing.get('category'),
            'description': existing.get('description', ''),
            'images': scraped.get('images', []),
            'similarity_score': match['similarity'],
            'source_url': scraped.get('source_url')
        })
    
    # Also add products from JSON that are in Smart Home category
    smart_home_products = [p for p in existing_products if p.get('category') == 'Smart Home']
    
    for product in smart_home_products:
        # Check if not already in output_products
        if not any(op['name'] == product['name'] for op in output_products):
            output_products.append({
                'name': product.get('name'),
                'category': product.get('category'),
                'description': product.get('description', ''),
                'images': [],  # Will be populated separately
                'similarity_score': 0,
                'source_url': ''
            })
    
    # Save results
    with open('qubo_smart_home_with_images.json', 'w', encoding='utf-8') as f:
        json.dump(output_products, f, indent=2, ensure_ascii=False)
    
    print(f"Saved {len(output_products)} products to qubo_smart_home_with_images.json")
    
    # Print summary
    print("\nSummary:")
    for product in output_products[:5]:  # Show first 5
        print(f"- {product['name']}: {len(product['images'])} images")
