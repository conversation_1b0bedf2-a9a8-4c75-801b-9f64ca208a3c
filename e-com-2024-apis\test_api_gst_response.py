#!/usr/bin/env python
"""
Test script to verify API responses include GST breakdown
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from decimal import Decimal
from products.models import GST, Product, Category, Brand
from products.serializers import ProductListSerializer, ProductDetailSerializer
from rest_framework.test import APIRequestFactory
import json

def test_api_gst_response():
    """Test API responses include GST breakdown"""
    print("=" * 60)
    print("Testing API GST Response Format")
    print("=" * 60)
    
    # Get the test product we created earlier
    try:
        product = Product.objects.get(name="SLIDING ROLLER KIT /2 DOOR Weight Capacity 50kg Door")
        print(f"Found test product: {product.name}")
        print(f"MRP: ₹{product.price}")
        print(f"GST Rate: {product.get_gst_rate().rate}%")
    except Product.DoesNotExist:
        print("Test product not found. Please run test_gst_inclusive.py first.")
        return
    
    # Create a mock request
    factory = APIRequestFactory()
    request = factory.get('/api/products/')
    
    # Test ProductListSerializer
    print("\n1. Testing ProductListSerializer:")
    print("-" * 40)
    
    list_serializer = ProductListSerializer(product, context={'request': request})
    list_data = list_serializer.data
    
    print("ProductListSerializer Response:")
    print(json.dumps(dict(list_data), indent=2, default=str))
    
    # Test ProductDetailSerializer
    print("\n2. Testing ProductDetailSerializer:")
    print("-" * 40)
    
    detail_serializer = ProductDetailSerializer(product, context={'request': request})
    detail_data = detail_serializer.data
    
    print("ProductDetailSerializer Response:")
    print(json.dumps(dict(detail_data), indent=2, default=str))
    
    # Verify key fields are present
    print("\n3. Verification:")
    print("-" * 20)
    
    required_fields = ['mrp', 'base_price', 'gst_amount', 'gst_rate']
    
    print("ProductListSerializer fields:")
    for field in required_fields:
        if field in list_data:
            print(f"  ✅ {field}: {list_data[field]}")
        else:
            print(f"  ❌ {field}: Missing")
    
    print("\nProductDetailSerializer additional fields:")
    if 'gst_breakdown' in detail_data:
        print(f"  ✅ gst_breakdown: {detail_data['gst_breakdown']}")
    else:
        print(f"  ❌ gst_breakdown: Missing")
    
    print("\n" + "=" * 60)
    print("API GST Response Test Complete!")
    print("=" * 60)

if __name__ == "__main__":
    test_api_gst_response()
