# nixpacks.toml - Configuration for Django with Gunicorn

# Setup phase - Install required system packages
[phases.setup]
nixPkgs = ["python311", "gcc"]

# Install phase - Install Python dependencies
[phases.install]
cmds = [
  "python3 -m venv /opt/venv",
  "source /opt/venv/bin/activate && python3 -m pip install --upgrade pip",
  "source /opt/venv/bin/activate && python3 -m pip install -r requirements.txt",
  "source /opt/venv/bin/activate && python3 -m pip install gunicorn",
  "source /opt/venv/bin/activate && python3 -m pip install pytest",
  "source /opt/venv/bin/activate && python3 -m pip install --upgrade phonepe-sdk --extra-index-url https://phonepe.mycloudrepo.io/public/repositories/phonepe-pg-sdk-python",
  "source /opt/venv/bin/activate && python3 -m pip install requests boto3 urllib3"  # For CDN and MinIO sync scripts
]

# Build phase - Collect static files, run migrations, and tests
[phases.build]
cmds = [
  "mkdir -p media staticfiles",
  "source /opt/venv/bin/activate && python3 manage.py collectstatic --noinput",
  "source /opt/venv/bin/activate && python3 manage.py makemigrations",
  "source /opt/venv/bin/activate && python3 manage.py migrate",
  "source /opt/venv/bin/activate && python3 -m pytest --cov=. --cov-report=term || true",
  "source /opt/venv/bin/activate && python3 sync_media_to_minio.py --media-dir media --force --skip-errors || true"
]

# Start command - Run Gunicorn with appropriate settings
[start]
cmd = "gunicorn backend.wsgi:application --bind=0.0.0.0:$PORT --workers=4 --threads=2 --worker-class=gthread --worker-tmp-dir=/dev/shm --log-file=- --access-logfile=- --error-logfile=- --capture-output --enable-stdio-inheritance --forwarded-allow-ips='*'"

# Environment variables
[variables]
PYTHONUNBUFFERED = "1"
PYTHONDONTWRITEBYTECODE = "1"
DJANGO_SETTINGS_MODULE = "backend.settings"
DEBUG = "0"
STATIC_ROOT = "static"
STATIC_URL = "/static/"

# CDN configuration
USE_CDN = "true"
CDN_REPO_OWNER = "sohangpurh53"
CDN_REPO_NAME = "triumph-static-assets"
CDN_REPO_VERSION = "main"
CDN_URL = "https://cdn.jsdelivr.net/gh/sohangpurh53/triumph-static-assets@main"

# MinIO configuration
USE_MINIO = "true"
MINIO_STORAGE_ENDPOINT = "minio-triumph.trio.net.in"
MINIO_STORAGE_ACCESS_KEY = "sGMhBtgAJbSCLqTu"
MINIO_STORAGE_SECRET_KEY = "AEcMahOmlUIC1noyDcwuHLh5y5lSY2ju"
MINIO_STORAGE_USE_HTTPS = "true"
MINIO_STORAGE_MEDIA_BUCKET_NAME = "media"
MINIO_STORAGE_AUTO_CREATE_MEDIA_BUCKET = "true"