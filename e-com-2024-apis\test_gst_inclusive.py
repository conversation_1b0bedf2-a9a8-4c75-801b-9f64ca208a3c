#!/usr/bin/env python
"""
Test script for GST inclusive pricing functionality
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from decimal import Decimal
from products.models import GST, Product, Category, Brand
from orders.gst_service import gst_service

def test_gst_inclusive_calculations():
    """Test GST inclusive pricing calculations"""
    print("=" * 60)
    print("Testing GST Inclusive Pricing Calculations")
    print("=" * 60)

    # Test case from your example: MRP ₹1530, should give base price ₹1254.6 and GST ₹275.4
    test_mrp = Decimal('1530.00')
    expected_base_price = Decimal('1254.6')
    expected_gst = Decimal('275.4')

    # Calculate what GST rate would give us the expected values
    expected_gst_rate = (expected_gst / expected_base_price) * 100
    print(f"Expected GST rate for your example: {expected_gst_rate:.2f}%")

    # Create a custom GST rate that matches your example (approximately 21.95%)
    custom_gst, created = GST.objects.get_or_create(
        name="Custom Rate for Example",
        defaults={
            'rate': Decimal('21.95'),
            'cgst_rate': Decimal('10.975'),
            'sgst_rate': Decimal('10.975'),
            'igst_rate': Decimal('21.95'),
            'hsn_code': '8471',
            'description': 'Custom GST rate to match the example',
            'is_active': True
        }
    )

    print(f"Using custom GST rate: {custom_gst.rate}% to match your example")

    # Also test with standard 18% GST
    default_gst = GST.get_default_gst()
    print(f"Standard GST rate: {default_gst.rate}%")
    
    # Test GST model calculations with custom rate
    print("\n1. Testing GST Model Calculations (Custom Rate):")
    print("-" * 50)

    calculated_base_price = custom_gst.calculate_base_price_from_inclusive(test_mrp)
    calculated_gst = custom_gst.calculate_gst_from_inclusive(test_mrp)
    calculated_cgst = custom_gst.calculate_cgst_from_inclusive(test_mrp)
    calculated_sgst = custom_gst.calculate_sgst_from_inclusive(test_mrp)
    
    print(f"MRP (inclusive): ₹{test_mrp}")
    print(f"Calculated base price: ₹{calculated_base_price:.2f}")
    print(f"Expected base price: ₹{expected_base_price}")
    print(f"Calculated GST: ₹{calculated_gst:.2f}")
    print(f"Expected GST: ₹{expected_gst}")
    print(f"CGST (9%): ₹{calculated_cgst:.2f}")
    print(f"SGST (9%): ₹{calculated_sgst:.2f}")
    print(f"Total GST: ₹{calculated_cgst + calculated_sgst:.2f}")
    
    # Verify calculations
    print(f"\nVerification:")
    print(f"Base price + GST = ₹{calculated_base_price + calculated_gst:.2f}")
    print(f"Should equal MRP: ₹{test_mrp}")
    print(f"Match: {abs((calculated_base_price + calculated_gst) - test_mrp) < Decimal('0.01')}")
    
    # Test GST breakdown with custom rate
    print("\n2. Testing GST Breakdown from Inclusive Price (Custom Rate):")
    print("-" * 60)

    breakdown = custom_gst.calculate_gst_breakdown_from_inclusive(test_mrp, is_inter_state=False)
    print(f"GST Breakdown:")
    for key, value in breakdown.items():
        print(f"  {key}: ₹{value}")

    # Also test with standard 18% GST for comparison
    print("\n2b. Testing with Standard 18% GST:")
    print("-" * 40)

    std_breakdown = default_gst.calculate_gst_breakdown_from_inclusive(test_mrp, is_inter_state=False)
    print(f"Standard GST Breakdown:")
    for key, value in std_breakdown.items():
        print(f"  {key}: ₹{value}")
    
    # Test with a product
    print("\n3. Testing Product GST Calculations:")
    print("-" * 40)
    
    # Get or create a test category and brand
    category, _ = Category.objects.get_or_create(
        name="Test Category",
        defaults={'slug': 'test-category', 'description': 'Test category for GST testing'}
    )
    
    brand, _ = Brand.objects.get_or_create(
        name="Test Brand",
        defaults={'description': 'Test brand for GST testing'}
    )
    
    # Create or get a test product with MRP ₹1530
    product, created = Product.objects.get_or_create(
        name="SLIDING ROLLER KIT /2 DOOR Weight Capacity 50kg Door",
        defaults={
            'slug': 'sliding-roller-kit-2-door-50kg',
            'description': 'Test product for GST inclusive pricing',
            'category': category,
            'brand': brand,
            'price': test_mrp,  # This is now MRP (inclusive of GST)
            'gst': custom_gst,  # Use custom GST rate
            'stock': 10,
            'is_active': True
        }
    )

    if created:
        print(f"Created test product: {product.name}")
    else:
        print(f"Using existing product: {product.name}")
        # Update price and GST rate to test MRP
        product.price = test_mrp
        product.gst = custom_gst
        product.save()
    
    print(f"\nProduct Details:")
    print(f"  Name: {product.name}")
    print(f"  MRP (stored price): ₹{product.mrp}")
    print(f"  Base price: ₹{product.base_price:.2f}")
    print(f"  GST amount: ₹{product.calculate_gst_from_mrp():.2f}")
    print(f"  CGST amount: ₹{product.calculate_cgst_from_mrp():.2f}")
    print(f"  SGST amount: ₹{product.calculate_sgst_from_mrp():.2f}")
    
    # Test product GST breakdown
    product_breakdown = product.calculate_gst_breakdown_from_mrp(quantity=1, is_inter_state=False)
    print(f"\nProduct GST Breakdown:")
    for key, value in product_breakdown.items():
        print(f"  {key}: {value}")
    
    # Test GST service
    print("\n4. Testing GST Service:")
    print("-" * 30)
    
    service_calculation = gst_service.calculate_product_gst_from_mrp(product, quantity=1)
    print(f"GST Service Calculation:")
    for key, value in service_calculation.items():
        print(f"  {key}: {value}")
    
    print("\n" + "=" * 60)
    print("GST Inclusive Pricing Test Complete!")
    print("=" * 60)

if __name__ == "__main__":
    test_gst_inclusive_calculations()
