import { Button } from "../../components/ui/button";
import { Separator } from "../../components/ui/separator";
import { ShoppingBag, Info } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface OrderSummaryProps {
  subtotal: number;
  shippingCost: number;
  discount: number;
  total: number;
  gstAmount?: number;
  cgstAmount?: number;
  sgstAmount?: number;
  igstAmount?: number;
  showGstBreakdown?: boolean;
  gstBreakdown?: {
    total_gst_amount: number;
    total_cgst_amount: number;
    total_sgst_amount: number;
    total_igst_amount: number;
    item_details: Array<{
      product: any;
      quantity: number;
      gst_rate: number;
      gst_amount: number;
      hsn_code: string;
    }>;
  };
}

export const OrderSummary = ({
  subtotal,
  shippingCost,
  discount,
  total,
  gstAmount,
  cgstAmount,
  sgstAmount,
  igstAmount,
  showGstBreakdown = false,
  gstBreakdown,
}: OrderSummaryProps) => {
  const [showGstDetails, setShowGstDetails] = useState(false);

  // Use dynamic GST breakdown if available, otherwise fallback to provided amounts or 18% default
  const calculatedGstAmount = gstBreakdown?.total_gst_amount || gstAmount || (subtotal * 0.18);
  const calculatedCgstAmount = gstBreakdown?.total_cgst_amount || cgstAmount || (calculatedGstAmount / 2);
  const calculatedSgstAmount = gstBreakdown?.total_sgst_amount || sgstAmount || (calculatedGstAmount / 2);
  const calculatedIgstAmount = gstBreakdown?.total_igst_amount || igstAmount || 0;

  // Check if we have dynamic GST rates (not all products have the same rate)
  const hasDynamicGst = gstBreakdown?.item_details && gstBreakdown.item_details.length > 0;
  const uniqueGstRates = hasDynamicGst
    ? [...new Set(gstBreakdown!.item_details.map(item => item.gst_rate))]
    : [18]; // Default rate

  const displayGstRate = uniqueGstRates.length === 1
    ? `${uniqueGstRates[0]}%`
    : 'Mixed Rates';

  return (
    <div className="border rounded-lg p-6 space-y-6 sticky top-4 shadow-sm bg-white">
      <h2 className="text-xl font-semibold flex items-center gap-2">
        <ShoppingBag className="h-5 w-5" />
        Order Summary
      </h2>

      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-gray-600">Subtotal (before GST)</span>
          <span className="font-medium">₹{subtotal > 0 ? subtotal.toFixed(2) : "0.00"}</span>
        </div>

        {/* GST Section */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <span className="text-gray-600">GST ({displayGstRate})</span>
              {showGstBreakdown && (
                <button
                  onClick={() => setShowGstDetails(!showGstDetails)}
                  className="text-blue-600 hover:text-blue-800"
                >
                  <Info className="h-4 w-4" />
                </button>
              )}
            </div>
            <span className="font-medium">₹{calculatedGstAmount.toFixed(2)}</span>
          </div>

          {/* GST Breakdown */}
          {showGstDetails && showGstBreakdown && (
            <div className="ml-4 space-y-1 text-sm">
              {/* Show product-wise GST breakdown if available */}
              {hasDynamicGst && (
                <div className="space-y-2 mb-3">
                  <div className="text-xs font-medium text-gray-700">Product-wise GST:</div>
                  {gstBreakdown!.item_details.map((item, index) => (
                    <div key={index} className="flex justify-between text-xs text-gray-500">
                      <span className="truncate max-w-[120px]">
                        {item.product?.name || 'Product'} ({item.gst_rate}%)
                      </span>
                      <span>₹{item.gst_amount.toFixed(2)}</span>
                    </div>
                  ))}
                  <div className="border-t pt-1 mt-2">
                    <div className="text-xs font-medium text-gray-700">Total GST Breakdown:</div>
                  </div>
                </div>
              )}

              {/* Standard GST breakdown */}
              {calculatedIgstAmount > 0 ? (
                <div className="flex justify-between text-gray-500">
                  <span>IGST ({uniqueGstRates.length === 1 ? uniqueGstRates[0] : 'Mixed'}%)</span>
                  <span>₹{calculatedIgstAmount.toFixed(2)}</span>
                </div>
              ) : (
                <>
                  <div className="flex justify-between text-gray-500">
                    <span>CGST ({uniqueGstRates.length === 1 ? (uniqueGstRates[0] / 2) : 'Mixed'}%)</span>
                    <span>₹{calculatedCgstAmount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-gray-500">
                    <span>SGST ({uniqueGstRates.length === 1 ? (uniqueGstRates[0] / 2) : 'Mixed'}%)</span>
                    <span>₹{calculatedSgstAmount.toFixed(2)}</span>
                  </div>
                </>
              )}
            </div>
          )}
        </div>

        <div className="flex justify-between items-center">
          <span className="text-gray-600">Shipping</span>
          <span className="font-medium">
            {shippingCost === 0 ?
              <span className="text-green-600">Free</span> :
              `₹${typeof shippingCost === "number" && shippingCost.toFixed(2)}`
            }
          </span>
        </div>

        {discount > 0 && (
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Discount</span>
            <span className="font-medium text-green-600">-₹{typeof discount === "number" && discount.toFixed(2)}</span>
          </div>
        )}
      </div>

      <Separator className="my-2" />

      <div className="flex justify-between items-center py-2">
        <span className="font-semibold text-lg">Total Amount</span>
        <span className="font-bold text-xl text-primary">
          ₹{typeof total === "number" ? total.toFixed(2) : (subtotal + calculatedGstAmount + shippingCost - discount).toFixed(2)}
        </span>
      </div>

      {/* GST Note */}
      <div className="text-xs text-gray-500 text-center mb-2">
        <p>* All prices are inclusive of applicable taxes</p>
        <p>GST will be shown separately on your invoice</p>
      </div>

      {subtotal < 150 ? (
        <div className="mt-4">
          <Button disabled className="w-full py-6 text-base font-medium flex items-center justify-center gap-2">
            <ShoppingBag className="h-5 w-5" />
            Proceed to Checkout
          </Button>
          <p className="text-sm text-red-500 mt-2 text-center">
            Minimum order value should be ₹150 to place an order.
            {subtotal > 0 && ` Add items worth ₹${(150 - subtotal).toFixed(2)} more.`}
          </p>
        </div>
      ) : (
        <Link href="/checkout">
          <Button className="w-full py-6 mt-4 text-base font-medium flex items-center justify-center gap-2 transition-all duration-300 hover:scale-[1.02]">
            <ShoppingBag className="h-5 w-5" />
            Proceed to Checkout
          </Button>
        </Link>
      )}

      <div className="mt-4 text-center">
        <p className="text-xs text-gray-500">
          Secure checkout powered by PhonePe
        </p>
      </div>
    </div>
  );
};
