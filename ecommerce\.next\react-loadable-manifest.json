{"app\\page.tsx -> @/components/home/<USER>": {"id": "app\\page.tsx -> @/components/home/<USER>", "files": ["static/chunks/_app-pages-browser_components_home_hero-carousel_tsx.js"]}, "components\\home\\navigation.tsx -> @/constant/urls": {"id": "components\\home\\navigation.tsx -> @/constant/urls", "files": []}, "components\\utils\\JsonLdWrapper.tsx -> ./JsonLd": {"id": "components\\utils\\JsonLdWrapper.tsx -> ./JsonLd", "files": ["static/chunks/_app-pages-browser_components_utils_JsonLd_tsx.js"]}, "node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js"]}}