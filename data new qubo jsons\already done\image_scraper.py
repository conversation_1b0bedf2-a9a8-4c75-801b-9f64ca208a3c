import os
import json
import time
import requests
import threading
import concurrent.futures
import subprocess
import sys
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from urllib.parse import quote_plus
import re
import logging
from PIL import Image
from io import BytesIO

# Install undetected-chromedriver if not already installed
try:
    import undetected_chromedriver as uc
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "undetected-chromedriver"])
    import undetected_chromedriver as uc

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("image_scraper.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants
MAX_THREADS = 4
MAX_IMAGES_PER_PRODUCT = 5
TIMEOUT = 10
SEARCH_SOURCES = ["amazon", "flipkart", "google", "godrej"]
OUTPUT_DIR = "output/images"
JSON_FILE_PATH = "output/json/product_data.json"

# Create output directory structure
os.makedirs(OUTPUT_DIR, exist_ok=True)

def setup_driver():
    """Set up and return a Chrome WebDriver with appropriate options."""
    try:
        # First try using undetected-chromedriver
        options = uc.ChromeOptions()
        options.add_argument("--headless")
        options.add_argument("--disable-gpu")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--window-size=1920,1080")

        driver = uc.Chrome(options=options)
        logger.info("Successfully initialized undetected-chromedriver")
        return driver
    except Exception as e:
        logger.error(f"Failed to initialize undetected-chromedriver: {str(e)}")

        # Fallback to regular Chrome with specific options
        try:
            chrome_options = Options()
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

            # Try to use Chrome directly without ChromeDriverManager
            driver = webdriver.Chrome(options=chrome_options)
            logger.info("Successfully initialized Chrome WebDriver directly")
            return driver
        except Exception as e2:
            logger.error(f"Failed to initialize Chrome WebDriver directly: {str(e2)}")

            # If all else fails, try using Edge WebDriver as a last resort
            try:
                from selenium.webdriver.edge.service import Service as EdgeService
                from selenium.webdriver.edge.options import Options as EdgeOptions

                edge_options = EdgeOptions()
                edge_options.add_argument("--headless")
                edge_options.add_argument("--disable-gpu")
                edge_options.add_argument("--no-sandbox")
                edge_options.add_argument("--disable-dev-shm-usage")

                driver = webdriver.Edge(options=edge_options)
                logger.info("Successfully initialized Edge WebDriver as fallback")
                return driver
            except Exception as e3:
                logger.error(f"All WebDriver initialization methods failed. Last error: {str(e3)}")
                raise Exception("Could not initialize any WebDriver")

def download_image(url, product_dir, filename):
    """Download an image from URL and save it to the specified directory."""
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            # Verify it's an image
            try:
                img = Image.open(BytesIO(response.content))
                img_path = os.path.join(product_dir, filename)
                img.save(img_path)
                logger.info(f"Downloaded image: {img_path}")
                return img_path
            except Exception as e:
                logger.error(f"Not a valid image: {url}, Error: {str(e)}")
                return None
        else:
            logger.error(f"Failed to download image: {url}, Status code: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error downloading image: {url}, Error: {str(e)}")
        return None

def search_amazon(driver, query):
    """Search for product images on Amazon."""
    image_urls = []
    try:
        search_url = f"https://www.amazon.in/s?k={quote_plus(query)}"
        driver.get(search_url)
        time.sleep(2)  # Allow page to load

        # Find product links
        product_links = []
        try:
            products = driver.find_elements(By.CSS_SELECTOR, "div.s-result-item h2 a")
            for product in products[:3]:  # Get first 3 products
                href = product.get_attribute("href")
                if href:
                    product_links.append(href)
        except Exception as e:
            logger.error(f"Error finding Amazon product links: {str(e)}")

        # Visit each product page and extract images
        for link in product_links:
            try:
                driver.get(link)
                time.sleep(2)

                # Try different image selectors (Amazon's structure can vary)
                image_elements = []
                selectors = [
                    "#landingImage",
                    "#imgBlkFront",
                    "img.a-dynamic-image",
                    "#main-image-container img",
                    "#imageBlock img"
                ]

                for selector in selectors:
                    try:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            image_elements.extend(elements)
                    except:
                        continue

                # Extract image URLs
                for img in image_elements:
                    src = img.get_attribute("src")
                    if src and src not in image_urls and not src.endswith(".gif"):
                        # Get high-resolution version if available
                        src = re.sub(r'._SL\d+_', '._SL1500_', src)
                        image_urls.append(src)
                        if len(image_urls) >= MAX_IMAGES_PER_PRODUCT:
                            return image_urls
            except Exception as e:
                logger.error(f"Error processing Amazon product page: {str(e)}")
                continue

    except Exception as e:
        logger.error(f"Error in Amazon search: {str(e)}")

    return image_urls

def search_flipkart(driver, query):
    """Search for product images on Flipkart."""
    image_urls = []
    try:
        search_url = f"https://www.flipkart.com/search?q={quote_plus(query)}"
        driver.get(search_url)
        time.sleep(2)  # Allow page to load

        # Find product links
        product_links = []
        try:
            products = driver.find_elements(By.CSS_SELECTOR, "a._1fQZEK, a._2rpwqI, a.s1Q9rs")
            for product in products[:3]:  # Get first 3 products
                href = product.get_attribute("href")
                if href:
                    product_links.append(href)
        except Exception as e:
            logger.error(f"Error finding Flipkart product links: {str(e)}")

        # Visit each product page and extract images
        for link in product_links:
            try:
                driver.get(link)
                time.sleep(2)

                # Try different image selectors
                image_elements = []
                selectors = [
                    "img._396cs4",
                    "img._2r_T1I",
                    ".CXW8mj img",
                    "._3kidJX img"
                ]

                for selector in selectors:
                    try:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            image_elements.extend(elements)
                    except:
                        continue

                # Extract image URLs
                for img in image_elements:
                    src = img.get_attribute("src")
                    if src and src not in image_urls and not src.endswith(".gif"):
                        image_urls.append(src)
                        if len(image_urls) >= MAX_IMAGES_PER_PRODUCT:
                            return image_urls
            except Exception as e:
                logger.error(f"Error processing Flipkart product page: {str(e)}")
                continue

    except Exception as e:
        logger.error(f"Error in Flipkart search: {str(e)}")

    return image_urls

def search_google(driver, query):
    """Search for product images on Google Images."""
    image_urls = []
    try:
        search_url = f"https://www.google.com/search?q={quote_plus(query)}&tbm=isch"
        driver.get(search_url)
        time.sleep(2)  # Allow page to load

        # Scroll down to load more images
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(1)

        # Find image elements
        image_elements = driver.find_elements(By.CSS_SELECTOR, "img.rg_i, img.Q4LuWd")

        # Extract image URLs
        for img in image_elements[:MAX_IMAGES_PER_PRODUCT]:
            try:
                # Click on the image to get the full-size version
                img.click()
                time.sleep(1)

                # Try to get the large image
                large_img = WebDriverWait(driver, 3).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "img.r48jcc, img.n3VNCb"))
                )
                src = large_img.get_attribute("src")

                if src and src.startswith("http") and src not in image_urls and not src.endswith(".gif"):
                    image_urls.append(src)
            except:
                # If clicking fails, try to get the src directly
                src = img.get_attribute("src")
                if src and src.startswith("http") and src not in image_urls and not src.endswith(".gif"):
                    image_urls.append(src)

            if len(image_urls) >= MAX_IMAGES_PER_PRODUCT:
                break

    except Exception as e:
        logger.error(f"Error in Google search: {str(e)}")

    return image_urls

def search_godrej(driver, query):
    """Search for product images on Godrej Enterprises website."""
    image_urls = []
    try:
        search_url = "https://www.godrejenterprises.com/locks-and-security/locks"
        driver.get(search_url)
        time.sleep(2)  # Allow page to load

        # Try to find products that match the query
        product_links = []
        try:
            # Look for product links that might match our query
            all_links = driver.find_elements(By.CSS_SELECTOR, "a")
            for link in all_links:
                try:
                    text = link.text.lower()
                    href = link.get_attribute("href")
                    # Check if any part of our query is in the link text
                    query_parts = query.lower().split()
                    if any(part in text for part in query_parts) and href and "product" in href:
                        product_links.append(href)
                except:
                    continue
        except Exception as e:
            logger.error(f"Error finding Godrej product links: {str(e)}")

        # Visit each product page and extract images
        for link in product_links[:3]:  # Limit to first 3 matching products
            try:
                driver.get(link)
                time.sleep(2)

                # Try different image selectors
                image_elements = []
                selectors = [
                    ".product-image img",
                    ".product-gallery img",
                    ".product-detail img",
                    ".product img"
                ]

                for selector in selectors:
                    try:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            image_elements.extend(elements)
                    except:
                        continue

                # Extract image URLs
                for img in image_elements:
                    src = img.get_attribute("src")
                    if src and src not in image_urls and not src.endswith(".gif"):
                        image_urls.append(src)
                        if len(image_urls) >= MAX_IMAGES_PER_PRODUCT:
                            return image_urls
            except Exception as e:
                logger.error(f"Error processing Godrej product page: {str(e)}")
                continue

    except Exception as e:
        logger.error(f"Error in Godrej search: {str(e)}")

    return image_urls

def process_product(product):
    """Process a single product to find and download images."""
    # Create a unique search query for the product
    model_number = product.get("model_number", "")
    name = product.get("name", "")
    brand = product.get("brand", "")
    category = product.get("category", "")

    # Create search query with all relevant information
    search_query = f"{brand} {name} {model_number} {category}"

    # Add subcategory to search query if it's different from category
    subcategory = product.get("subcategory", "")
    if subcategory and subcategory != category:
        search_query += f" {subcategory}"

    # Add description keywords if they're not already in the name
    description = product.get("description", "")
    if description and description not in name:
        # Extract key terms from description that aren't in the search query
        desc_terms = [term for term in description.split() if term.lower() not in search_query.lower()]
        if desc_terms:
            search_query += f" {' '.join(desc_terms)}"

    # Create directory for product images
    product_dir = os.path.join(OUTPUT_DIR, f"{brand}_{category}_{model_number}")
    os.makedirs(product_dir, exist_ok=True)

    logger.info(f"Processing product: {name} (Model: {model_number})")

    # Initialize WebDriver
    driver = setup_driver()

    try:
        all_image_urls = []

        # Search on each platform
        for source in SEARCH_SOURCES:
            logger.info(f"Searching {source} for {name} (Model: {model_number})")

            if source == "amazon":
                urls = search_amazon(driver, search_query)
            elif source == "flipkart":
                urls = search_flipkart(driver, search_query)
            elif source == "google":
                urls = search_google(driver, search_query)
            elif source == "godrej":
                urls = search_godrej(driver, search_query)

            all_image_urls.extend(urls)

            # If we have enough images, stop searching
            if len(all_image_urls) >= MAX_IMAGES_PER_PRODUCT:
                all_image_urls = all_image_urls[:MAX_IMAGES_PER_PRODUCT]
                break

        # Download images
        image_paths = []
        for i, url in enumerate(all_image_urls):
            filename = f"{model_number}_{i+1}.jpg"
            img_path = download_image(url, product_dir, filename)
            if img_path:
                # Convert to relative path
                rel_path = os.path.relpath(img_path, os.getcwd())
                image_paths.append(rel_path)

        # Update product with image paths
        product["images"] = image_paths

        logger.info(f"Completed processing {name} (Model: {model_number}). Found {len(image_paths)} images.")

    except Exception as e:
        logger.error(f"Error processing product {name} (Model: {model_number}): {str(e)}")
    finally:
        # Close the driver
        try:
            driver.quit()
        except:
            pass

    return product

def main():
    """Main function to process all products."""
    logger.info("Starting image scraping process")

    # Load product data from JSON file
    try:
        with open(JSON_FILE_PATH, 'r', encoding='utf-8') as f:
            data = json.load(f)
            products = data.get("products", [])
            logger.info(f"Loaded {len(products)} products from {JSON_FILE_PATH}")
    except Exception as e:
        logger.error(f"Error loading product data: {str(e)}")
        return

    # Process products with multithreading
    updated_products = []

    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_THREADS) as executor:
        # Submit all products for processing
        future_to_product = {executor.submit(process_product, product): product for product in products}

        # Process results as they complete
        for future in concurrent.futures.as_completed(future_to_product):
            product = future_to_product[future]
            try:
                updated_product = future.result()
                updated_products.append(updated_product)
                logger.info(f"Completed {len(updated_products)}/{len(products)} products")
            except Exception as e:
                logger.error(f"Error processing product: {str(e)}")
                # Add the original product to maintain data integrity
                updated_products.append(product)

    # Update the JSON file with new image paths
    try:
        data["products"] = updated_products
        with open(JSON_FILE_PATH, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2)
        logger.info(f"Updated {JSON_FILE_PATH} with image paths")
    except Exception as e:
        logger.error(f"Error updating JSON file: {str(e)}")

        # Save a backup in case the main file update fails
        try:
            backup_path = JSON_FILE_PATH.replace(".json", "_updated.json")
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2)
            logger.info(f"Saved backup to {backup_path}")
        except Exception as e:
            logger.error(f"Error saving backup file: {str(e)}")

    logger.info("Image scraping process completed")

if __name__ == "__main__":
    main()
