"use client";

import React from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { ChevronRight } from "lucide-react";

interface ProductCategoriesProps {
  categories: any[];
  title: string;
  subtitle?: string;
  accentColor?: "primary" | "secondary" | "tertiary";
}

const ProductCategories: React.FC<ProductCategoriesProps> = ({
  categories,
  title,
  subtitle,
  accentColor = "primary",
}) => {
  // Default categories if none are provided or if the array is empty
  const defaultCategories = [
    {
      id: 1,
      name: "Smart Locks",
      slug: "smart-locks",
      image: "https://placehold.co/400x400/2ECC71/FFFFFF?text=Smart+Locks"
    },
    {
      id: 2,
      name: "Security Cameras",
      slug: "security-cameras",
      image: "https://placehold.co/400x400/3498DB/FFFFFF?text=Security+Cameras"
    },
    {
      id: 3,
      name: "Home Automation",
      slug: "home-automation",
      image: "https://placehold.co/400x400/9B59B6/FFFFFF?text=Home+Automation"
    },
    {
      id: 4,
      name: "Lighting",
      slug: "lighting",
      image: "https://placehold.co/400x400/F1C40F/FFFFFF?text=Lighting"
    },
    {
      id: 5,
      name: "Sensors",
      slug: "sensors",
      image: "https://placehold.co/400x400/E74C3C/FFFFFF?text=Sensors"
    },
    {
      id: 6,
      name: "Alarms",
      slug: "alarms",
      image: "https://placehold.co/400x400/1ABC9C/FFFFFF?text=Alarms"
    }
  ];

  // Use provided categories if available, otherwise use default categories
  // Make sure we have a valid array to work with
  const effectiveCategories = Array.isArray(categories) && categories.length > 0 ? categories : defaultCategories;

  // Generate placeholder images for categories without images
  const categoriesWithImages = effectiveCategories.map((category, index) => {
    // If the category doesn't have an image, use a local placeholder image
    if (!category.image && !category.icon) {
      // Try to use a category-specific image based on slug
      const slug = category.slug || '';
      const categorySlug = slug.toLowerCase().replace(/\s+/g, '-');

      return {
        ...category,
        image: `/assets/products/${categorySlug}.svg`,
        // Add a fallback image path for onError handler
        fallbackImage: `/assets/products/product-placeholder.svg`
      };
    }
    return category;
  });

  // Determine accent color classes
  const accentClasses = {
    primary: {
      bg: "bg-theme-accent-primary/20",
      text: "text-theme-accent-primary",
      line: "bg-theme-accent-primary",
      gradient: "from-theme-accent-primary/5",
      activeBg: "bg-theme-accent-primary",
      activeText: "text-white",
      hoverBg: "hover:bg-theme-accent-primary/10",
    },
    secondary: {
      bg: "bg-theme-accent-secondary/30",
      text: "text-theme-accent-secondary",
      line: "bg-theme-accent-secondary",
      gradient: "from-theme-accent-secondary/5",
      activeBg: "bg-theme-accent-secondary",
      activeText: "text-white",
      hoverBg: "hover:bg-theme-accent-secondary/10",
    },
    tertiary: {
      bg: "bg-theme-accent-primary/30",
      text: "text-theme-accent-primary",
      line: "bg-theme-accent-primary",
      gradient: "from-theme-accent-primary/10",
      activeBg: "bg-theme-accent-primary",
      activeText: "text-white",
      hoverBg: "hover:bg-theme-accent-primary/10",
    },
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };

  // Default category images if none provided
  const defaultCategoryImages = [
    "https://placehold.co/400x400/2ECC71/FFFFFF?text=Category+1",
    "https://placehold.co/400x400/3498DB/FFFFFF?text=Category+2",
    "https://placehold.co/400x400/9B59B6/FFFFFF?text=Category+3",
    "https://placehold.co/400x400/F1C40F/FFFFFF?text=Category+4",
    "https://placehold.co/400x400/E74C3C/FFFFFF?text=Category+5",
    "https://placehold.co/400x400/1ABC9C/FFFFFF?text=Category+6",
  ];

  return (
    <section className="py-12 sm:py-16 md:py-20 relative overflow-hidden w-full">
      {/* Background gradient */}
      <div className={`absolute inset-0 bg-gradient-to-b ${accentClasses[accentColor].gradient} to-theme-homepage z-0 opacity-70`}></div>

      {/* Decorative elements */}
      <div className="absolute top-1/3 right-[15%] w-32 h-32 rounded-full bg-theme-accent-secondary/10 blur-xl opacity-50"></div>
      <div className="absolute bottom-1/3 left-[10%] w-24 h-24 rounded-full bg-theme-accent-primary/10 blur-xl opacity-50"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 w-full max-w-full 2xl:max-w-[1536px]">
        {/* Section header */}
        <div className="flex flex-col items-center mb-8 sm:mb-12">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-theme-text-primary mb-3 relative">
            <span className="relative z-10">{title}</span>
            <span className={`absolute -bottom-1 left-0 right-0 h-3 ${accentClasses[accentColor].bg} transform -rotate-1 z-0`}></span>
          </h2>
          {subtitle && <p className="text-theme-text-primary/70 text-center max-w-2xl mb-4">{subtitle}</p>}
          <div className={`w-16 sm:w-24 h-1 ${accentClasses[accentColor].line} rounded-full mt-1`}></div>
        </div>

        {/* Categories grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true, margin: "-100px" }}
          className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 sm:gap-6"
        >
          {/* Display categories dynamically */}
          {categoriesWithImages.slice(0, 12).map((category, index) => (
            <motion.div
              key={category.id || `category-${index}`}
              variants={itemVariants}
              className="group"
            >
              <Link href={`/products/categories/${category.slug}`}>
                <div className="relative overflow-hidden rounded-xl aspect-square bg-gray-100 shadow-md transition-all duration-300 group-hover:shadow-lg">
                  {/* Category image */}
                  <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/50 opacity-70 z-10"></div>
                  <img
                    src={category.image}
                    alt={category.name}
                    className="absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                    onError={(e) => {
                      // Fallback to category-specific image if available, otherwise use generic placeholder
                      const imgElement = e.target as HTMLImageElement;

                      // Try to use a category-specific placeholder based on category name
                      if (!imgElement.src.includes('product-placeholder')) {
                        const categoryName = category.name.toLowerCase().replace(/\s+/g, '-');
                        const fallbackSrc = `/assets/products/${categoryName}.svg`;

                        // Set a second error handler for the category-specific fallback
                        imgElement.onerror = () => {
                          // If category-specific fallback fails, use generic product placeholder
                          imgElement.src = '/assets/products/product-placeholder.svg';
                          imgElement.onerror = null; // Prevent infinite error loop
                        };

                        imgElement.src = fallbackSrc;
                      }
                    }}
                  />

                  {/* Category name */}
                  <div className="absolute inset-x-0 bottom-0 p-3 z-20">
                    <h3 className="text-white font-medium text-sm sm:text-base text-shadow">{category.name}</h3>
                  </div>

                  {/* Hover overlay */}
                  <div className="absolute inset-0 bg-theme-accent-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"></div>
                </div>
              </Link>
            </motion.div>
          ))}
        </motion.div>

        {/* View all link */}
        <div className="flex justify-center mt-8">
          <Link
            href="/products/categories"
            className={`flex items-center ${accentClasses[accentColor].text} hover:text-theme-accent-hover group transition-all duration-300`}
          >
            <span>View All Categories</span>
            <ChevronRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" />
          </Link>
        </div>
      </div>
    </section>
  );
};

export default ProductCategories;
