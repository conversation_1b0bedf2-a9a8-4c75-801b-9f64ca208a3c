import json
import os
import shutil
from pathlib import Path
from django.core.management.base import BaseCommand
from django.core.files import File
from django.utils.text import slugify
from django.conf import settings
from products.models import Product, Brand, Category, SubCategorie, ProductImage
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Import Haier products from JSON file'

    def add_arguments(self, parser):
        parser.add_argument('json_file', type=str, help='Path to the JSON file containing product data')
        parser.add_argument('--images-dir', type=str, help='Base directory containing product images', default=None)
        parser.add_argument('--skip-existing', action='store_true', help='Skip products that already exist')
        parser.add_argument('--dry-run', action='store_true', help='Perform a dry run without saving to database')

    def handle(self, *args, **options):
        json_file_path = options['json_file']
        images_dir = options['images_dir']
        skip_existing = options['skip_existing']
        dry_run = options['dry_run']

        # If images_dir is not provided, assume it's in the same directory as the JSON file
        if not images_dir:
            json_file_dir = os.path.dirname(json_file_path)
            images_dir = json_file_dir

        # Normalize the paths to handle Windows backslashes
        json_file_path = os.path.normpath(json_file_path)
        images_dir = os.path.normpath(images_dir)

        # Verify the images directory exists
        if not os.path.exists(images_dir):
            self.stdout.write(self.style.ERROR(f'Images directory does not exist: {images_dir}'))
            return

        # Log the absolute paths for clarity
        self.stdout.write(self.style.SUCCESS(f'Starting import from {os.path.abspath(json_file_path)}'))
        self.stdout.write(f'Images directory: {os.path.abspath(images_dir)}')

        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be saved'))

        try:
            with open(json_file_path, 'r', encoding='utf-8') as file:
                products_data = json.load(file)

            self.stdout.write(f'Found {len(products_data)} products in JSON file')

            # Process each product
            products_created = 0
            products_skipped = 0
            images_processed = 0

            for product_data in products_data:
                try:
                    # Process the product
                    created, img_count = self.process_product(
                        product_data,
                        images_dir,
                        skip_existing,
                        dry_run
                    )

                    if created:
                        products_created += 1
                    else:
                        products_skipped += 1

                    images_processed += img_count

                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'Error processing product {product_data.get("name", "Unknown")}: {str(e)}')
                    )
                    logger.exception(f'Error processing product: {product_data.get("name", "Unknown")}')

            self.stdout.write(self.style.SUCCESS(
                f'Import completed: {products_created} products created, '
                f'{products_skipped} products skipped, {images_processed} images processed'
            ))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error importing products: {str(e)}'))
            logger.exception('Error importing products')
            raise

    def process_product(self, product_data, images_dir, skip_existing, dry_run):
        """Process a single product from the JSON data"""
        model_number = product_data.get('model', '')
        name = product_data.get('name', '')

        if not name:
            self.stdout.write(self.style.WARNING(f'Skipping product with no name: {model_number}'))
            return False, 0

        # Check if product already exists
        existing_product = Product.objects.filter(name=name).first()
        if existing_product and skip_existing:
            self.stdout.write(f'Skipping existing product: {name}')
            return False, 0

        # Get or create brand
        brand_name = product_data.get('brand', '')
        brand = None
        if brand_name:
            brand, _ = Brand.objects.get_or_create(
                name=brand_name,
                defaults={'description': f'{brand_name} brand'}
            )

        # Get or create category
        category_name = product_data.get('category', '')
        category = None
        if category_name:
            category, _ = Category.objects.get_or_create(
                name=category_name,
                defaults={
                    'description': f'{category_name} category',
                    'slug': slugify(category_name)
                }
            )

        # Get or create subcategory
        subcategory_name = product_data.get('subcategory', '')
        subcategory = None
        if subcategory_name and category:
            subcategory, _ = SubCategorie.objects.get_or_create(
                name=subcategory_name,
                category=category,
                defaults={
                    'description': f'{subcategory_name} subcategory',
                    'slug': slugify(subcategory_name)
                }
            )

        # Prepare product data
        price = product_data.get('price')
        if price is None:
            # Set a default price if none is provided
            price = Decimal('0.00')
        else:
            price = Decimal(str(price))

        product_slug = slugify(name)
        model_number = product_data.get('model', '')

        # Create or update product
        if existing_product:
            product = existing_product
            # Update product fields
            product.description = product_data.get('description', '')
            product.price = price
            product.brand = brand
            product.category = category
            product.subcategory = subcategory

            if not dry_run:
                product.save()
            self.stdout.write(f'Updated product: {name}')
        else:
            product = Product(
                name=name,
                slug=product_slug,
                description=product_data.get('description', ''),
                price=price,
                brand=brand,
                category=category,
                subcategory=subcategory,
                stock=10  # Default stock value
            )
            if not dry_run:
                product.save()
            self.stdout.write(f'Created product: {name}')

        # Process images
        images = product_data.get('images', [])
        images_processed = 0

        for i, image_path in enumerate(images):
            try:
                if dry_run:
                    self.stdout.write(f'Would process image: {image_path}')
                    images_processed += 1
                    continue

                # Process and save the image
                success = self.process_image(product, image_path, images_dir, i == 0)
                if success:
                    images_processed += 1
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error processing image {image_path} for product {name}: {str(e)}')
                )

        return True, images_processed

    def process_image(self, product, image_path, images_dir, is_primary):
        """Process and save a product image"""
        # Extract the model number and image filename from the path
        # Example: "haier_kitchen_images\\HIH-V90HM-P1\\image_1.jpg"
        parts = image_path.replace('\\\\', '\\').replace('/', '\\').split('\\')

        if len(parts) >= 2:
            # Get the model folder and image filename from the path
            model_folder = parts[-2] if len(parts) >= 2 else ""  # e.g., "HIH-V90HM-P1"
            image_file = parts[-1]    # e.g., "image_1.jpg"

            # Try different possible paths to find the image
            possible_paths = [
                # Direct path as provided in the command
                os.path.join(images_dir, model_folder, image_file),

                # Try with haier_kitchen_images folder
                os.path.join(images_dir, "haier_kitchen_images", model_folder, image_file),

                # Try with the full path from the JSON
                os.path.join(images_dir, *parts),

                # Try with just the filename in case images are all in one directory
                os.path.join(images_dir, image_file)
            ]

            # Find the first path that exists
            full_image_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    full_image_path = path
                    break

            if not full_image_path:
                self.stdout.write(self.style.WARNING(f'Image file not found. Tried paths: {possible_paths}'))
                return False
        else:
            self.stdout.write(self.style.WARNING(f'Invalid image path format: {image_path}'))
            return False

        # Extract filename from path
        filename = os.path.basename(full_image_path)

        # Extract filename for saving
        # The upload_path function in the model will handle the directory structure

        # Create a ProductImage instance
        product_image = ProductImage(
            product=product,
            is_primary=is_primary
        )

        # Log the full image path for debugging
        self.stdout.write(f'Found image at: {full_image_path}')

        try:
            # Open the image file and save it to the model
            with open(full_image_path, 'rb') as img_file:
                # Save the image to the model - let the upload_path function handle the directory structure
                product_image.image.save(
                    filename,
                    File(img_file),
                    save=True
                )

            self.stdout.write(self.style.SUCCESS(f'Saved image: {product_image.image.name} (Primary: {is_primary})'))
            return True
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error saving image {full_image_path}: {str(e)}'))
            logger.exception(f'Error saving image: {full_image_path}')
            return False
