import json
import os
import glob
import shutil

def update_json_with_images():
    # Load the JSON file
    json_file_path = 'output/json/haier_kitchen_products.json'
    backup_file_path = 'output/json/haier_kitchen_products_backup.json'
    print(f"Loading JSON from: {os.path.abspath(json_file_path)}")

    # Create a backup of the original file
    try:
        shutil.copy2(json_file_path, backup_file_path)
        print(f"Created backup at: {backup_file_path}")
    except Exception as e:
        print(f"Warning: Could not create backup: {e}")

    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            products = json.load(f)
        print(f"Successfully loaded JSON with {len(products)} products")
    except Exception as e:
        print(f"Error loading JSON: {e}")
        return

    # Path to the images directory
    images_dir = 'haier_product_images_downloaded'
    print(f"Images directory: {os.path.abspath(images_dir)}")

    # Count of products updated
    updated_count = 0

    # Create a copy of the products list to modify
    updated_products = []

    # Iterate through each product
    for product in products:
        # Create a copy of the product to modify
        updated_product = product.copy()

        model = updated_product.get('model')
        if not model:
            print("Product has no model number, skipping")
            updated_products.append(updated_product)
            continue

        # Look for a folder that starts with the model number
        matching_folders = glob.glob(f"{images_dir}/{model}_*")

        if matching_folders:
            folder_path = matching_folders[0]
            print(f"Found folder for {model}: {folder_path}")

            # Get all image files in the folder
            image_files = []
            for ext in ['*.jpg', '*.jpeg', '*.png']:
                image_files.extend(glob.glob(os.path.join(folder_path, ext)))

            # Sort the image files to ensure consistent order
            image_files.sort()

            # Create relative paths for the images
            relative_paths = [img_path.replace('\\', '/') for img_path in image_files]

            if relative_paths:
                # Update the product's images array
                updated_product['images'] = relative_paths
                updated_count += 1
                print(f"Updated {model} with {len(relative_paths)} images")
                print(f"First image path: {relative_paths[0]}")
            else:
                print(f"No images found for {model} in {folder_path}")
        else:
            print(f"No matching folder found for model: {model}")

        updated_products.append(updated_product)

    # Save the updated JSON back to the original file
    try:
        with open(json_file_path, 'w', encoding='utf-8') as f:
            json.dump(updated_products, f, indent=2)
        print(f"Successfully updated original JSON file: {json_file_path}")

        # Print a sample of the updated JSON to verify
        print("\nSample of updated JSON:")
        for product in updated_products[:2]:
            if 'images' in product and len(product['images']) > 0:
                print(f"Model: {product['model']}")
                print(f"First image: {product['images'][0]}")
                print(f"Total images: {len(product['images'])}")
                print()
    except Exception as e:
        print(f"Error saving JSON: {e}")
        print(f"You can find the backup at: {backup_file_path}")

    print(f"\nTotal products updated: {updated_count} out of {len(products)}")

if __name__ == "__main__":
    update_json_with_images()
