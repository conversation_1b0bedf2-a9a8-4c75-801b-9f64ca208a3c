#!/usr/bin/env python3
"""
Haier Product Image Scraper
Extracts high-quality images from Haier product pages using Playwright
"""

import asyncio
import os
import re
import urllib.parse
from pathlib import Path
from typing import List, Set
import aiohttp
import aiofiles
from playwright.async_api import async_playwright, Page
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('haier_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class HaierImageScraper:
    def __init__(self, output_dir: str = "haier_images"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.session = None
        self.downloaded_images: Set[str] = set()
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    def sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe saving"""
        # Remove invalid characters
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # Limit length
        if len(filename) > 200:
            name, ext = os.path.splitext(filename)
            filename = name[:200-len(ext)] + ext
        return filename

    def extract_product_name(self, url: str) -> str:
        """Extract product name from URL for folder organization"""
        # Extract the product part from URL
        path = urllib.parse.urlparse(url).path
        product_part = path.split('/')[-1].replace('.shtml', '')
        return product_part.replace('-', '_')

    async def download_image(self, img_url: str, product_folder: Path, img_name: str) -> bool:
        """Download a single image"""
        try:
            # Skip if already downloaded
            if img_url in self.downloaded_images:
                return True
                
            async with self.session.get(img_url) as response:
                if response.status == 200:
                    content = await response.read()
                    
                    # Determine file extension
                    content_type = response.headers.get('content-type', '')
                    if 'jpeg' in content_type or 'jpg' in content_type:
                        ext = '.jpg'
                    elif 'png' in content_type:
                        ext = '.png'
                    elif 'webp' in content_type:
                        ext = '.webp'
                    else:
                        # Try to get extension from URL
                        ext = os.path.splitext(urllib.parse.urlparse(img_url).path)[1]
                        if not ext:
                            ext = '.jpg'  # Default
                    
                    filename = self.sanitize_filename(f"{img_name}{ext}")
                    filepath = product_folder / filename
                    
                    # Avoid duplicates
                    counter = 1
                    while filepath.exists():
                        name_part = img_name + f"_{counter}"
                        filename = self.sanitize_filename(f"{name_part}{ext}")
                        filepath = product_folder / filename
                        counter += 1
                    
                    async with aiofiles.open(filepath, 'wb') as f:
                        await f.write(content)
                    
                    self.downloaded_images.add(img_url)
                    logger.info(f"Downloaded: {filename}")
                    return True
                else:
                    logger.warning(f"Failed to download {img_url}: HTTP {response.status}")
                    return False
        except Exception as e:
            logger.error(f"Error downloading {img_url}: {str(e)}")
            return False

    async def extract_images_from_page(self, page: Page, url: str) -> List[str]:
        """Extract all high-quality image URLs from a product page"""
        try:
            await page.goto(url, wait_until='networkidle', timeout=30000)
            await page.wait_for_timeout(2000)  # Wait for dynamic content
            
            # Common selectors for product images on Haier website
            image_selectors = [
                'img[src*="product"]',
                'img[src*="kitchen"]',
                'img[data-src*="product"]',
                'img[data-src*="kitchen"]',
                '.product-image img',
                '.gallery img',
                '.slider img',
                '.carousel img',
                '.product-gallery img',
                '.hero-image img',
                'picture img',
                '[class*="image"] img',
                '[class*="gallery"] img',
                '[id*="image"] img',
                '[id*="gallery"] img'
            ]
            
            image_urls = set()
            
            for selector in image_selectors:
                try:
                    images = await page.query_selector_all(selector)
                    for img in images:
                        # Get src or data-src
                        src = await img.get_attribute('src')
                        data_src = await img.get_attribute('data-src')
                        
                        for url_candidate in [src, data_src]:
                            if url_candidate:
                                # Convert relative URLs to absolute
                                if url_candidate.startswith('//'):
                                    url_candidate = 'https:' + url_candidate
                                elif url_candidate.startswith('/'):
                                    url_candidate = 'https://www.haier.com' + url_candidate
                                elif not url_candidate.startswith('http'):
                                    continue
                                
                                # Filter for high-quality images
                                if self.is_high_quality_image(url_candidate):
                                    image_urls.add(url_candidate)
                except Exception as e:
                    logger.debug(f"Error with selector {selector}: {str(e)}")
                    continue
            
            logger.info(f"Found {len(image_urls)} images on {url}")
            return list(image_urls)
            
        except Exception as e:
            logger.error(f"Error extracting images from {url}: {str(e)}")
            return []

    def is_high_quality_image(self, url: str) -> bool:
        """Filter for high-quality images"""
        url_lower = url.lower()
        
        # Skip small/thumbnail images
        skip_patterns = [
            'thumb', 'small', 'mini', 'icon', 'logo', 'banner',
            '_s.', '_xs.', '_sm.', '_thumb.', '_small.',
            'w=50', 'w=100', 'w=150', 'h=50', 'h=100', 'h=150',
            'size=small', 'size=thumb'
        ]
        
        if any(pattern in url_lower for pattern in skip_patterns):
            return False
        
        # Accept common image formats
        if any(ext in url_lower for ext in ['.jpg', '.jpeg', '.png', '.webp']):
            return True
        
        # Accept URLs that likely contain product images
        if any(keyword in url_lower for keyword in ['product', 'kitchen', 'appliance', 'gallery']):
            return True
        
        return False

    async def scrape_product_page(self, page: Page, url: str) -> int:
        """Scrape images from a single product page"""
        try:
            product_name = self.extract_product_name(url)
            product_folder = self.output_dir / product_name
            product_folder.mkdir(exist_ok=True)
            
            logger.info(f"Scraping: {url}")
            
            image_urls = await self.extract_images_from_page(page, url)
            
            if not image_urls:
                logger.warning(f"No images found on {url}")
                return 0
            
            # Download images concurrently
            download_tasks = []
            for i, img_url in enumerate(image_urls):
                img_name = f"image_{i+1:03d}"
                task = self.download_image(img_url, product_folder, img_name)
                download_tasks.append(task)
            
            results = await asyncio.gather(*download_tasks, return_exceptions=True)
            successful_downloads = sum(1 for result in results if result is True)
            
            logger.info(f"Downloaded {successful_downloads}/{len(image_urls)} images from {product_name}")
            return successful_downloads
            
        except Exception as e:
            logger.error(f"Error scraping {url}: {str(e)}")
            return 0

    async def scrape_all_urls(self, urls: List[str]) -> None:
        """Scrape images from all URLs"""
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            )
            page = await context.new_page()
            
            total_images = 0
            
            for i, url in enumerate(urls, 1):
                logger.info(f"Processing {i}/{len(urls)}: {url}")
                try:
                    images_downloaded = await self.scrape_product_page(page, url)
                    total_images += images_downloaded
                    
                    # Small delay between requests
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    logger.error(f"Failed to process {url}: {str(e)}")
                    continue
            
            await browser.close()
            logger.info(f"Scraping completed! Total images downloaded: {total_images}")

def load_urls_from_file(filename: str) -> List[str]:
    """Load URLs from text file"""
    urls = []
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    # Remove trailing comma if present
                    url = line.rstrip(',')
                    if url.startswith('http'):
                        urls.append(url)
        logger.info(f"Loaded {len(urls)} URLs from {filename}")
        return urls
    except FileNotFoundError:
        logger.error(f"File {filename} not found")
        return []
    except Exception as e:
        logger.error(f"Error reading {filename}: {str(e)}")
        return []

async def main():
    """Main function"""
    urls = load_urls_from_file('urls_haier.txt')
    
    if not urls:
        logger.error("No URLs to process")
        return
    
    async with HaierImageScraper() as scraper:
        await scraper.scrape_all_urls(urls)

if __name__ == "__main__":
    asyncio.run(main())
