# Haier Image Quality Cleaner

This project contains comprehensive scripts to remove low-quality images from directories and update the JSON database accordingly.

## 🎯 Purpose

The image cleaner helps maintain a high-quality image collection by:
- Removing images that are too small (< 800x600 pixels)
- Removing images with inappropriate file sizes (< 50KB or > 10MB)
- Removing images with bad aspect ratios
- Detecting and removing blurry/low-quality images
- Cleaning up corrupted image files
- Updating JSON references automatically

## 📁 Files Overview

### Core Scripts
- **`remove_low_quality_images.py`** - Basic image quality cleaner
- **`advanced_image_cleaner.py`** - Advanced cleaner with detailed analysis
- **`clean_images.py`** - Simple menu-driven interface

### Configuration
- **`image_quality_config.json`** - Quality thresholds and settings

## 🚀 Quick Start

### Option 1: Simple Menu Interface
```bash
python clean_images.py
```

### Option 2: Basic Cleaner
```bash
python remove_low_quality_images.py
```

### Option 3: Advanced Cleaner
```bash
python advanced_image_cleaner.py --interactive
```

## 📊 Test Results

Based on the dry run test:
- **Total images analyzed**: 479
- **Low-quality images found**: 414 (86.4%)
- **Main issues detected**:
  - Small file sizes (< 50KB)
  - Small dimensions (< 800x600)
  - Bad aspect ratios
  - Placeholder images (1KB files)

## ⚙️ Quality Thresholds

### Default Settings
```json
{
  "min_width": 800,
  "min_height": 600,
  "min_file_size": 50000,     // 50KB
  "max_file_size": 10000000,  // 10MB
  "min_aspect_ratio": 0.5,
  "max_aspect_ratio": 2.0
}
```

### Common Issues Found
1. **Tiny placeholder images**: 1.164KB PNG files
2. **Small thumbnails**: 480x480, 600x540 dimensions
3. **Banner images**: 1680x300 (bad aspect ratio)
4. **Undersized files**: < 50KB (likely compressed/low quality)

## 🔧 Usage Options

### 1. Dry Run (Recommended First)
```bash
python remove_low_quality_images.py
# Choose 'y' for dry run to preview changes
```

### 2. Clean Specific Directory
```bash
python advanced_image_cleaner.py --directory "haier_product_images_downloaded"
```

### 3. Command Line Options
```bash
# Dry run mode
python advanced_image_cleaner.py --dry-run

# Custom config file
python advanced_image_cleaner.py --config custom_config.json

# Interactive mode
python advanced_image_cleaner.py --interactive
```

## 📈 What Gets Cleaned

### Directories Processed
- `haier_product_images_downloaded/`
- `haier_product_images_hq/`

### JSON Fields Updated
- `images[]` - Regular product images
- `hq_images[]` - High-quality images

### Quality Checks
1. **File Size**: 50KB - 10MB range
2. **Dimensions**: Minimum 800x600 pixels
3. **Aspect Ratio**: 0.5 - 2.0 range
4. **Blur Detection**: Laplacian variance analysis
5. **Corruption Check**: File readability test

## 🛡️ Safety Features

### Automatic Backups
- Creates timestamped backups before modifying JSON
- Backup location: `backups/haier_products_backup_YYYYMMDD_HHMMSS.json`

### Progress Saving
- Saves progress every 5 products processed
- Resumable if interrupted

### Dry Run Mode
- Preview all changes before execution
- No files deleted in dry run mode
- Detailed statistics provided

## 📊 Expected Results

Based on your current collection:
- **~414 low-quality images** will be removed
- **~65 high-quality images** will remain
- **Significant space savings** (estimated 15-20MB freed)
- **Cleaner JSON structure** with only quality image references

## 🔍 Detailed Analysis

### File Size Issues
- Many images are 15-45KB (too small for quality)
- Placeholder images are only 1.164KB
- Some images are just 2KB (likely broken)

### Dimension Issues
- Common problematic sizes: 480x480, 600x540, 650x650
- Banner images: 1680x300 (wrong aspect ratio)
- Target minimum: 800x600 for quality display

### Quality Detection
- Blur detection using Laplacian variance
- Color analysis for placeholder detection
- Brightness analysis for over/under-exposed images

## 🎛️ Customization

### Modify Quality Thresholds
Edit `image_quality_config.json`:
```json
{
  "quality_thresholds": {
    "min_width": 1000,        // Stricter width requirement
    "min_height": 800,        // Stricter height requirement
    "min_file_size": 100000   // 100KB minimum
  }
}
```

### Add Custom Directories
```json
{
  "directories_to_clean": [
    "haier_product_images_downloaded",
    "haier_product_images_hq",
    "custom_image_directory"
  ]
}
```

## 📋 Step-by-Step Workflow

1. **Backup Current State**
   ```bash
   cp output/json/haier_kitchen_products.json output/json/haier_kitchen_products_manual_backup.json
   ```

2. **Run Dry Run**
   ```bash
   python remove_low_quality_images.py
   # Choose 'y' for dry run
   ```

3. **Review Results**
   - Check the statistics
   - Verify the types of images being removed

4. **Execute Cleanup**
   ```bash
   # Continue from dry run or run again
   python remove_low_quality_images.py
   # Choose 'n' for dry run, then 'y' to proceed
   ```

5. **Verify Results**
   - Check remaining images in directories
   - Verify JSON file updates
   - Test image loading in your application

## 🚨 Important Notes

### Before Running
- **Backup your data** - The script creates backups but manual backup is recommended
- **Test with dry run** - Always preview changes first
- **Check disk space** - Ensure enough space for backups

### After Running
- **Verify image loading** - Test that remaining images load correctly
- **Check JSON integrity** - Ensure JSON file is valid
- **Update applications** - Refresh any cached image references

## 🔧 Troubleshooting

### Common Issues
1. **Permission errors**: Run with appropriate permissions
2. **Memory issues**: Process large directories in batches
3. **JSON corruption**: Restore from backup if needed

### Recovery
```bash
# Restore from automatic backup
cp backups/haier_products_backup_YYYYMMDD_HHMMSS.json output/json/haier_kitchen_products.json

# Restore from manual backup
cp output/json/haier_kitchen_products_manual_backup.json output/json/haier_kitchen_products.json
```

## 📞 Support

If you encounter issues:
1. Check the console output for error messages
2. Verify file permissions
3. Ensure sufficient disk space
4. Try running with `--dry-run` first
5. Check the backup files if recovery is needed

The image cleaner will significantly improve your image collection quality while maintaining data integrity through comprehensive backup and safety features.
