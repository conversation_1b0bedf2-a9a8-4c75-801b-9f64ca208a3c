"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/embla-carousel-autoplay";
exports.ids = ["vendor-chunks/embla-carousel-autoplay"];
exports.modules = {

/***/ "(ssr)/./node_modules/embla-carousel-autoplay/esm/embla-carousel-autoplay.esm.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/embla-carousel-autoplay/esm/embla-carousel-autoplay.esm.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Autoplay)\n/* harmony export */ });\nconst defaultOptions = {\n  active: true,\n  breakpoints: {},\n  delay: 4000,\n  jump: false,\n  playOnInit: true,\n  stopOnFocusIn: true,\n  stopOnInteraction: true,\n  stopOnMouseEnter: false,\n  stopOnLastSnap: false,\n  rootNode: null\n};\n\nfunction normalizeDelay(emblaApi, delay) {\n  const scrollSnaps = emblaApi.scrollSnapList();\n  if (typeof delay === 'number') {\n    return scrollSnaps.map(() => delay);\n  }\n  return delay(scrollSnaps, emblaApi);\n}\nfunction getAutoplayRootNode(emblaApi, rootNode) {\n  const emblaRootNode = emblaApi.rootNode();\n  return rootNode && rootNode(emblaRootNode) || emblaRootNode;\n}\n\nfunction Autoplay(userOptions = {}) {\n  let options;\n  let emblaApi;\n  let destroyed;\n  let delay;\n  let timerStartTime = null;\n  let timerId = 0;\n  let autoplayActive = false;\n  let mouseIsOver = false;\n  let playOnDocumentVisible = false;\n  let jump = false;\n  function init(emblaApiInstance, optionsHandler) {\n    emblaApi = emblaApiInstance;\n    const {\n      mergeOptions,\n      optionsAtMedia\n    } = optionsHandler;\n    const optionsBase = mergeOptions(defaultOptions, Autoplay.globalOptions);\n    const allOptions = mergeOptions(optionsBase, userOptions);\n    options = optionsAtMedia(allOptions);\n    if (emblaApi.scrollSnapList().length <= 1) return;\n    jump = options.jump;\n    destroyed = false;\n    delay = normalizeDelay(emblaApi, options.delay);\n    const {\n      eventStore,\n      ownerDocument\n    } = emblaApi.internalEngine();\n    const isDraggable = !!emblaApi.internalEngine().options.watchDrag;\n    const root = getAutoplayRootNode(emblaApi, options.rootNode);\n    eventStore.add(ownerDocument, 'visibilitychange', visibilityChange);\n    if (isDraggable) {\n      emblaApi.on('pointerDown', pointerDown);\n    }\n    if (isDraggable && !options.stopOnInteraction) {\n      emblaApi.on('pointerUp', pointerUp);\n    }\n    if (options.stopOnMouseEnter) {\n      eventStore.add(root, 'mouseenter', mouseEnter);\n    }\n    if (options.stopOnMouseEnter && !options.stopOnInteraction) {\n      eventStore.add(root, 'mouseleave', mouseLeave);\n    }\n    if (options.stopOnFocusIn) {\n      emblaApi.on('slideFocusStart', stopAutoplay);\n    }\n    if (options.stopOnFocusIn && !options.stopOnInteraction) {\n      eventStore.add(emblaApi.containerNode(), 'focusout', startAutoplay);\n    }\n    if (options.playOnInit) startAutoplay();\n  }\n  function destroy() {\n    emblaApi.off('pointerDown', pointerDown).off('pointerUp', pointerUp).off('slideFocusStart', stopAutoplay);\n    stopAutoplay();\n    destroyed = true;\n    autoplayActive = false;\n  }\n  function setTimer() {\n    const {\n      ownerWindow\n    } = emblaApi.internalEngine();\n    ownerWindow.clearTimeout(timerId);\n    timerId = ownerWindow.setTimeout(next, delay[emblaApi.selectedScrollSnap()]);\n    timerStartTime = new Date().getTime();\n    emblaApi.emit('autoplay:timerset');\n  }\n  function clearTimer() {\n    const {\n      ownerWindow\n    } = emblaApi.internalEngine();\n    ownerWindow.clearTimeout(timerId);\n    timerId = 0;\n    timerStartTime = null;\n    emblaApi.emit('autoplay:timerstopped');\n  }\n  function startAutoplay() {\n    if (destroyed) return;\n    if (documentIsHidden()) {\n      playOnDocumentVisible = true;\n      return;\n    }\n    if (!autoplayActive) emblaApi.emit('autoplay:play');\n    setTimer();\n    autoplayActive = true;\n  }\n  function stopAutoplay() {\n    if (destroyed) return;\n    if (autoplayActive) emblaApi.emit('autoplay:stop');\n    clearTimer();\n    autoplayActive = false;\n  }\n  function visibilityChange() {\n    if (documentIsHidden()) {\n      playOnDocumentVisible = autoplayActive;\n      return stopAutoplay();\n    }\n    if (playOnDocumentVisible) startAutoplay();\n  }\n  function documentIsHidden() {\n    const {\n      ownerDocument\n    } = emblaApi.internalEngine();\n    return ownerDocument.visibilityState === 'hidden';\n  }\n  function pointerDown() {\n    if (!mouseIsOver) stopAutoplay();\n  }\n  function pointerUp() {\n    if (!mouseIsOver) startAutoplay();\n  }\n  function mouseEnter() {\n    mouseIsOver = true;\n    stopAutoplay();\n  }\n  function mouseLeave() {\n    mouseIsOver = false;\n    startAutoplay();\n  }\n  function play(jumpOverride) {\n    if (typeof jumpOverride !== 'undefined') jump = jumpOverride;\n    startAutoplay();\n  }\n  function stop() {\n    if (autoplayActive) stopAutoplay();\n  }\n  function reset() {\n    if (autoplayActive) startAutoplay();\n  }\n  function isPlaying() {\n    return autoplayActive;\n  }\n  function next() {\n    const {\n      index\n    } = emblaApi.internalEngine();\n    const nextIndex = index.clone().add(1).get();\n    const lastIndex = emblaApi.scrollSnapList().length - 1;\n    const kill = options.stopOnLastSnap && nextIndex === lastIndex;\n    if (emblaApi.canScrollNext()) {\n      emblaApi.scrollNext(jump);\n    } else {\n      emblaApi.scrollTo(0, jump);\n    }\n    emblaApi.emit('autoplay:select');\n    if (kill) return stopAutoplay();\n    startAutoplay();\n  }\n  function timeUntilNext() {\n    if (!timerStartTime) return null;\n    const currentDelay = delay[emblaApi.selectedScrollSnap()];\n    const timePastSinceStart = new Date().getTime() - timerStartTime;\n    return currentDelay - timePastSinceStart;\n  }\n  const self = {\n    name: 'autoplay',\n    options: userOptions,\n    init,\n    destroy,\n    play,\n    stop,\n    reset,\n    isPlaying,\n    timeUntilNext\n  };\n  return self;\n}\nAutoplay.globalOptions = undefined;\n\n\n//# sourceMappingURL=embla-carousel-autoplay.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/embla-carousel-autoplay/esm/embla-carousel-autoplay.esm.js\n");

/***/ })

};
;