import json
import os
import requests
import re
import time
from urllib.parse import urljoin
from bs4 import BeautifulSoup

def create_folder(folder_path):
    """Create folder if it doesn't exist"""
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
        print(f"Created folder: {folder_path}")

def sanitize_filename(filename):
    """Remove invalid characters from filename"""
    return re.sub(r'[\\/*?:"<>|]', "", filename)

def download_image(url, save_path):
    """Download image from URL and save to specified path"""
    try:
        response = requests.get(url, stream=True, timeout=10)
        if response.status_code == 200:
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(1024):
                    f.write(chunk)
            print(f"Downloaded: {save_path}")
            return True
        else:
            print(f"Failed to download {url}, status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"Error downloading {url}: {e}")
        return False

def extract_product_images(product_url, product_model, product_name):
    """Visit product page and extract image URLs using BeautifulSoup"""
    try:
        print(f"Visiting: {product_url}")
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(product_url, headers=headers, timeout=15)

        if response.status_code != 200:
            print(f"Failed to access {product_url}, status code: {response.status_code}")
            return []

        soup = BeautifulSoup(response.text, 'html.parser')

        # Look for image elements - adjust selectors based on the actual website structure
        image_elements = []

        # Try different possible image containers
        # Product gallery images
        gallery_images = soup.select('.product-gallery img, .product-images img, .gallery img')
        if gallery_images:
            image_elements.extend(gallery_images)

        # Main product image
        main_image = soup.select('.product-image img, .main-image img, .product-img img')
        if main_image:
            image_elements.extend(main_image)

        # Banner images that might contain product
        banner_images = soup.select('.product-banner img')
        if banner_images:
            image_elements.extend(banner_images)

        # If still no images found, try a more generic approach
        if not image_elements:
            # Look for any img with src containing the product model or with alt text containing product name
            all_images = soup.find_all('img')
            for img in all_images:
                src = img.get('src', '')
                alt = img.get('alt', '')
                if product_model.lower() in src.lower() or product_model.lower() in alt.lower() or \
                   product_name.lower() in alt.lower():
                    image_elements.append(img)

        # If still no images, get all images from the page that are reasonably sized
        if not image_elements:
            all_images = soup.find_all('img')
            for img in all_images:
                width = img.get('width')
                height = img.get('height')
                if width and height and int(width) > 100 and int(height) > 100:
                    image_elements.append(img)

        # Extract image URLs
        image_urls = []
        for img in image_elements:
            src = img.get('src')
            if src:
                # Convert relative URLs to absolute
                full_url = urljoin(product_url, src)
                # Filter out small icons, logos, etc.
                if not any(x in full_url.lower() for x in ['icon', 'logo', 'button']):
                    image_urls.append(full_url)

        return image_urls

    except Exception as e:
        print(f"Error processing {product_url}: {e}")
        return []

def main():
    # Create base output directory
    output_dir = "haier_product_images"
    create_folder(output_dir)

    # Load JSON file
    with open('output/json/haier_kitchen_products.json', 'r') as f:
        products = json.load(f)

    print(f"Found {len(products)} products in the JSON file")

    # Process each product
    for i, product in enumerate(products):
        product_name = product.get('name', 'Unknown')
        product_model = product.get('model', 'Unknown')

        # Create sanitized folder name
        folder_name = sanitize_filename(f"{product_model}_{product_name}")
        product_dir = os.path.join(output_dir, folder_name)
        create_folder(product_dir)

        # Get product URL from images array
        product_urls = product.get('images', [])
        if not product_urls:
            print(f"No URL found for product: {product_name} ({product_model})")
            continue

        # Process each URL for the product
        for product_url in product_urls:
            # Skip empty URLs
            if not product_url:
                continue

            # Extract images from product page using BeautifulSoup
            image_urls = extract_product_images(product_url, product_model, product_name)

            # Download each image
            for img_index, img_url in enumerate(image_urls):
                # Create filename with product info
                file_extension = os.path.splitext(img_url.split('?')[0])[1]
                if not file_extension:
                    file_extension = '.jpg'  # Default extension

                filename = f"{product_model}_{img_index+1}{file_extension}"
                save_path = os.path.join(product_dir, filename)

                # Download the image
                download_image(img_url, save_path)

            print(f"Processed {len(image_urls)} images for {product_name} ({product_model})")

            # Add a small delay to avoid overwhelming the server
            time.sleep(2)

        print(f"Completed {i+1}/{len(products)}: {product_name}")

if __name__ == "__main__":
    main()
