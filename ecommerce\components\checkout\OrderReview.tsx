import { MAIN_URL } from "@/constant/urls";
import { Separator } from "../../components/ui/separator";
import { Label } from "../ui/label";
import { Button } from "../ui/button";
import { useState } from "react";
import { MapPin, Mail, Phone, Truck, CreditCard, ShoppingBag } from "lucide-react";

interface OrderReviewProps {
  deliveryInfo: {
    order_user_email: string;
    order_user_phone: string;
    street_address: string;
    city: string;
    state: string;
    postal_code: string;
  };
  shippingMethod: any;
  paymentInfo: {
    paymentMethod: string;
  };
  items: any;
  orderDetails: any;
  handlePlaceOrder: any;
}

export const OrderReview = ({
  deliveryInfo,
  shippingMethod,
  paymentInfo,
  items,
  orderDetails,
  handlePlaceOrder,
}: OrderReviewProps) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleOrder = async () => {
    setIsLoading(true);
    try {
      await handlePlaceOrder();
    } finally {
      // In case there's an error and we don't redirect
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      <h2 className="text-2xl font-semibold text-center mb-6 gradient-text">Review Your Order</h2>

      {/* Order Items */}
      <div className="space-y-4">
        {Array.isArray(items) &&
          items.map((item: any) => (
            <div
              key={item?.id}
              className="flex flex-col sm:flex-row items-start sm:items-center gap-4 p-5 border rounded-lg shadow-sm hover:shadow-md transition-all duration-300 bg-white"
            >
              <div className="flex-1 space-y-2">
                <h3 className="font-semibold text-lg">{item?.product?.name}</h3>
                <p className="font-medium text-primary">₹{item?.line_total}</p>
              </div>
              <span className="px-3 py-1 bg-secondary rounded-full text-sm">
                Quantity: {item?.quantity}
              </span>
            </div>
          ))}

        {/* Order Summary */}
        <div className="mt-6 p-5 border rounded-lg shadow-sm bg-white">
          <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
            <ShoppingBag className="h-5 w-5 text-primary" />
            Order Summary
          </h3>
          <div className="space-y-2 divide-y">
            <div className="flex justify-between py-2">
              <span className="text-muted-foreground">Subtotal (before GST)</span>
              <span className="font-medium">₹{orderDetails?.subtotal}</span>
            </div>

            {/* GST Breakdown */}
            <div className="py-2 space-y-1">
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  GST ({orderDetails?.gst_amount ? 'Dynamic' : '18%'})
                </span>
                <span className="font-medium">
                  ₹{(Number(orderDetails?.gst_amount || 0) || Number(orderDetails?.subtotal || 0) * 0.18).toFixed(2)}
                </span>
              </div>
              <div className="ml-4 space-y-1 text-sm">
                {Number(orderDetails?.igst_amount || 0) > 0 ? (
                  <div className="flex justify-between text-gray-500">
                    <span>IGST</span>
                    <span>₹{Number(orderDetails?.igst_amount || 0).toFixed(2)}</span>
                  </div>
                ) : (
                  <>
                    <div className="flex justify-between text-gray-500">
                      <span>CGST</span>
                      <span>₹{(Number(orderDetails?.cgst_amount || 0) || Number(orderDetails?.subtotal || 0) * 0.09).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-gray-500">
                      <span>SGST</span>
                      <span>₹{(Number(orderDetails?.sgst_amount || 0) || Number(orderDetails?.subtotal || 0) * 0.09).toFixed(2)}</span>
                    </div>
                  </>
                )}
              </div>
            </div>

            {orderDetails?.promo_discount && (
              <div className="flex justify-between py-2">
                <span className="text-muted-foreground">Discount</span>
                <span className="font-medium text-green-600">-₹{orderDetails?.promo_discount}</span>
              </div>
            )}

            <div className="flex justify-between py-2">
              <span className="text-muted-foreground">Shipping</span>
              <span className="font-medium">₹{orderDetails?.shipping_cost}</span>
            </div>

            <div className="flex justify-between py-2 bg-blue-50 px-3 rounded-md">
              <span className="font-bold">Total Amount</span>
              <span className="font-bold text-lg text-primary">₹{orderDetails?.total}</span>
            </div>
          </div>

          {/* GST Compliance Note */}
          <div className="mt-3 text-xs text-gray-500 text-center">
            <p>* GST-compliant invoice will be generated after payment</p>
          </div>
        </div>
      </div>

      {/* Delivery Information */}
      <div className="p-5 border rounded-lg shadow-sm bg-white">
        <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
          <MapPin className="h-5 w-5 text-primary" />
          Delivery Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <Mail className="h-4 w-4 text-muted-foreground" />
            <span>{deliveryInfo.order_user_email}</span>
          </div>
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4 text-muted-foreground" />
            <span>{deliveryInfo.order_user_phone}</span>
          </div>
          <div className="flex items-center gap-2 col-span-full">
            <MapPin className="h-4 w-4 text-muted-foreground" />
            <span>{deliveryInfo.street_address}</span>
          </div>
          <div className="flex items-center gap-2 col-span-full">
            <span className="ml-6">{deliveryInfo.city}, {deliveryInfo.state} {deliveryInfo.postal_code}</span>
          </div>
        </div>
      </div>

      <Separator className="my-2" />

      {/* Shipping Method */}
      <div className="p-5 border rounded-lg shadow-sm bg-white">
        <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
          <Truck className="h-5 w-5 text-primary" />
          Shipping Method
        </h3>
        <div className="p-3 bg-secondary/50 rounded-md">
          <div className="flex justify-between items-center">
            <div>
              <div className="font-medium">{shippingMethod?.name}</div>
              <div className="text-sm text-muted-foreground">
                {shippingMethod?.description}
              </div>
            </div>
            <div className="font-medium text-primary">
              ₹{Number(shippingMethod?.price).toFixed(2)}
            </div>
          </div>
        </div>
      </div>

      <Separator className="my-2" />

      {/* Payment Method */}
      <div className="p-5 border rounded-lg shadow-sm bg-white">
        <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
          <CreditCard className="h-5 w-5 text-primary" />
          Payment Method
        </h3>
        <div className="p-3 bg-blue-50 rounded-md flex items-center justify-between">
          <div>
            <div className="font-medium">PhonePe</div>
            <div className="text-sm text-muted-foreground">
              You'll be redirected to complete payment
            </div>
          </div>
          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
              <path d="M5 12h14"></path>
              <path d="M12 5v14"></path>
            </svg>
          </div>
        </div>
      </div>

      {/* Place Order Button */}
      <Button
        className="w-full md:w-1/2 mx-auto flex justify-center items-center py-6 mt-6 shadow-md hover:shadow-lg transition-all duration-300"
        onClick={handleOrder}
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <div className="w-5 h-5 border-t-2 border-b-2 border-white rounded-full animate-spin mr-2"></div>
            Processing...
          </>
        ) : (
          "Place Order"
        )}
      </Button>

      {/* Terms */}
      <div className="bg-muted p-4 rounded-lg text-center">
        <p className="text-sm text-muted-foreground">
          By placing your order, you agree to our Terms of Service and Privacy
          Policy. Your payment information is processed securely.
        </p>
      </div>
    </div>
  );
};
