"use client";
import {
  ChevronDown,
  Menu,
  Search,
  ShoppingCart,
  Trash2,
  User,
  X,
} from "lucide-react";
import React from "react";
import CartMenu from "./CartManu";
import MyAccountMenu from "./MyAccountMenu";
import CategoriesMenu from "./CategoriesMenu";
import Link from "next/link";
import Image from "next/image";
import { Button } from "../ui/button";
import { SearchBtn } from "./SearchBtn";
import { useSession } from "next-auth/react";

const NavBar = () => {
  const { status } = useSession();
  const [isOpen, setIsOpen] = React.useState({
    humburgar: false,
    cart: false,
    search: false,
    account: false,
  });

  const quickLinks = [
    { text: "Shop", href: "/shop" },
    { text: "Cart", href: "/cart" },
    { text: "Account", href: "/account" },
    { text: "Categories", href: "/categories" },
  ];

  return (
    <nav className="sticky top-0 z-50 bg-theme-header text-white backdrop-blur-sm shadow-md w-full">
      <div className="w-full py-3 px-4 lg:px-8">
        <div className="flex items-center justify-between max-w-screen-2xl mx-auto">
          <div className="flex items-center space-x-8">
            <div className="shrink-0">
              <Link href="/" className="flex items-center gap-2">
                <Image
                  src="/logotriumph.png"
                  alt="Triumph Enterprises Logo"
                  width={32}
                  height={32}
                  className="h-8 w-auto"
                  priority
                />
                <span className="text-[10px] md:text-xl lg:text-2xl font-bold text-white">TRIUMPH ENTERPRISES</span>
              </Link>
            </div>
            <div className="hidden md:flex space-x-6">
              <Link href="/shop" className="text-white hover:text-theme-accent-primary font-medium transition-colors relative group">
                Shop
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-theme-accent-secondary transition-all duration-300 group-hover:w-full"></span>
              </Link>
              {/* <Link href="/categories" className="text-gray-700 hover:text-indigo-600 font-medium transition-colors">
                Categories
              </Link> */}
            </div>
          </div>

          <div className="flex items-center gap-1 lg:gap-3">
            <div className="">
              <Button
                variant="ghost"
                onClick={() => {
                  setIsOpen((val) => {
                    return { ...val, search: !val.search };
                  });
                }}
                className="inline-flex items-center rounded-full justify-center p-2 hover:bg-white/10 text-sm font-medium leading-none text-white"
              >
                <Search className="h-5 w-5" />
              </Button>

              {isOpen.search && (
                <div className="z-50 backdrop-blur-lg bg-black/50 fixed top-0 left-0 h-screen w-full flex justify-center items-center overflow-y-scroll">
                  <X
                    onClick={() => setIsOpen({ ...isOpen, search: false })}
                    className="absolute top-4 right-4 cursor-pointer text-white hover:text-theme-accent-primary text-2xl"
                  />
                  <SearchBtn />
                </div>
              )}
            </div>
            <div className="relative">
              <Button
                variant="ghost"
                onClick={() => {
                  if (status === "authenticated") {
                    setIsOpen((val) => {
                      return { ...val, cart: !val.cart };
                    });
                  } else {
                    // Redirect to login if not authenticated
                    window.location.href = "/auth/login?callbackUrl=/cart";
                  }
                }}
                className="inline-flex items-center rounded-full justify-center p-2 hover:bg-white/10 text-sm font-medium leading-none text-white"
              >
                <ShoppingCart className="w-5 h-5" />
              </Button>

              {isOpen.cart && status === "authenticated" && (
                <div
                  onMouseLeave={() => setIsOpen({ ...isOpen, cart: false })}
                  className="fixed md:absolute h-auto max-h-[calc(100vh-150px)] overflow-y-auto top-[60px] md:top-[45px] right-0 md:right-0 z-50 w-full md:w-auto max-w-full md:max-w-md"
                >
                  <div className="p-2">
                    <CartMenu key={`cart-menu-${isOpen.cart}`} />
                  </div>
                </div>
              )}
            </div>
            <div className="relative">
              <Button
                variant="ghost"
                onClick={() =>
                  setIsOpen({ ...isOpen, account: !isOpen.account })
                }
                className="inline-flex items-center rounded-full justify-center p-2 hover:bg-white/10 text-sm font-medium leading-none text-white"
              >
                <User className="w-5 h-5" />
              </Button>

              {isOpen.account && (
                <div
                  onMouseLeave={() => setIsOpen({ ...isOpen, account: false })}
                  id="userDropdown1"
                  className="fixed md:absolute top-[60px] md:top-[45px] right-0 md:right-0 z-50 w-full md:w-auto max-w-full md:max-w-md shadow-md bg-white rounded-md"
                >
                  <MyAccountMenu />
                </div>
              )}
            </div>
            <Button
              variant="ghost"
              type="button"
              onClick={() =>
                setIsOpen({ ...isOpen, humburgar: !isOpen.humburgar })
              }
              data-collapse-toggle="ecommerce-navbar-menu-1"
              aria-controls="ecommerce-navbar-menu-1"
              aria-expanded="false"
              className="md:hidden inline-flex items-center justify-center hover:bg-white/10 rounded-full p-2 text-white"
            >
              {isOpen.humburgar ? (
                <X className="w-5 h-5" />
              ) : (
                <Menu className="w-5 h-5" />
              )}
            </Button>
          </div>
        </div>

        {isOpen.humburgar && (
          <div
            onMouseLeave={() => setIsOpen({ ...isOpen, humburgar: false })}
            className="w-full bg-theme-header text-white absolute top-[60px] right-0 z-10 border-t border-gray-700/30 shadow-lg py-4 px-6 mt-1"
          >
            <div className="block md:hidden mb-6">
              <CategoriesMenu />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <Link href="/shop" className="flex items-center space-x-2 p-2 rounded-md hover:bg-white/10">
                <div className="w-8 h-8 bg-theme-accent-primary/20 rounded-full flex items-center justify-center">
                  <ShoppingCart className="h-4 w-4 text-theme-accent-primary" />
                </div>
                <span className="font-medium text-white">Shop</span>
              </Link>
              <Link href="/categories" className="flex items-center space-x-2 p-2 rounded-md hover:bg-white/10">
                <div className="w-8 h-8 bg-theme-accent-secondary/20 rounded-full flex items-center justify-center">
                  <ChevronDown className="h-4 w-4 text-theme-accent-secondary" />
                </div>
                <span className="font-medium text-white">Categories</span>
              </Link>
              <Link href="/cart" className="flex items-center space-x-2 p-2 rounded-md hover:bg-white/10">
                <div className="w-8 h-8 bg-theme-accent-primary/20 rounded-full flex items-center justify-center">
                  <ShoppingCart className="h-4 w-4 text-theme-accent-primary" />
                </div>
                <span className="font-medium text-white">Cart</span>
              </Link>
              <Link href="/account" className="flex items-center space-x-2 p-2 rounded-md hover:bg-white/10">
                <div className="w-8 h-8 bg-theme-accent-secondary/20 rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-theme-accent-secondary" />
                </div>
                <span className="font-medium text-white">Account</span>
              </Link>
            </div>
            <div className="mt-6 pt-4 border-t border-gray-700/30">
              <ul className="flex flex-wrap gap-4">
                {quickLinks.map(({ text, href }) => (
                  <li key={text}>
                    <Link
                      href={href}
                      className="text-sm text-gray-300 hover:text-theme-accent-primary transition-colors duration-300"
                    >
                      {text}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default NavBar;
