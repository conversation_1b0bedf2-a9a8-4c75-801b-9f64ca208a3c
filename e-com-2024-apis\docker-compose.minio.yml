version: '3.8'

services:
  # MinIO service for object storage
  minio:
    image: minio/minio:latest
    container_name: triumph-minio
    ports:
      - "9000:9000"  # API port
      - "9001:9001"  # Console port
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    restart: unless-stopped
    networks:
      - triumph-network

  # Create buckets and set policies
  minio-setup:
    image: minio/mc:latest
    container_name: triumph-minio-setup
    depends_on:
      - minio
    entrypoint: >
      /bin/sh -c "
      sleep 10;
      /usr/bin/mc config host add myminio http://minio:9000 minioadmin minioadmin;
      /usr/bin/mc mb --ignore-existing myminio/media;
      /usr/bin/mc policy set public myminio/media;
      exit 0;
      "
    networks:
      - triumph-network

  # Django application
  django:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: triumph-django
    ports:
      - "8000:8000"
    environment:
      - DEBUG=1
      - DB_NAME=postgres
      - DB_USER=postgres
      - DB_PASSWORD=postgres
      - DB_HOST=db
      - DB_PORT=5432
      - USE_MINIO=true
      - MINIO_STORAGE_ENDPOINT=minio:9000
      - MINIO_STORAGE_ACCESS_KEY=minioadmin
      - MINIO_STORAGE_SECRET_KEY=minioadmin
      - MINIO_STORAGE_USE_HTTPS=false
      - MINIO_STORAGE_MEDIA_BUCKET_NAME=media
      - MINIO_STORAGE_AUTO_CREATE_MEDIA_BUCKET=true
    depends_on:
      - db
      - minio
    volumes:
      - .:/app
    command: >
      sh -c "python manage.py migrate &&
             python manage.py runserver 0.0.0.0:8000"
    networks:
      - triumph-network

  # PostgreSQL database
  db:
    image: postgres:14
    container_name: triumph-postgres
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - triumph-network

volumes:
  postgres_data:
  minio_data:

networks:
  triumph-network:
    driver: bridge
