import { MAIN_URL, PRODUCTS } from "@/constant/urls";
import { useNewApi } from "@/hooks/useNewApi";
import { Search } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import ProductInfiniteScrolling from "../product/ProductInfiniteScrolling";
import { useInfiniteScroll } from "@/hooks/useInfiniteScroll";

export const SearchBtn = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentQuery, setCurrentQuery] = useState("");
  const { page, loading, setLoading, setHasMore } =
    useInfiniteScroll();

  const [products, setProducts] = useState<any[]>([]);
  const { get } = useNewApi({
    baseURL: MAIN_URL,
  });
  const observerTarget = useRef<HTMLDivElement>(null);

  const handleProductSearch = async (isNewSearch = false) => {
    setLoading(true);
    let url = `${PRODUCTS}?search=${currentQuery}&page=${page}`;
    const response: any = await get(url);
    if (response.data) {
      const data = response.data;
      if (data?.next) {
        setHasMore(true);
      } else {
        setHasMore(false);
      }
      if (Boolean(data?.results?.length > 0)) {
        if (isNewSearch) {
          // Replace products for new searches
          setProducts(data?.results);
        } else {
          // Append products for pagination
          setProducts((prev) => {
            // Create a Set of existing product IDs to avoid duplicates
            const existingIds = new Set(prev.map((product: any) => product.id));
            // Filter out any products that already exist in the array
            const newProducts = data?.results.filter((product: any) => !existingIds.has(product.id));
            return [...prev, ...newProducts];
          });
        }
      } else if (isNewSearch) {
        // Clear products if no results for a new search
        setProducts([]);
      }

      setLoading(false);
    }
    if (response.error) {
      // Handle error
      setLoading(false);
    }
  };

  // Effect for handling search query changes
  useEffect(() => {
    // If query is empty, clear results and exit
    if (!searchQuery) {
      setProducts([]);
      setCurrentQuery("");
      return;
    }

    // Set a delay for the debounce
    const handler = setTimeout(() => {
      if (searchQuery !== currentQuery) {
        setCurrentQuery(searchQuery);
      }
    }, 500);

    // Clean up the timeout if the query changes before delay completes
    return () => clearTimeout(handler);
  }, [searchQuery]);

  // Effect for handling actual API calls
  useEffect(() => {
    if (!currentQuery) return;

    // If the query changed, reset page to 1 and treat as new search
    const isNewSearch = page === 1;
    handleProductSearch(isNewSearch);
  }, [currentQuery, page]);

  return (
    <div className="rounded-lg p-6 h-full">
      <form
        className="w-[80vw] mx-auto flex items-center gap-2 bg-white border border-neutral-300 rounded-full px-4 py-2 hover:shadow-lg"
        onSubmit={(e) => {
          e.preventDefault();
          if (searchQuery.trim()) {
            setCurrentQuery(searchQuery.trim());
          }
        }}
      >
        <input
          type="text"
          placeholder="Search for products..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="flex-1 px-4 py-2 bg-transparent focus:outline-none text-lg"
        />
        <button
          type="submit"
          className="flex items-center justify-center h-[40px] w-[40px] bg-theme-accent-primary text-white rounded-full shadow-md hover:bg-theme-accent-hover transition-colors duration-200"
        >
          <Search className="h-5 w-5" strokeWidth={2.5} />
        </button>
      </form>

      <div className="mt-4">
        {searchQuery.trim() !== "" && (
          <ProductInfiniteScrolling
            products={products}
            isLoading={loading}
            observerTarget={observerTarget}
          />
        )}
      </div>
    </div>
  );
};
